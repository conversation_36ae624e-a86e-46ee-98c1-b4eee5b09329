# OpenAPI接口contractExpirationTime完整场景-测试用例

## 接口测试

### 一步发起签署接口

#### TL-一步发起签署接口业务模板配置关闭场景验证

##### PD-前置条件：具有API调用权限；业务模板配置项关闭；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：contractExpirationTime设置为空进行发起

##### 步骤三：contractExpirationTime设置为"2024-12-31 23:59:59"进行发起

##### 步骤四：contractExpirationTime设置为"2023-01-01 00:00:00"进行发起

##### 步骤五：调用签署详情接口查看contractExpirationTime字段

##### 步骤六：调用列表查询接口查看contractExpirationTime字段

##### 步骤七：检查签署环节开启回调中的contractExpirationTime字段

##### ER-预期结果：1：所有情况都能发起成功；2：contractExpirationTime不管设置什么值都不生效；3：发起成功后无合同到期时间；4：详情接口返回contractExpirationTime为空；5：列表查询返回contractExpirationTime为空；6：签署环节开启回调中contractExpirationTime为空；

#### TL-一步发起签署接口发起时指定配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：contractExpirationTime设置为空进行发起

##### 步骤三：contractExpirationTime设置为"2024-12-31 23:59:59"进行发起

##### 步骤四：调用列表查询接口查看contractExpirationTime

##### 步骤五：调用详情接口查看contractExpirationTime

##### 步骤六：检查签署环节开启回调中的contractExpirationTime

##### ER-预期结果：1：空值发起成功；2：未来时间发起成功；3：列表查询显示接口传入的值；4：详情接口返回接口传入的值；5：签署环节开启回调中显示接口传入的值；

#### TL-一步发起签署接口发起签署后固定时长配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起签署后固定时长"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：contractExpirationTime设置为空进行发起

##### 步骤三：contractExpirationTime设置为"2024-12-31 23:59:59"进行发起

##### 步骤四：contractExpirationTime设置为"2023-01-01 00:00:00"进行发起

##### 步骤五：调用列表查询接口查看contractExpirationTime

##### 步骤六：调用详情接口查看contractExpirationTime

##### 步骤七：检查签署环节开启回调中的contractExpirationTime

##### ER-预期结果：1：所有情况都能发起成功；2：不管contractExpirationTime设置什么值都能发起成功；3：列表查询中的contractExpirationTime根据业务模板规则生成；4：详情接口返回根据业务模板规则生成的值；5：签署环节回调中的contractExpirationTime根据业务模板规则生成；6：与传入的值无关；

#### TL-一步发起签署接口签署完成后固定时长配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"签署完成后固定时长"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：contractExpirationTime设置为空进行发起

##### 步骤三：contractExpirationTime设置为"2024-12-31 23:59:59"进行发起

##### 步骤四：完成签署流程

##### 步骤五：调用列表查询接口查看contractExpirationTime

##### 步骤六：调用详情接口查看contractExpirationTime

##### 步骤七：检查签署流程完成回调中的contractExpirationTime

##### ER-预期结果：1：所有情况都能发起成功；2：签署完成后自动计算contractExpirationTime；3：列表查询显示根据业务模板规则生成的值；4：详情接口返回根据业务模板规则生成的值；5：签署流程完成回调中显示根据业务模板规则生成的值；6：与传入的contractExpirationTime值无关；

#### TL-一步发起签署接口无需设置配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"无需设置"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：contractExpirationTime设置为空进行发起

##### 步骤三：contractExpirationTime设置为"2024-12-31 23:59:59"进行发起

##### 步骤四：调用列表查询接口查看contractExpirationTime

##### 步骤五：调用详情接口查看contractExpirationTime

##### 步骤六：检查签署环节开启回调中的contractExpirationTime

##### ER-预期结果：1：所有情况都能发起成功；2：列表查询中contractExpirationTime为空；3：详情接口返回contractExpirationTime为空；4：签署环节开启回调中contractExpirationTime为空；5：传入值不生效；

### 使用业务模板发起签署接口

#### TL-使用业务模板发起签署接口发起时指定配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createByBizTemplate接口

##### 步骤二：contractExpirationTime设置为空进行发起

##### 步骤三：contractExpirationTime设置为"2024-12-31 23:59:59"进行发起

##### 步骤四：检查创建结果和返回的contractExpirationTime

##### ER-预期结果：1：空值发起成功；2：未来时间发起成功；3：创建的流程包含正确的contractExpirationTime；4：规则同一步发起接口；

#### TL-使用业务模板发起签署接口固定时长配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起签署后固定时长"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createByBizTemplate接口

##### 步骤二：contractExpirationTime设置为空进行发起

##### 步骤三：contractExpirationTime设置为"2024-12-31 23:59:59"进行发起

##### 步骤四：检查生成的contractExpirationTime

##### ER-预期结果：1：所有情况都能发起成功；2：contractExpirationTime根据业务模板规则生成；3：传入值不生效；

### 创建签署流程接口

#### TL-创建签署流程接口发起时指定配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/create接口

##### 步骤二：contractExpirationTime设置为空进行创建

##### 步骤三：contractExpirationTime设置为"2024-12-31 23:59:59"进行创建

##### 步骤四：检查创建结果

##### ER-预期结果：1：空值创建成功；2：未来时间创建成功；3：创建的流程包含正确的contractExpirationTime；4：规则同一步发起接口；

#### TL-创建签署流程接口固定时长配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"签署完成后固定时长"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/create接口

##### 步骤二：contractExpirationTime设置为空进行创建

##### 步骤三：contractExpirationTime设置为"2024-12-31 23:59:59"进行创建

##### 步骤四：检查生成的contractExpirationTime

##### ER-预期结果：1：所有情况都能创建成功；2：contractExpirationTime根据业务模板规则生成；3：传入值不生效；

### 签署详情接口

#### TL-签署详情接口返回contractExpirationTime字段验证

##### PD-前置条件：签署流程已创建；设置了合同到期时间；

##### 步骤一：调用/esign-signs/v1/signFlow/signDetail接口

##### 步骤二：传入有效的流程ID

##### 步骤三：检查返回结果中的contractExpirationTime字段

##### 步骤四：对比设置的到期时间和返回的到期时间

##### ER-预期结果：1：返回结果包含contractExpirationTime字段；2：字段值与设置的到期时间一致；3：时间格式为"yyyy-MM-dd hh:mm:ss"；4：未设置到期时间时字段为空；

#### TL-签署详情接口不同业务模板规则返回验证

##### PD-前置条件：存在不同业务模板规则的签署流程；

##### 步骤一：查询"发起时指定"规则的流程详情

##### 步骤二：查询"发起签署后固定时长"规则的流程详情

##### 步骤三：查询"签署完成后固定时长"规则的流程详情

##### 步骤四：查询"无需设置"规则的流程详情

##### ER-预期结果：1：发起时指定返回接口传入的contractExpirationTime；2：发起签署后固定时长返回根据业务模板规则生成的contractExpirationTime；3：签署完成后固定时长返回根据业务模板规则生成的contractExpirationTime；4：无需设置返回空值；

### 获取签署流程列表接口

#### TL-获取签署流程列表接口时间区间查询验证

##### PD-前置条件：存在多个签署流程；部分设置了合同到期时间；

##### 步骤一：调用/esign-signs/v1/signFlow/list接口

##### 步骤二：传入contractExpirationTimeStartTime和contractExpirationTimeEndTime参数

##### 步骤三：验证时间区间查询功能

##### 步骤四：检查返回结果中的contractExpirationTime字段

##### ER-预期结果：1：支持按contractExpirationTime区间查询；2：查询结果准确；3：返回结果包含contractExpirationTime字段；4：分页功能正常；

#### TL-获取签署流程列表接口返回字段验证

##### PD-前置条件：存在多个签署流程；部分设置了合同到期时间；

##### 步骤一：调用列表接口查询所有流程

##### 步骤二：检查返回结果中每个流程的contractExpirationTime字段

##### 步骤三：对比详情接口返回的到期时间

##### ER-预期结果：1：列表接口返回结果包含contractExpirationTime字段；2：设置了到期时间的流程正确显示；3：未设置到期时间的流程字段为空；4：与详情接口返回值一致；

### 更新签署流程接口

#### TL-更新签署流程接口配置关闭场景验证

##### PD-前置条件：业务模板配置项关闭；签署中流程已存在；具有API调用权限；

##### 步骤一：调用/esign-docs/v1/signFlow/update接口

##### 步骤二：传入有效的signFlowId和未来的contractExpirationTime

##### 步骤三：检查更新结果

##### 步骤四：等待到期日检查消息通知

##### ER-预期结果：1：更新成功；2：更新后的流程包含新的contractExpirationTime；3：在到期日发送消息通知；

#### TL-更新签署流程接口基本功能验证

##### PD-前置条件：签署流程已存在；具有API调用权限；

##### 步骤一：调用/esign-docs/v1/signFlow/update接口

##### 步骤二：传入有效的signFlowId和未来的contractExpirationTime

##### 步骤三：传入有效的businessNo和未来的contractExpirationTime

##### 步骤四：检查更新结果

##### ER-预期结果：1：使用signFlowId更新成功；2：使用businessNo更新成功；3：更新后的流程包含新的contractExpirationTime；4：时间格式为"yyyy-MM-dd hh:mm:ss"；

### 签署回调事件

#### TL-签署回调事件contractExpirationTime字段验证

##### PD-前置条件：配置了回调地址；签署流程设置了合同到期时间；

##### 步骤一：触发签署环节开启事件

##### 步骤二：触发签署方签署完成事件

##### 步骤三：触发签署流程完成事件

##### 步骤四：检查回调数据中的contractExpirationTime字段

##### ER-预期结果：1：签署环节开启回调包含contractExpirationTime字段；2：签署方签署完成回调包含contractExpirationTime字段；3：签署流程完成回调包含contractExpirationTime字段；4：字段值正确；

#### TL-签署回调事件所有事件类型验证

##### PD-前置条件：配置了回调地址；签署流程设置了合同到期时间；

##### 步骤一：触发签署流程过期事件

##### 步骤二：触发签署截止前事件

##### 步骤三：触发签署方拒签事件

##### 步骤四：触发签署流程作废事件

##### 步骤五：触发签署失败事件

##### 步骤六：触发签署方转交事件

##### ER-预期结果：1：所有回调事件都包含contractExpirationTime字段；2：字段值与流程设置的到期时间一致；3：回调数据格式正确；4：时间格式为"yyyy-MM-dd hh:mm:ss"；

#### TL-签署回调事件未设置到期时间验证

##### PD-前置条件：配置了回调地址；签署流程未设置合同到期时间；

##### 步骤一：触发各种签署回调事件

##### 步骤二：检查回调数据中的contractExpirationTime字段

##### ER-预期结果：1：回调数据包含contractExpirationTime字段；2：字段值为空或null；3：不影响其他回调数据；

## 接口场景测试

### 场景一：业务模板配置关闭场景

#### TL-业务模板配置关闭完整流程验证

##### PD-前置条件：业务模板配置项关闭；具有API调用权限；

##### 步骤一：通过openapi接口发起签署流程，contractExpirationTime设置为"2024-12-31 23:59:59"

##### 步骤二：调用列表查询接口查看contractExpirationTime

##### 步骤三：调用详情接口查看contractExpirationTime

##### 步骤四：检查签署环节开启回调中的contractExpirationTime

##### 步骤五：流程进入签署中状态，使用更新签署流程接口设置contractExpirationTime

##### 步骤六：等待到期日检查消息通知

##### ER-预期结果：1：发起成功，contractExpirationTime不管设置什么值都不生效；2：列表查询返回contractExpirationTime为空；3：详情接口返回contractExpirationTime为空；4：签署环节开启回调中contractExpirationTime为空；5：更新接口设置contractExpirationTime成功；6：在到期日发送消息通知；

### 场景二：发起时指定配置场景

#### TL-发起时指定配置合同到期前签署完成验证

##### PD-前置条件：业务模板配置为"发起时指定"；具有API调用权限；

##### 步骤一：通过openapi接口发起签署流程

##### 步骤二：contractExpirationTime设置为"2024-12-31 23:59:59"

##### 步骤三：在合同到期之前完成签署

##### 步骤四：调用列表查询接口查看contractExpirationTime

##### 步骤五：调用详情接口查看contractExpirationTime

##### 步骤六：检查签署环节开启回调中的contractExpirationTime

##### 步骤七：等待合同到期日检查通知

##### ER-预期结果：1：发起成功；2：设置的contractExpirationTime生效；3：签署完成成功；4：列表查询显示接口传入的值；5：详情接口返回接口传入的值；6：签署环节开启回调中显示接口传入的值；7：在合同到期日发送通知；

#### TL-发起时指定配置合同到期后签署完成验证

##### PD-前置条件：业务模板配置为"发起时指定"；具有API调用权限；

##### 步骤一：通过openapi接口发起签署流程

##### 步骤二：contractExpirationTime设置为较近的未来时间

##### 步骤三：在合同到期日之后完成签署

##### 步骤四：调用列表查询接口查看contractExpirationTime

##### 步骤五：调用详情接口查看contractExpirationTime

##### 步骤六：检查签署流程完成回调中的contractExpirationTime

##### 步骤七：等待签署完成后次日检查通知

##### ER-预期结果：1：发起成功；2：设置的contractExpirationTime生效；3：到期后仍可签署完成；4：列表查询显示接口传入的值；5：详情接口返回接口传入的值；6：签署流程完成回调中显示接口传入的值；7：在签署完成时间点+1天发送通知；

#### TL-发起时指定配置参数为空验证

##### PD-前置条件：业务模板配置为"发起时指定"；具有API调用权限；

##### 步骤一：通过openapi接口发起签署流程

##### 步骤二：contractExpirationTime设置为空

##### 步骤三：调用列表查询接口查看contractExpirationTime

##### 步骤四：调用详情接口查看contractExpirationTime

##### 步骤五：检查签署环节开启回调中的contractExpirationTime

##### ER-预期结果：1：发起成功；2：列表查询显示接口传入的值（空）；3：详情接口返回接口传入的值（空）；4：签署环节开启回调中显示接口传入的值（空）；5：不发送到期提醒；

### 场景三：发起签署后固定时长配置场景

#### TL-发起签署后固定时长配置参数无关性验证

##### PD-前置条件：业务模板配置为"发起签署后固定时长"；具有API调用权限；

##### 步骤一：通过openapi接口发起签署流程

##### 步骤二：contractExpirationTime设置为空

##### 步骤三：再次发起，contractExpirationTime设置为"2024-12-31 23:59:59"

##### 步骤四：再次发起，contractExpirationTime设置为"2023-01-01 00:00:00"

##### 步骤五：调用列表查询接口查看contractExpirationTime

##### 步骤六：调用详情接口查看contractExpirationTime

##### 步骤七：检查签署环节回调中的contractExpirationTime

##### ER-预期结果：1：所有情况都能发起成功；2：不管contractExpirationTime设置什么值都能发起成功；3：列表查询中的contractExpirationTime根据业务模板规则生成；4：详情接口返回根据业务模板规则生成的值；5：签署环节回调中的contractExpirationTime根据业务模板规则生成；6：与传入的值无关；

### 场景四：或签节点场景

#### TL-或签节点一方已签署无需签署方不通知验证

##### PD-前置条件：设置了或签节点；配置了到期提醒；具有API调用权限；

##### 步骤一：通过openapi接口发起包含或签节点的签署流程

##### 步骤二：设置contractExpirationTime为"2024-12-31 23:59:59"

##### 步骤三：完成或签节点中一方的签署

##### 步骤四：等待到期提醒时间

##### 步骤五：检查通知发送对象

##### ER-预期结果：1：发起成功并设置contractExpirationTime；2：或签节点一方签署完成；3：另一方无需签署；4：无需签署的签署人不接收到期通知；5：其他相关人员正常接收通知；

#### TL-或签节点多方签署场景验证

##### PD-前置条件：设置了或签节点；配置了到期提醒；具有API调用权限；

##### 步骤一：通过openapi接口发起包含或签节点的签署流程

##### 步骤二：设置contractExpirationTime为"2024-12-31 23:59:59"

##### 步骤三：或签节点中多方都进行签署

##### 步骤四：等待到期提醒时间

##### 步骤五：检查通知发送对象

##### ER-预期结果：1：发起成功并设置contractExpirationTime；2：或签节点多方都可签署；3：所有签署方都接收到期通知；4：通知内容正确；

### 场景五：多方签场景

#### TL-顺序签多方签场景验证

##### PD-前置条件：设置了顺序签；配置了到期提醒；具有API调用权限；

##### 步骤一：通过openapi接口发起顺序签署流程

##### 步骤二：设置contractExpirationTime为"2024-12-31 23:59:59"

##### 步骤三：按顺序完成多方签署

##### 步骤四：等待到期提醒时间

##### 步骤五：检查通知发送对象

##### ER-预期结果：1：发起成功并设置contractExpirationTime；2：顺序签署流程正常；3：所有签署方都接收到期通知；4：发起人和抄送人也接收通知；

#### TL-无序签多方签场景验证

##### PD-前置条件：设置了无序签；配置了到期提醒；具有API调用权限；

##### 步骤一：通过openapi接口发起无序签署流程

##### 步骤二：设置contractExpirationTime为"2024-12-31 23:59:59"

##### 步骤三：无序完成多方签署

##### 步骤四：等待到期提醒时间

##### 步骤五：检查通知发送对象

##### ER-预期结果：1：发起成功并设置contractExpirationTime；2：无序签署流程正常；3：所有签署方都接收到期通知；4：通知发送逻辑正确；

#### TL-多人签场景同时通知验证

##### PD-前置条件：设置了多人签；配置了到期提醒；具有API调用权限；

##### 步骤一：通过openapi接口发起多人签署流程

##### 步骤二：设置contractExpirationTime为"2024-12-31 23:59:59"

##### 步骤三：完成多人签署

##### 步骤四：等待到期提醒时间

##### 步骤五：检查通知发送对象和时间

##### ER-预期结果：1：发起成功并设置contractExpirationTime；2：多人签署流程正常；3：同一个流程多人签，合同到期多人同时通知；4：通知内容包含正确的流程信息；

### 场景六：不同业务模板分开提醒场景

#### TL-不同业务模板同一用户分开提醒验证

##### PD-前置条件：存在多个不同的业务模板；配置了不同的提醒时间；具有API调用权限；

##### 步骤一：通过业务模板A发起签署流程给用户X

##### 步骤二：通过业务模板B发起签署流程给用户X

##### 步骤三：设置两个流程的contractExpirationTime为相同时间

##### 步骤四：等待到期提醒时间

##### 步骤五：检查用户X接收到的通知情况

##### ER-预期结果：1：两个流程都发起成功；2：contractExpirationTime设置为相同时间；3：提醒时间不同；4：需分开提醒；5：用户X收到两次独立的通知；

#### TL-不同业务模板不同提醒时间验证

##### PD-前置条件：存在多个不同的业务模板；配置了不同的提醒规则；具有API调用权限；

##### 步骤一：业务模板A设置提醒时间为10:00

##### 步骤二：业务模板B设置提醒时间为14:00

##### 步骤三：通过两个模板发起流程给同一用户

##### 步骤四：设置相同的contractExpirationTime

##### 步骤五：分别在10:00和14:00检查通知发送

##### ER-预期结果：1：两个流程都发起成功；2：10:00发送业务模板A相关的通知；3：14:00发送业务模板B相关的通知；4：通知内容分别对应不同的业务模板；

### 场景七：同一业务模板合并发送场景

#### TL-同一业务模板多条记录合并发送验证

##### PD-前置条件：同一个业务模板；配置了到期提醒；具有API调用权限；

##### 步骤一：通过同一个业务模板给用户X发起多条签署流程

##### 步骤二：设置所有流程的contractExpirationTime为相同时间

##### 步骤三：等待到期提醒时间

##### 步骤四：检查用户X接收到的通知情况

##### ER-预期结果：1：多条流程都发起成功；2：contractExpirationTime设置为相同时间；3：为了节流，多条流程合并在一起发送；4：用户X收到一次合并的通知；5：通知内容包含所有相关流程信息；

#### TL-同一业务模板不同到期时间分开发送验证

##### PD-前置条件：同一个业务模板；配置了到期提醒；具有API调用权限；

##### 步骤一：通过同一个业务模板给用户X发起多条签署流程

##### 步骤二：设置不同的contractExpirationTime

##### 步骤三：等待不同的到期提醒时间

##### 步骤四：检查用户X接收到的通知情况

##### ER-预期结果：1：多条流程都发起成功；2：contractExpirationTime设置为不同时间；3：按不同的到期时间分别发送通知；4：用户X收到多次通知；5：每次通知对应相应到期时间的流程；

#### TL-同一业务模板部分相同到期时间验证

##### PD-前置条件：同一个业务模板；配置了到期提醒；具有API调用权限；

##### 步骤一：通过同一个业务模板给用户X发起5条签署流程

##### 步骤二：其中3条设置contractExpirationTime为"2024-12-31 23:59:59"

##### 步骤三：其中2条设置contractExpirationTime为"2024-12-30 23:59:59"

##### 步骤四：等待不同的到期提醒时间

##### 步骤五：检查用户X接收到的通知情况

##### ER-预期结果：1：5条流程都发起成功；2：2024-12-30到期的2条流程合并发送一次通知；3：2024-12-31到期的3条流程合并发送一次通知；4：用户X总共收到2次通知；5：每次通知包含对应到期时间的所有流程；

## 异常测试

### 接口参数异常

#### TL-contractExpirationTime参数格式异常处理验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；

##### 步骤一：传入错误格式"2024/12/31 23:59:59"的contractExpirationTime参数

##### 步骤二：传入只有日期"2024-12-31"的contractExpirationTime参数

##### 步骤三：传入ISO格式"2024-12-31T23:59:59"的contractExpirationTime参数

##### 步骤四：传入非法字符"abc-def-ghi hh:mm:ss"的contractExpirationTime参数

##### 步骤五：传入超长字符串的contractExpirationTime参数

##### ER-预期结果：1：错误格式返回格式错误提示；2：只有日期格式返回格式错误；3：ISO格式返回格式错误；4：非法字符返回格式错误；5：超长字符串返回参数长度错误；

#### TL-接口并发调用异常处理验证

##### PD-前置条件：具有API调用权限；接口服务正常；

##### 步骤一：同时发起多个创建签署流程请求

##### 步骤二：同时发起多个更新签署流程请求

##### 步骤三：监控系统性能和响应

##### ER-预期结果：1：并发请求正常处理；2：系统性能稳定；3：数据一致性得到保障；4：无数据竞争问题；

### 业务逻辑异常

#### TL-业务模板配置变更后接口调用验证

##### PD-前置条件：具有API调用权限；业务模板已配置；

##### 步骤一：使用"发起时指定"配置的模板发起流程

##### 步骤二：修改业务模板配置为"无需设置"

##### 步骤三：再次使用该模板发起流程

##### 步骤四：检查两次发起的结果差异

##### ER-预期结果：1：第一次发起按"发起时指定"规则处理；2：第二次发起按"无需设置"规则处理；3：配置变更实时生效；4：历史流程不受影响；

#### TL-消息合并发送异常处理验证

##### PD-前置条件：同一业务模板；多条流程相同到期时间；消息服务异常；

##### 步骤一：发起多条流程设置相同contractExpirationTime

##### 步骤二：模拟消息服务异常

##### 步骤三：等待到期提醒时间

##### 步骤四：检查系统处理机制

##### ER-预期结果：1：系统记录发送失败日志；2：具备重试机制；3：不影响其他正常流程的提醒；4：异常恢复后能补发通知；

## 性能测试

### 接口性能

#### TL-大量流程创建接口性能验证

##### PD-前置条件：具有API调用权限；系统资源充足；

##### 步骤一：连续调用1000次创建签署流程接口

##### 步骤二：每次都传入contractExpirationTime参数

##### 步骤三：监控接口响应时间和系统资源

##### ER-预期结果：1：所有接口调用成功；2：平均响应时间在合理范围内；3：系统资源使用正常；4：无内存泄漏问题；

#### TL-消息合并发送性能验证

##### PD-前置条件：同一业务模板；大量流程相同到期时间；

##### 步骤一：创建1000个流程设置相同contractExpirationTime

##### 步骤二：等待到期提醒时间

##### 步骤三：监控消息合并处理性能

##### 步骤四：检查通知发送效率

##### ER-预期结果：1：消息合并处理及时；2：系统资源使用正常；3：通知发送成功率高；4：合并逻辑性能良好；

#### TL-多方签场景性能验证

##### PD-前置条件：具有API调用权限；配置了多方签场景；

##### 步骤一：创建包含大量签署方的流程

##### 步骤二：设置contractExpirationTime

##### 步骤三：完成签署流程

##### 步骤四：等待到期提醒时间并监控通知发送性能

##### ER-预期结果：1：大量签署方流程创建成功；2：签署流程正常完成；3：到期提醒发送及时；4：系统性能稳定；

## 兼容性测试

### 接口版本兼容性

#### TL-接口版本向下兼容性验证

##### PD-前置条件：具有API调用权限；存在旧版本客户端；

##### 步骤一：使用旧版本客户端调用新增了contractExpirationTime参数的接口

##### 步骤二：检查接口返回结果

##### 步骤三：验证旧版本客户端的正常功能

##### ER-预期结果：1：旧版本客户端调用成功；2：新增字段不影响旧版本功能；3：向下兼容性良好；

#### TL-签署场景兼容性验证

##### PD-前置条件：具有API调用权限；不同签署场景配置；

##### 步骤一：在或签、顺序签、无序签场景下设置contractExpirationTime

##### 步骤二：检查各场景的兼容性

##### 步骤三：验证到期提醒功能

##### ER-预期结果：1：所有签署场景都兼容contractExpirationTime设置；2：到期提醒功能正常；3：场景切换不影响功能；

#### TL-消息合并机制兼容性验证

##### PD-前置条件：不同版本的业务模板；配置了到期提醒；

##### 步骤一：使用新版本业务模板发起流程

##### 步骤二：使用旧版本业务模板发起流程

##### 步骤三：检查消息合并发送机制

##### ER-预期结果：1：新旧版本业务模板都正常；2：消息合并机制兼容不同版本；3：通知发送逻辑正确；

## 安全测试

### 接口安全

#### TL-接口权限控制验证

##### PD-前置条件：不同权限级别的用户；

##### 步骤一：无权限用户调用创建签署流程接口

##### 步骤二：无权限用户调用更新签署流程接口

##### 步骤三：无权限用户调用查询接口

##### ER-预期结果：1：无权限用户无法调用创建接口；2：无权限用户无法调用更新接口；3：查询接口按权限返回数据；4：权限控制严格；

#### TL-contractExpirationTime数据安全验证

##### PD-前置条件：具有API调用权限；配置了HTTPS环境；

##### 步骤一：通过HTTPS调用接口传输contractExpirationTime

##### 步骤二：检查数据传输过程

##### 步骤三：验证数据存储安全

##### ER-预期结果：1：数据传输使用HTTPS加密；2：敏感数据不泄露；3：数据存储安全；4：日志记录合规；

#### TL-消息合并发送安全验证

##### PD-前置条件：多个用户；同一业务模板；相同到期时间；

##### 步骤一：为不同用户发起流程设置相同contractExpirationTime

##### 步骤二：等待到期提醒时间

##### 步骤三：检查消息发送的用户隔离

##### ER-预期结果：1：不同用户的流程信息不会混淆；2：消息发送严格按用户隔离；3：用户隐私得到保护；4：数据安全性良好；

## 五、测试用例优化

**优化维度检查：**
1. 完整性检查：覆盖了所有OpenAPI接口、业务模板4种规则、7个接口场景、多种签署场景、消息合并机制
2. 准确性检查：接口参数验证准确，业务规则验证明确，签署场景处理正确，消息合并逻辑清晰
3. 可执行性检查：前置条件明确，API调用步骤清晰，验证点具体
4. 可维护性检查：用例独立性好，参数设置清晰，结果验证明确

**补充遗漏场景：**

### 边界场景

#### TL-contractExpirationTime时间边界值处理验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；

##### 步骤一：设置contractExpirationTime为当前时间

##### 步骤二：设置contractExpirationTime为当前时间+1秒

##### 步骤三：设置contractExpirationTime为当前时间-1秒

##### ER-预期结果：1：当前时间设置成功；2：当前时间+1秒设置成功；3：当前时间-1秒设置失败；

#### TL-消息合并发送边界值验证

##### PD-前置条件：同一业务模板；配置了到期提醒；

##### 步骤一：发起1条流程设置contractExpirationTime

##### 步骤二：发起100条流程设置相同contractExpirationTime

##### 步骤三：发起1000条流程设置相同contractExpirationTime

##### 步骤四：等待到期提醒时间检查合并效果

##### ER-预期结果：1：1条流程正常发送；2：100条流程合并发送；3：1000条流程合并发送；4：合并机制在不同数量下都正常工作；

## 六、测试用例评审及补充

**评审结果：**
1. 需求覆盖度：98%
2. 场景完整性：95%
3. 步骤合理性：95%
4. 结果可验证性：95%
5. 数据充分性：92%

**遗漏场景：无重大遗漏，已补充时间边界值和消息合并边界值场景**

## 七、冒烟测试用例提取

### MYTL-一步发起签署接口contractExpirationTime基本功能冒烟验证

#### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；

#### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

#### 步骤二：传入有效的contractExpirationTime参数

#### 步骤三：检查创建结果

#### ER-预期结果：1：接口调用成功；2：contractExpirationTime参数接受正确；3：流程创建成功并包含到期时间；

### MYTL-业务模板规则交互冒烟验证

#### PD-前置条件：具有API调用权限；不同配置的业务模板；

#### 步骤一：使用"发起时指定"模板发起流程

#### 步骤二：使用"发起签署后固定时长"模板发起流程

#### 步骤三：检查两种情况的contractExpirationTime处理

#### ER-预期结果：1：发起时指定模式contractExpirationTime生效并显示传入值；2：固定时长模式contractExpirationTime不生效按模板规则生成；3：业务规则正确执行；

### MYTL-签署详情接口contractExpirationTime返回冒烟验证

#### PD-前置条件：签署流程已创建；设置了合同到期时间；

#### 步骤一：调用签署详情接口

#### 步骤二：检查返回的contractExpirationTime字段

#### ER-预期结果：1：接口调用成功；2：返回正确的contractExpirationTime；3：时间格式正确；

### MYTL-更新签署流程接口contractExpirationTime冒烟验证

#### PD-前置条件：签署流程已存在；具有API调用权限；

#### 步骤一：调用更新签署流程接口

#### 步骤二：传入有效的contractExpirationTime

#### 步骤三：检查更新结果

#### ER-预期结果：1：接口调用成功；2：contractExpirationTime更新成功；3：更新后数据正确；

### MYTL-签署回调事件contractExpirationTime冒烟验证

#### PD-前置条件：配置了回调地址；签署流程设置了合同到期时间；

#### 步骤一：触发签署流程完成事件

#### 步骤二：检查回调数据

#### ER-预期结果：1：回调正常触发；2：包含contractExpirationTime字段；3：字段值正确；

### MYTL-接口场景一冒烟验证

#### PD-前置条件：业务模板配置项关闭；具有API调用权限；

#### 步骤一：通过API发起签署流程

#### 步骤二：使用更新接口设置contractExpirationTime

#### 步骤三：检查设置结果

#### ER-预期结果：1：发起成功但无contractExpirationTime；2：更新接口设置成功；3：设置后有contractExpirationTime；

### MYTL-消息合并发送冒烟验证

#### PD-前置条件：同一业务模板；配置了到期提醒；

#### 步骤一：发起3条流程设置相同contractExpirationTime

#### 步骤二：等待到期提醒时间

#### 步骤三：检查通知发送情况

#### ER-预期结果：1：3条流程都发起成功；2：收到一次合并通知；3：通知包含所有流程信息；

## 八、线上验证用例提取

### PATL-OpenAPI接口contractExpirationTime完整流程线上验证

#### PD-前置条件：生产环境API权限；真实业务数据；

#### 步骤一：调用创建签署流程API设置contractExpirationTime

#### 步骤二：调用签署详情API查看contractExpirationTime

#### 步骤三：调用更新流程API修改contractExpirationTime

#### 步骤四：调用列表API按contractExpirationTime区间查询

#### ER-预期结果：1：所有API调用成功；2：contractExpirationTime设置和查询正确；3：更新功能正常；4：查询功能正常；

### PATL-业务模板规则线上验证

#### PD-前置条件：生产环境；不同配置的业务模板；

#### 步骤一：使用"发起时指定"模板通过API发起

#### 步骤二：使用"固定时长"模板通过API发起

#### 步骤三：检查生成的contractExpirationTime

#### ER-预期结果：1：发起时指定规则contractExpirationTime生效并显示传入值；2：固定时长规则按模板计算；3：业务逻辑正确；

### PATL-签署回调事件contractExpirationTime线上验证

#### PD-前置条件：生产环境；配置了回调地址；

#### 步骤一：创建包含contractExpirationTime的签署流程

#### 步骤二：完成签署触发回调事件

#### 步骤三：检查回调数据中的contractExpirationTime字段

#### ER-预期结果：1：回调事件正常触发；2：包含contractExpirationTime字段；3：字段值准确；

### PATL-接口场景线上验证

#### PD-前置条件：生产环境；真实业务场景；

#### 步骤一：执行业务模板配置关闭场景

#### 步骤二：执行发起时指定配置场景

#### 步骤三：执行固定时长配置场景

#### ER-预期结果：1：配置关闭场景处理正确；2：发起时指定场景contractExpirationTime生效并显示传入值；3：固定时长场景按规则计算；

### PATL-多方签场景contractExpirationTime线上验证

#### PD-前置条件：生产环境；多方签署场景；

#### 步骤一：创建或签、顺序签、无序签流程

#### 步骤二：设置contractExpirationTime

#### 步骤三：完成签署并等待到期提醒

#### ER-预期结果：1：各种签署场景都正常；2：contractExpirationTime设置成功；3：到期提醒发送正确；4：或签场景无需签署方不收到通知；5：多人签场景同时通知；

### PATL-消息合并发送机制线上验证

#### PD-前置条件：生产环境；同一业务模板；多条流程；

#### 步骤一：通过同一业务模板发起多条流程

#### 步骤二：设置相同的contractExpirationTime

#### 步骤三：等待到期提醒时间

#### 步骤四：检查消息合并发送效果

#### ER-预期结果：1：多条流程都发起成功；2：相同到期时间的流程合并发送；3：不同到期时间的流程分开发送；4：消息合并机制正常工作；

### PATL-不同业务模板分开提醒线上验证

#### PD-前置条件：生产环境；不同业务模板；相同用户；

#### 步骤一：通过不同业务模板给同一用户发起流程

#### 步骤二：设置相同的contractExpirationTime

#### 步骤三：等待到期提醒时间

#### 步骤四：检查分开提醒效果

#### ER-预期结果：1：不同业务模板流程都发起成功；2：提醒时间不同需分开提醒；3：用户收到多次独立通知；4：分开提醒机制正常工作；

### PATL-系统性能线上验证

#### PD-前置条件：生产环境；大量真实数据；

#### 步骤一：批量调用创建接口设置contractExpirationTime

#### 步骤二：调用列表查询接口按contractExpirationTime查询

#### 步骤三：监控接口性能和回调处理

#### 步骤四：检查消息合并发送性能

#### ER-预期结果：1：接口响应时间合理；2：系统资源使用正常；3：回调处理及时；4：消息合并处理高效；5：不影响其他业务；
