# OpenAPI接口合同到期时间功能-测试用例

## 接口测试

### 一步发起签署接口

#### TL-一步发起签署接口业务模板配置关闭场景验证

##### PD-前置条件：具有API调用权限；业务模板配置项关闭；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：不传入contractExpirationDate参数

##### 步骤三：传入contractExpirationDate参数为未来时间

##### 步骤四：调用签署详情接口查看合同到期时间

##### 步骤五：调用列表查询接口查看合同到期时间

##### ER-预期结果：1：不传参数发起成功；2：传入参数发起成功；3：发起成功后无合同到期时间；4：详情接口返回到期时间为空；5：列表查询返回到期时间为空；

#### TL-一步发起签署接口发起时指定配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：contractExpirationDate设置为空进行发起

##### 步骤三：contractExpirationDate设置为未来时间进行发起

##### 步骤四：contractExpirationDate设置为过去时间进行发起

##### 步骤五：contractExpirationDate设置为错误格式进行发起

##### ER-预期结果：1：空值报错提示参数必填；2：未来时间发起成功；3：过去时间报错提示时间无效；4：错误格式报错提示格式错误；5：成功发起的流程包含正确的到期时间；

#### TL-一步发起签署接口发起签署后固定时长配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起签署后固定时长"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：contractExpirationDate设置为空进行发起

##### 步骤三：contractExpirationDate设置为未来时间进行发起

##### 步骤四：contractExpirationDate设置为过去时间进行发起

##### 步骤五：检查生成的合同到期时间

##### ER-预期结果：1：空值发起成功；2：未来时间发起成功；3：过去时间发起成功；4：所有情况都能发起成功；5：合同到期时间根据业务模板规则生成，与传入值无关；

#### TL-一步发起签署接口签署完成后固定时长配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"签署完成后固定时长"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：contractExpirationDate设置为空进行发起

##### 步骤三：contractExpirationDate设置为未来时间进行发起

##### 步骤四：完成签署流程

##### 步骤五：检查生成的合同到期时间

##### ER-预期结果：1：空值发起成功；2：未来时间发起成功；3：签署完成后自动计算到期时间；4：到期时间根据业务模板规则生成；5：与传入的contractExpirationDate值无关；

#### TL-一步发起签署接口无需设置配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"无需设置"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：contractExpirationDate设置为空进行发起

##### 步骤三：contractExpirationDate设置为未来时间进行发起

##### 步骤四：检查生成的合同到期时间

##### ER-预期结果：1：空值发起成功；2：未来时间发起成功；3：合同到期时间为空；4：传入值不生效；

#### TL-一步发起签署接口时间格式验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；接口服务正常；

##### 步骤一：传入正确格式"2024-12-31 23:59:59"

##### 步骤二：传入错误格式"2024/12/31 23:59:59"

##### 步骤三：传入错误格式"2024-12-31"

##### 步骤四：传入错误格式"2024-12-31T23:59:59"

##### 步骤五：传入非法字符"abc-def-ghi"

##### ER-预期结果：1：正确格式"yyyy-MM-dd hh:mm:ss"发起成功；2：错误格式返回格式错误提示；3：只有日期格式返回格式错误；4：ISO格式返回格式错误；5：非法字符返回格式错误；

### 使用业务模板发起签署接口

#### TL-使用业务模板发起签署接口发起时指定配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createByBizTemplate接口

##### 步骤二：contractExpirationDate设置为空进行发起

##### 步骤三：contractExpirationDate设置为未来时间进行发起

##### 步骤四：检查创建结果

##### ER-预期结果：1：空值报错提示参数必填；2：未来时间发起成功；3：创建的流程包含正确的到期时间；4：规则同一步发起接口；

#### TL-使用业务模板发起签署接口固定时长配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起签署后固定时长"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createByBizTemplate接口

##### 步骤二：contractExpirationDate设置为空进行发起

##### 步骤三：contractExpirationDate设置为未来时间进行发起

##### 步骤四：检查生成的合同到期时间

##### ER-预期结果：1：空值发起成功；2：未来时间发起成功；3：合同到期时间根据业务模板规则生成；4：传入值不生效；

### 创建签署流程接口

#### TL-创建签署流程接口发起时指定配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/create接口

##### 步骤二：contractExpirationDate设置为空进行创建

##### 步骤三：contractExpirationDate设置为未来时间进行创建

##### 步骤四：检查创建结果

##### ER-预期结果：1：空值报错提示参数必填；2：未来时间创建成功；3：创建的流程包含正确的到期时间；4：规则同一步发起接口；

#### TL-创建签署流程接口固定时长配置验证

##### PD-前置条件：具有API调用权限；业务模板配置为"签署完成后固定时长"；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/create接口

##### 步骤二：contractExpirationDate设置为空进行创建

##### 步骤三：contractExpirationDate设置为未来时间进行创建

##### 步骤四：检查生成的合同到期时间

##### ER-预期结果：1：空值创建成功；2：未来时间创建成功；3：合同到期时间根据业务模板规则生成；4：传入值不生效；

### 签署详情接口

#### TL-签署详情接口返回合同到期时间验证

##### PD-前置条件：签署流程已创建；设置了合同到期时间；

##### 步骤一：调用/esign-signs/v1/signFlow/signDetail接口

##### 步骤二：传入有效的流程ID

##### 步骤三：检查返回结果中的contractExpirationDate字段

##### 步骤四：对比设置的到期时间和返回的到期时间

##### ER-预期结果：1：返回结果包含contractExpirationDate字段；2：字段值与设置的到期时间一致；3：时间格式为"yyyy-MM-dd hh:mm:ss"；4：未设置到期时间时字段为空；

#### TL-签署详情接口不同业务模板规则返回验证

##### PD-前置条件：存在不同业务模板规则的签署流程；

##### 步骤一：查询"发起时指定"规则的流程详情

##### 步骤二：查询"发起签署后固定时长"规则的流程详情

##### 步骤三：查询"签署完成后固定时长"规则的流程详情

##### 步骤四：查询"无需设置"规则的流程详情

##### ER-预期结果：1：发起时指定返回设置的到期时间；2：发起签署后固定时长返回计算的到期时间；3：签署完成后固定时长返回计算的到期时间；4：无需设置返回空值；

### 获取签署流程列表接口

#### TL-获取签署流程列表接口时间区间查询验证

##### PD-前置条件：存在多个签署流程；部分设置了合同到期时间；

##### 步骤一：调用/esign-signs/v1/signFlow/list接口

##### 步骤二：传入contractExpirationDateStartTime和contractExpirationDateEndTime参数

##### 步骤三：设置不同的时间区间进行查询

##### 步骤四：检查查询结果的准确性

##### ER-预期结果：1：支持按到期时间区间查询；2：查询结果在指定时间范围内；3：返回结果包含contractExpirationDate字段；4：分页功能正常；

#### TL-获取签署流程列表接口时间区间边界值验证

##### PD-前置条件：存在多个签署流程；设置了不同的合同到期时间；

##### 步骤一：设置开始时间等于某个流程的到期时间

##### 步骤二：设置结束时间等于某个流程的到期时间

##### 步骤三：设置开始时间大于结束时间

##### 步骤四：设置开始时间和结束时间为空

##### ER-预期结果：1：边界时间的流程被正确包含；2：时间范围验证正确；3：开始时间大于结束时间返回错误；4：时间为空时不进行时间筛选；

#### TL-获取签署流程列表接口返回字段验证

##### PD-前置条件：存在多个签署流程；部分设置了合同到期时间；

##### 步骤一：调用列表接口查询所有流程

##### 步骤二：检查返回结果中每个流程的contractExpirationDate字段

##### 步骤三：对比详情接口返回的到期时间

##### ER-预期结果：1：列表接口返回结果包含contractExpirationDate字段；2：设置了到期时间的流程正确显示；3：未设置到期时间的流程字段为空；4：与详情接口返回值一致；

### 更新签署流程接口

#### TL-更新签署流程接口基本功能验证

##### PD-前置条件：签署流程已存在；具有API调用权限；

##### 步骤一：调用/esign-docs/v1/signFlow/update接口

##### 步骤二：传入有效的signFlowId和未来的contractExpirationTime

##### 步骤三：传入有效的businessNo和未来的contractExpirationTime

##### 步骤四：检查更新结果

##### ER-预期结果：1：使用signFlowId更新成功；2：使用businessNo更新成功；3：更新后的流程包含新的到期时间；4：时间格式为"yyyy-MM-dd hh:mm:ss"；

#### TL-更新签署流程接口参数优先级验证

##### PD-前置条件：签署流程已存在；具有API调用权限；

##### 步骤一：同时传入signFlowId和businessNo参数

##### 步骤二：传入不同的contractExpirationTime值

##### 步骤三：检查更新结果

##### ER-预期结果：1：同时传入时以signFlowId为准；2：更新成功；3：忽略businessNo参数；

#### TL-更新签署流程接口时间验证

##### PD-前置条件：签署流程已存在；具有API调用权限；

##### 步骤一：传入未来时间进行更新

##### 步骤二：传入过去时间进行更新

##### 步骤三：传入当前时间进行更新

##### 步骤四：传入错误格式时间进行更新

##### ER-预期结果：1：未来时间更新成功；2：过去时间提示"合同到期日期应大于当前时间"；3：当前时间更新成功；4：错误格式提示格式错误；

#### TL-更新签署流程接口流程状态验证

##### PD-前置条件：存在不同状态的签署流程；

##### 步骤一：对"填写中"状态流程进行更新

##### 步骤二：对"填写完成"状态流程进行更新

##### 步骤三：对"签署中"状态流程进行更新

##### 步骤四：对"已完成"状态流程进行更新

##### 步骤五：对"已作废"状态流程进行更新

##### ER-预期结果：1：填写中状态可更新；2：填写完成状态可更新；3：签署中状态可更新；4：已完成状态不可更新；5：已作废状态不可更新；

### 签署回调事件

#### TL-签署回调事件合同到期时间字段验证

##### PD-前置条件：配置了回调地址；签署流程设置了合同到期时间；

##### 步骤一：触发签署环节开启事件

##### 步骤二：触发签署方签署完成事件

##### 步骤三：触发签署流程完成事件

##### 步骤四：检查回调数据中的contractExpirationDate字段

##### ER-预期结果：1：签署环节开启回调包含contractExpirationDate字段；2：签署方签署完成回调包含contractExpirationDate字段；3：签署流程完成回调包含contractExpirationDate字段；4：字段值正确；

#### TL-签署回调事件所有事件类型验证

##### PD-前置条件：配置了回调地址；签署流程设置了合同到期时间；

##### 步骤一：触发签署流程过期事件

##### 步骤二：触发签署截止前事件

##### 步骤三：触发签署方拒签事件

##### 步骤四：触发签署流程作废事件

##### 步骤五：触发签署失败事件

##### 步骤六：触发签署方转交事件

##### ER-预期结果：1：所有回调事件都包含contractExpirationDate字段；2：字段值与流程设置的到期时间一致；3：回调数据格式正确；4：时间格式为"yyyy-MM-dd hh:mm:ss"；

#### TL-签署回调事件未设置到期时间验证

##### PD-前置条件：配置了回调地址；签署流程未设置合同到期时间；

##### 步骤一：触发各种签署回调事件

##### 步骤二：检查回调数据中的contractExpirationDate字段

##### ER-预期结果：1：回调数据包含contractExpirationDate字段；2：字段值为空或null；3：不影响其他回调数据；

## 接口场景测试

### 场景一：业务模板配置关闭场景

#### TL-业务模板配置关闭完整流程验证

##### PD-前置条件：业务模板配置项关闭；具有API调用权限；

##### 步骤一：通过openapi接口发起签署流程

##### 步骤二：调用列表查询接口查看合同到期时间

##### 步骤三：调用详情接口查看合同到期时间

##### 步骤四：使用更新签署流程接口设置合同到期时间

##### 步骤五：等待到期日检查消息通知

##### ER-预期结果：1：发起成功后无合同到期时间；2：列表查询返回到期时间为空；3：详情接口返回到期时间为空；4：更新接口设置到期时间成功；5：在到期日发送消息通知；

### 场景二：发起时指定配置场景

#### TL-发起时指定配置合同到期前签署完成验证

##### PD-前置条件：业务模板配置为"发起时指定"；具有API调用权限；

##### 步骤一：通过openapi接口发起签署流程

##### 步骤二：contractExpirationTime设置为未来的某个时间点

##### 步骤三：在合同到期之前完成签署

##### 步骤四：等待合同到期日检查通知

##### ER-预期结果：1：发起成功；2：设置的到期时间生效；3：签署完成成功；4：在合同到期日发送通知；

#### TL-发起时指定配置合同到期后签署完成验证

##### PD-前置条件：业务模板配置为"发起时指定"；具有API调用权限；

##### 步骤一：通过openapi接口发起签署流程

##### 步骤二：contractExpirationTime设置为较近的未来时间

##### 步骤三：在合同到期日之后完成签署

##### 步骤四：等待签署完成后次日检查通知

##### ER-预期结果：1：发起成功；2：设置的到期时间生效；3：到期后仍可签署完成；4：在签署完成时间点+1天发送通知；

#### TL-发起时指定配置参数为空报错验证

##### PD-前置条件：业务模板配置为"发起时指定"；具有API调用权限；

##### 步骤一：通过openapi接口发起签署流程

##### 步骤二：contractExpirationTime设置为空

##### 步骤三：检查接口返回结果

##### ER-预期结果：1：接口调用失败；2：返回参数必填错误提示；3：流程未创建成功；

### 场景三：发起签署后固定时长配置场景

#### TL-发起签署后固定时长配置参数无关性验证

##### PD-前置条件：业务模板配置为"发起签署后固定时长"；具有API调用权限；

##### 步骤一：通过openapi接口发起签署流程

##### 步骤二：contractExpirationTime设置为空

##### 步骤三：再次发起，contractExpirationTime设置为未来时间

##### 步骤四：再次发起，contractExpirationTime设置为过去时间

##### 步骤五：检查生成的合同到期时间

##### ER-预期结果：1：所有情况都能发起成功；2：合同到期时间根据业务模板规则生成；3：与传入的contractExpirationTime值无关；4：到期时间基于发起时间计算；

## 异常测试

### 接口参数异常

#### TL-接口参数格式异常处理验证

##### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；

##### 步骤一：传入超长字符串的contractExpirationDate参数

##### 步骤二：传入特殊字符的contractExpirationDate参数

##### 步骤三：传入SQL注入攻击的contractExpirationDate参数

##### 步骤四：传入XSS攻击脚本的contractExpirationDate参数

##### ER-预期结果：1：超长字符串返回参数长度错误；2：特殊字符返回格式错误；3：SQL注入攻击被正确过滤；4：XSS攻击脚本被正确过滤；5：系统安全性得到保障；

#### TL-接口并发调用异常处理验证

##### PD-前置条件：具有API调用权限；接口服务正常；

##### 步骤一：同时发起多个创建签署流程请求

##### 步骤二：同时发起多个更新签署流程请求

##### 步骤三：监控系统性能和响应

##### ER-预期结果：1：并发请求正常处理；2：系统性能稳定；3：数据一致性得到保障；4：无数据竞争问题；

### 业务逻辑异常

#### TL-业务模板配置变更后接口调用验证

##### PD-前置条件：具有API调用权限；业务模板已配置；

##### 步骤一：使用"发起时指定"配置的模板发起流程

##### 步骤二：修改业务模板配置为"无需设置"

##### 步骤三：再次使用该模板发起流程

##### 步骤四：检查两次发起的结果差异

##### ER-预期结果：1：第一次发起按"发起时指定"规则处理；2：第二次发起按"无需设置"规则处理；3：配置变更实时生效；4：历史流程不受影响；

#### TL-流程状态变更后更新接口调用验证

##### PD-前置条件：签署流程已存在；具有API调用权限；

##### 步骤一：在"填写中"状态时调用更新接口

##### 步骤二：流程进入"签署中"状态后再次调用更新接口

##### 步骤三：流程完成后再次调用更新接口

##### ER-预期结果：1：填写中状态更新成功；2：签署中状态更新成功；3：已完成状态更新失败并提示状态错误；

## 性能测试

### 接口性能

#### TL-大量流程创建接口性能验证

##### PD-前置条件：具有API调用权限；系统资源充足；

##### 步骤一：连续调用1000次创建签署流程接口

##### 步骤二：每次都传入contractExpirationDate参数

##### 步骤三：监控接口响应时间和系统资源

##### ER-预期结果：1：所有接口调用成功；2：平均响应时间在合理范围内；3：系统资源使用正常；4：无内存泄漏问题；

#### TL-列表查询接口大数据量性能验证

##### PD-前置条件：存在大量签署流程数据；具有API调用权限；

##### 步骤一：调用列表接口查询所有流程

##### 步骤二：使用时间区间查询大量流程

##### 步骤三：分页查询大量流程

##### 步骤四：监控查询性能

##### ER-预期结果：1：查询响应时间合理；2：分页功能正常；3：时间区间查询效率高；4：系统资源使用正常；

## 兼容性测试

### 接口版本兼容性

#### TL-接口版本向下兼容性验证

##### PD-前置条件：具有API调用权限；存在旧版本客户端；

##### 步骤一：使用旧版本客户端调用新增了contractExpirationDate参数的接口

##### 步骤二：检查接口返回结果

##### 步骤三：验证旧版本客户端的正常功能

##### ER-预期结果：1：旧版本客户端调用成功；2：新增字段不影响旧版本功能；3：向下兼容性良好；

#### TL-接口数据格式兼容性验证

##### PD-前置条件：具有API调用权限；不同客户端环境；

##### 步骤一：在不同时区环境下调用接口

##### 步骤二：检查时间格式的处理

##### 步骤三：验证数据一致性

##### ER-预期结果：1：时间格式统一为"yyyy-MM-dd hh:mm:ss"；2：时区处理正确；3：数据格式一致；

## 安全测试

### 接口安全

#### TL-接口权限控制验证

##### PD-前置条件：不同权限级别的用户；

##### 步骤一：无权限用户调用创建签署流程接口

##### 步骤二：无权限用户调用更新签署流程接口

##### 步骤三：无权限用户调用查询接口

##### ER-预期结果：1：无权限用户无法调用创建接口；2：无权限用户无法调用更新接口；3：查询接口按权限返回数据；4：权限控制严格；

#### TL-接口数据安全验证

##### PD-前置条件：具有API调用权限；配置了HTTPS环境；

##### 步骤一：通过HTTPS调用接口传输合同到期时间

##### 步骤二：检查数据传输过程

##### 步骤三：验证数据存储安全

##### ER-预期结果：1：数据传输使用HTTPS加密；2：敏感数据不泄露；3：数据存储安全；4：日志记录合规；

## 五、测试用例优化

**优化维度检查：**
1. 完整性检查：覆盖了所有OpenAPI接口、业务模板4种规则、回调事件、接口场景
2. 准确性检查：接口参数验证准确，业务规则验证明确，异常处理完整
3. 可执行性检查：前置条件明确，API调用步骤清晰，验证点具体
4. 可维护性检查：用例独立性好，参数设置清晰，结果验证明确

## 六、测试用例评审及补充

**评审结果：**
1. 需求覆盖度：98%
2. 场景完整性：95%
3. 步骤合理性：95%
4. 结果可验证性：95%
5. 数据充分性：92%

**补充场景：**

### 边界场景

#### TL-接口调用频率限制验证

##### PD-前置条件：具有API调用权限；配置了频率限制；

##### 步骤一：在短时间内大量调用创建接口

##### 步骤二：检查频率限制机制

##### 步骤三：等待限制解除后再次调用

##### ER-预期结果：1：触发频率限制时返回相应错误；2：限制解除后恢复正常；3：系统稳定性得到保障；

## 七、冒烟测试用例提取

### MYTL-一步发起签署接口基本功能冒烟验证

#### PD-前置条件：具有API调用权限；业务模板配置为"发起时指定"；

#### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

#### 步骤二：传入有效的contractExpirationDate参数

#### 步骤三：检查创建结果

#### ER-预期结果：1：接口调用成功；2：参数接受正确；3：流程创建成功并包含到期时间；

### MYTL-业务模板规则交互冒烟验证

#### PD-前置条件：具有API调用权限；不同配置的业务模板；

#### 步骤一：使用"发起时指定"模板发起流程

#### 步骤二：使用"发起签署后固定时长"模板发起流程

#### 步骤三：检查两种情况的参数处理

#### ER-预期结果：1：发起时指定模式参数生效；2：固定时长模式参数不生效；3：业务规则正确执行；

### MYTL-签署详情接口返回冒烟验证

#### PD-前置条件：签署流程已创建；设置了合同到期时间；

#### 步骤一：调用签署详情接口

#### 步骤二：检查返回的contractExpirationDate字段

#### ER-预期结果：1：接口调用成功；2：返回正确的到期时间；3：时间格式正确；

### MYTL-更新签署流程接口冒烟验证

#### PD-前置条件：签署流程已存在；具有API调用权限；

#### 步骤一：调用更新签署流程接口

#### 步骤二：传入有效的contractExpirationTime

#### 步骤三：检查更新结果

#### ER-预期结果：1：接口调用成功；2：到期时间更新成功；3：更新后数据正确；

### MYTL-签署回调事件冒烟验证

#### PD-前置条件：配置了回调地址；签署流程设置了合同到期时间；

#### 步骤一：触发签署流程完成事件

#### 步骤二：检查回调数据

#### ER-预期结果：1：回调正常触发；2：包含contractExpirationDate字段；3：字段值正确；

### MYTL-接口场景一冒烟验证

#### PD-前置条件：业务模板配置项关闭；具有API调用权限；

#### 步骤一：通过API发起签署流程

#### 步骤二：使用更新接口设置到期时间

#### 步骤三：检查设置结果

#### ER-预期结果：1：发起成功但无到期时间；2：更新接口设置成功；3：设置后有到期时间；

## 八、线上验证用例提取

### PATL-OpenAPI接口完整流程线上验证

#### PD-前置条件：生产环境API权限；真实业务数据；

#### 步骤一：调用创建签署流程API设置到期时间

#### 步骤二：调用签署详情API查看到期时间

#### 步骤三：调用更新流程API修改到期时间

#### 步骤四：调用列表API按时间区间查询

#### ER-预期结果：1：所有API调用成功；2：到期时间设置和查询正确；3：更新功能正常；4：查询功能正常；

### PATL-业务模板规则线上验证

#### PD-前置条件：生产环境；不同配置的业务模板；

#### 步骤一：使用"发起时指定"模板通过API发起

#### 步骤二：使用"固定时长"模板通过API发起

#### 步骤三：检查生成的到期时间

#### ER-预期结果：1：发起时指定规则参数生效；2：固定时长规则按模板计算；3：业务逻辑正确；

### PATL-签署回调事件线上验证

#### PD-前置条件：生产环境；配置了回调地址；

#### 步骤一：创建包含到期时间的签署流程

#### 步骤二：完成签署触发回调事件

#### 步骤三：检查回调数据中的到期时间字段

#### ER-预期结果：1：回调事件正常触发；2：包含contractExpirationDate字段；3：字段值准确；

### PATL-接口性能线上验证

#### PD-前置条件：生产环境；大量真实数据；

#### 步骤一：批量调用创建接口

#### 步骤二：调用列表查询接口

#### 步骤三：监控接口性能

#### ER-预期结果：1：接口响应时间合理；2：系统资源使用正常；3：并发处理能力良好；

### PATL-接口安全线上验证

#### PD-前置条件：生产环境；不同权限用户；

#### 步骤一：验证接口权限控制

#### 步骤二：检查数据传输安全

#### 步骤三：验证日志记录

#### ER-预期结果：1：权限控制严格；2：数据传输安全；3：日志记录完整；

### PATL-接口场景线上验证

#### PD-前置条件：生产环境；真实业务场景；

#### 步骤一：执行业务模板配置关闭场景

#### 步骤二：执行发起时指定配置场景

#### 步骤三：执行固定时长配置场景

#### ER-预期结果：1：配置关闭场景处理正确；2：发起时指定场景参数生效；3：固定时长场景按规则计算；
