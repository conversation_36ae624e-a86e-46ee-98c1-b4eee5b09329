# -*- coding: utf-8 -*-
# @Description :
# @Time : 2023/2/20 17:05
# <AUTHOR> <PERSON><PERSON><PERSON>
# @File : apiLogin.py
from httpRequest.api.apiHttpBin import *
from httpRequest.schemas.loginSchema import AccountLoginIn, LoginIn, SendDynamicCodeIn, ToLoginIn


class ApiLogin(ApiHttpBin):

    def apiAccountLogin(self, header, accountLoginIn: AccountLoginIn):
        """
        统一门户内部用户登录api
        :param header:
        :param accountLoginIn: 请求体
        :return: ApiLogin
        """
        self.method = EnumMethod.POST
        self.path = '/sso/accountLogin'
        self.setHeaders(header)
        self.json = accountLoginIn.dict()
        return self

    def apiLogin(self, header, loginIn: LoginIn):
        """
        外部用户登录api
        :param header:
        :param loginIn: 请求体
        :return: ApiLogin
        """
        self.method = EnumMethod.POST
        self.path = '/sso/login'
        self.setHeaders(header)
        self.json = loginIn.dict()
        return self

    def apiToLogin(self, header, toLoginIn: ToLoginIn):
        """
        管理平台登录api
        :param header:
        :param toLoginIn: 请求体
        :return: ApiLogin
        """
        self.method = EnumMethod.POST
        self.path = '/manage/login/toLogin'
        self.setHeaders(header)
        self.json = toLoginIn.dict()
        return self

    def apiValidateCaptcha(self, header, toLoginIn: ToLoginIn):
        """
        管理平台，校验验证码api
        :param header:
        :param toLoginIn: 请求体
        :return: ApiLogin
        """
        self.method = EnumMethod.POST
        self.path = '/manage/login/validateCaptcha'
        self.setHeaders(header)
        self.json = toLoginIn.dict()
        return self

    def apiSendVerifyCode(self):
        """
        统一门户登录，获取图形验证码api
        """
        self.method = EnumMethod.GET
        self.path = '/sso/sendVerifyCode'
        return self

    def apiSendDynamicCode(self, header, sendDynamicCodeIn: SendDynamicCodeIn):
        """
        外部用户登录，发送短信验证码api
        :param header:
        :param sendDynamicCodeIn: 请求体
        """
        self.method = EnumMethod.POST
        self.setHeaders(header)
        self.path = '/sso/sendDynamicCode'
        self.json = sendDynamicCodeIn.dict()
        return self

    def apiVerifyCode(self):
        """
        管理后台登录，获取图形验证码api
        """
        self.method = EnumMethod.GET
        self.path = '/manage/login/verifyCode'
        return self

    def apiGetTokenByCode(self, tCode):
        """
        置换管理后台token api
        :param tCode:
        """
        self.method = EnumMethod.POST
        self.path = '/manage/anon/getTokenByCode'
        self.json = {
            "authentication": True,
            "domain": "admin_platform",
            "tCode": tCode
        }
        return self
