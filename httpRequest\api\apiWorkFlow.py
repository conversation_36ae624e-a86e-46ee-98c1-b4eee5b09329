
from httpRequest.api.apiHttpBin import *
from httpRequest.schemas.workFlowSchema import FlowWithdrawIn, FlowTerminateIn, FlowCompleteIn, EpeiusRedirectIn


class ApiWorkFlow(ApiHttpBin):

    def apiEpeiusLog(self, process_id):
        """
        获取流程日志
        param process_id: 流程id
        """

        self.method = EnumMethod.GET
        self.path = "/epeius/api/v1/process/log"
        self.params = {
            "processInstanceId": process_id
        }
        return self

    def apiEpeiusWithdraw(self, flowWithdrawIn: FlowWithdrawIn):
        """
        审核流程撤回
        :param flowWithdrawIn:
        """
        self.method = EnumMethod.POST
        self.path = "/epeius/api/v1/flow/withdraw"
        self.json = flowWithdrawIn.dict()
        return self

    def apiEpeiusTerminate(self, flowTerminateIn: FlowTerminateIn):
        """
        审核拒绝
        :param flowTerminateIn:
        """
        self.method = EnumMethod.POST
        self.path = "/epeius/api/v1/flow/terminate"
        self.json = flowTerminateIn.dict()
        return self

    def apiEpeiusComplete(self, flowCompleteIn: FlowCompleteIn):
        """
        审核同意
        :param flowCompleteIn: obj
        """
        self.method = EnumMethod.POST
        self.path = "/epeius/api/v1/complete"
        self.json = flowCompleteIn.dict()
        return self

    def apiEpeiusRedirect(self, epeiusRedirectIn: EpeiusRedirectIn):
        """
        任务转交
        """
        self.method = EnumMethod.POST
        self.path = "/epeius/api/v1/redirect"
        self.json = epeiusRedirectIn.dict()
        return self

    def apiGetOneProcessInstance(self, processInstanceId):
        """
        查询流程
        :param processInstanceId:
        :return:
        """
        self.method = EnumMethod.POST
        self.path = "/epeius/api/v1/flow/getOneProcessInstance"
        self.params = {
            "processInstanceId": processInstanceId
        }
        return self

    def apiFlowTaskId(self, processInstanceId):
        """
        根据实例id获取最新任务id
        :param processInstanceId:
        :return:
        """
        self.method = EnumMethod.GET
        self.path = "/epeius/api/v2/flowTaskId"
        self.params = {
            "processInstanceId": processInstanceId
        }
        return self

    def apiGetOneTaskNew(self, taskId):
        """
        获取任务详情(新)
        :param taskId:
        :return:
        """
        self.method = EnumMethod.POST
        self.path = "/epeius/api/v1/getOneTaskNew"
        self.json = {
            "taskId": taskId,
            "loginUser": ""
        }
        return self

    def apiGetOneTask(self, taskId):
        """
        获取任务详情
        :param taskId:
        :return:
        """
        self.method = EnumMethod.GET
        self.path = "/epeius/api/v1/getOneTask"
        self.params = {
            "taskId": taskId
        }
        return self


if __name__ == "__main__":
    default = {
        "code": "flowable_base_query",
        "pageSize": 10,
        'processVariables': '',
        "paramMap": {
            "sortType": "DESC",
            "sortKey": "startTime",
            'processStatus': ''
        }
    }
    search = {
        "code": "flowable_task_query",
        "currentPage": 0,
        "pageSize": 20,
        "paramMap": {
            "sortType": "DESC",
            "sortKey": "createTime",
            'startAccountId': ''
        }
    }
    default.update(search)
    print(default)

    pass
