# -*- coding: utf-8 -*- 
# @Description : 
# @Time : 2023/2/28 17:01 
# <AUTHOR> zhuqi 
# @File : testController.py
import os
import sys

from httprunner.api import HttpRunner

from httpRequest.service.loginService import LoginService
from utils import log

project_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_dir)

MAIN_HOST = 'http://tianyin6-stable.tsign.cn'
ACCOUNT = 'ceswdzxzdhyhwgd1'
PASSWORD = 'GRhRZt7/VnwctRieXXcEnyIRc8s0a/O3GkaWmwEl7mt1QorsBbzoGcnvxpCKzsjhCGsaYqklLDyDQZSNP1PZTjk5DKAXagA/bP6W4gywTsWKVidyXoi+uP0BeDPfJ/HndqRBDhKrMFZPNdUxICs0O9VrqiA1gMisAKhabNj+xq2ILpLzs27XGsMKyaBOyR7F1ZSH1vkUYUhqVWfJK9+sUD3tMwnyPLwhhhFL2WlckEpbXGpclHLobPpw8Zw7bWD2rNKaWr/5JeIQRw5GkpuqTwvKCroxY3aoFzXDWuBJn4Hn8t+0Y90IV/6Bjms1XAGDKaFE+WRt0XOKwYCVSQkV3g=='


def demoController():
    projectId = '10000'
    loginService = LoginService(MAIN_HOST)
    token = loginService.createToken(projectId, ACCOUNT, PASSWORD)
    log.info('获取token %s', token)


def hrunTest():
    """
    hrun调试
    :return:
    """
    ENV = '.env_64'
    log.info('project_dir %s', project_dir)

    casePath = project_dir + '/' + 'tests/testcases/process/refuseFlow/refuseExceptSceneTC.yml'
    dotEnvPath = project_dir + '/tests/' + ENV

    HttpRunner().run(casePath, dot_env_path=dotEnvPath)


if __name__ == "__main__":
    hrunTest()
