# -*- coding: utf-8 -*- 
# @Description : 
# @Time : 2022/3/23 5:34 下午 
# <AUTHOR> zhuqi 
# @File : database.py
from sqlalchemy import create_engine

from sqlalchemy.orm import sessionmaker, Session


class MySqlTestEnv:
    HOST = 'rm-bp12ztip62mt13hu9.mysql.rds.aliyuncs.com'
    PORT = 3306
    USER = 'base_billing'
    PASSWORD = 'Base_Billing#123456#'
    DBNAME = 'base_billing'
    CHARSET = 'utf8'


class MySqlSmlEnv:
    HOST = 'rm-bp121416726ndl22k.mysql.rds.aliyuncs.com'
    PORT = 3306
    USER = 'billing'
    PASSWORD = 'billing#123456#'
    DBNAME = 'billing'
    CHARSET = 'utf8'


class MySqlEngine:
    def __init__(self, dbName, envType='test'):
        self.dbName = dbName
        self.env = MySqlTestEnv
        if envType.lower() in ['sml', 'simulation']:
            self.env = MySqlSmlEnv

    @property
    def engine(self):
        return create_engine(
            f"mysql+pymysql://{self.env.USER}:{self.env.PASSWORD}@{self.env.HOST}/{self.dbName}?"
            f"charset=utf8mb4", echo=True, pool_pre_ping=True, pool_recycle=3600)

    @property
    def sessionLocal(self):
        return sessionmaker(bind=self.engine, autocommit=False, autoflush=False)

    @property
    def getDb(self):
        db = self.sessionLocal()
        try:
            yield db
        finally:
            db.close()


class CrudBase:
    def __init__(self, dbName, envType='test'):
        self.db: Session = MySqlEngine(dbName, envType).sessionLocal()

    def _rollback(self):
        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            raise Exception("数据库操作失败: %s" % str(e))

    def _queryFilterOne(self, models, filters):
        result = self.db.query(models).filter(filters).first()
        if result:
            return result
        raise Exception("未查到数据 %s: %s" % (models, filters))

    def _queryFilterAll(self, models, filters):
        result = self.db.query(models).filter(filters).all()
        if result:
            return result
        raise Exception("未查到数据 %s: %s" % (models, filters))

    def _queryFilterByAsc(self, models, orderBy, filters):
        result = self.db.query(models).filter(filters).order_by(orderBy.asc()).all()
        if result:
            return result
        raise Exception("未查到数据 %s: %s" % (models, filters))

    def _deleteBy_(self, models, filters):
        self.db.query(models).filter(filters).delete()
        self._rollback()
