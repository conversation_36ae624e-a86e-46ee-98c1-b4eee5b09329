# -*- coding: utf-8 -*- 
# @Description : 
# @Time : 2022/12/14 14:45 
# <AUTHOR> <PERSON><PERSON><PERSON> 
# @File : epeiusResp.py
import jmespath

from crm.Utils.zhuqi.httpRequest.api.apiWorkFlow import ApiWorkFlow
from crm.Utils.zhuqi.httpRequest.schemas.workFlowSchema import FlowWithdrawIn, FlowTerminateIn, FlowCompleteIn, \
    EpeiusRedirectIn


class EpeiusResp:
    def __init__(self, apiWorkFlowObj: ApiWorkFlow):
        self.apiWorkFlowObj = apiWorkFlowObj

    # def esSearchProcess(self, searchConditions=None, processId=None):
    #     """
    #     es搜索接口，返回搜索结果
    #     :param searchConditions: 查询条件
    #     :param processId: 查询条件
    #     :return:
    #     """
    #     resp = self.apiWorkFlowObj.apiEsSearch(searchConditions, processId).run()
    #     searchData = jmespath.search("data.datas[0]", resp)
    #     return searchData

    def getOneProcessInstance(self, processInstanceId):
        """
        查询流程
        :param processInstanceId:
        :return:
        """
        response = self.apiWorkFlowObj.apiGetOneProcessInstance(processInstanceId).run()
        processInfo = jmespath.search('data', response)
        if processInfo:
            return processInfo
        raise Exception("当前环境流程不存在，%s" % processInstanceId)

    def flowTaskId(self, processInstanceId):
        """
        根据实例id获取最新任务id
        :param processInstanceId:
        :return:
        """
        response = self.apiWorkFlowObj.apiFlowTaskId(processInstanceId).run()
        taskId = jmespath.search('data', response)
        return taskId

    def getOneTaskNew(self, taskId):
        """
        获取任务详情(新)
        :param taskId:
        :return:
        """
        response = self.apiWorkFlowObj.apiGetOneTaskNew(taskId).run()
        taskInfo = jmespath.search('data', response)
        return taskInfo

    def getOneTask(self, taskId):
        """
        获取任务详情
        :param taskId:
        :return:
        """
        response = self.apiWorkFlowObj.apiGetOneTask(taskId).run()
        taskInfo = jmespath.search('data', response)
        return taskInfo

    def epeiusWithdraw(self, flowWithdrawIn: FlowWithdrawIn):
        """
        撤回流程
        :param flowWithdrawIn:
        :return:
        """
        response = self.apiWorkFlowObj.apiEpeiusWithdraw(flowWithdrawIn).run()
        withdraw = jmespath.search('data', response)
        return withdraw

    def epeiusTerminate(self, flowTerminateIn: FlowTerminateIn):
        """
        终止流程
        :param flowTerminateIn:
        :return:
        """
        response = self.apiWorkFlowObj.apiEpeiusTerminate(flowTerminateIn).run()
        terminate = jmespath.search('data', response)
        return terminate

    def epeiusComplete(self, flowCompleteIn: FlowCompleteIn):
        """
        完成流程
        :param flowCompleteIn:
        :return:
        """
        response = self.apiWorkFlowObj.apiEpeiusComplete(flowCompleteIn).run()
        complete = jmespath.search('data', response)
        return complete

    def epeiusRedirect(self, epeiusRedirectIn: EpeiusRedirectIn):
        """
        完成流程
        :param epeiusRedirectIn:
        :return:
        """
        response = self.apiWorkFlowObj.apiEpeiusRedirect(epeiusRedirectIn).run()
        redirect = jmespath.search('data', response)
        return redirect
