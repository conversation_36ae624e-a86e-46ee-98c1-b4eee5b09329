# -*- coding: utf-8 -*-
# @Description :
# @Time : 2023/2/20 17:21
# <AUTHOR> <PERSON><PERSON><PERSON>
# @File : loginSchema.py

from typing import Optional, Any, Union

from pydantic import BaseModel


class TokenObj(BaseModel):
    """
    token对象
    """
    code: Optional[str]
    token: str
    refreshToken: Optional[str]


class VerificationCodeObj(BaseModel):
    """
    验证码对象
    """
    code: str = '9999'
    headerCode: Optional[str]
    imgCode: Optional[bytes]


class AccountLoginIn(BaseModel):
    """
    统一门户 内部登录api入参
    """
    platform: Optional[str] = 'pc'
    referer: Optional[str] = ''
    type: int = 1
    verificationCode: str
    account: str
    password: str


class LoginIn(BaseModel):
    """
    统一门户 外部用户登录api入参
    """
    verificationCode: str
    dynamicCode: str = '123456'
    accountType: int = 4
    platform: Optional[str] = 'pc'
    referer: Optional[str]
    userCode: Optional[str]
    userTerritory: Optional[str]  # None-内部用户， '2'-外部用户
    account: str


class ToLoginParams(BaseModel):
    """
    管理平台登录api入参的params
    """
    rememberMe: bool = False
    tenantCode: int = 1000
    userCode: str = 'admin'
    verificationCode: str
    vertificationCodeHeader: str
    password: str


class ValidateCaptchaParams(BaseModel):
    """
    管理后台校验验证码api
    """
    tenantCode: int = 1000
    verificationCode: str
    vertificationCodeHeader: str


class ToLoginIn(BaseModel):
    """
    管理平台登录api入参
    """
    domain: str = 'admin_platform'
    params: Union[ToLoginParams, ValidateCaptchaParams]


class SendDynamicCodeIn(BaseModel):
    """
    发送短信验证码api入参
    """
    verificationCode: str
    accountType: int = 4
    platform: Optional[str] = 'pc'
    scene: int = 1  # 1-内部用户，7-外部用户
    userTerritory: Optional[str] = '2'  # None-内部用户， '2'-外部用户
    account: str


class InitHeaderIn(BaseModel):
    contentType: Optional[str] = 'application/json'
    xTimevaleSignature: Optional[str]
    projectId: str
    navId: Optional[str]
    authorization: Optional[str]
    verificationCode: Optional[str]
