# -*- coding: utf-8 -*- 
# @Description : 
# @Time : 2022/12/15 15:11 
# <AUTHOR> <PERSON><PERSON><PERSON> 
# @File : workFlowSchema.py


from typing import Optional, Any

from pydantic import BaseModel


class FlowTerminateIn(BaseModel):
    """
    流程拒绝api入参
    """
    activityId: Optional[str] = 'userTask5'
    assignee: str
    processInstanceId: str
    reason: Optional[str] = "脚本执行--审核拒绝"
    taskId: str


class FlowWithdrawIn(BaseModel):
    """
    流程撤回api入参
    """
    activityId: Optional[str]
    assignee: str
    processInstanceId: str
    reason: Optional[str] = "脚本执行--发起人撤回"
    taskId: Optional[str]


class FlowCompleteIn(BaseModel):
    """
    审批完成api入参
    """
    assignee: str
    processInstanceId: str
    reason: Optional[str] = "脚本执行--审核同意"
    taskId: str
    variables: Optional[dict] = {}


class EpeiusRedirectIn(BaseModel):
    """
    转交api入参
    """
    assignee: str
    processInstanceId: str
    reason: Optional[str] = "脚本转交"
    isLeaveRedirect: Optional[bool] = False
    taskId: str
    taskName: Optional[str]
    toAssignee: str


class WorkFlowIn(BaseModel):
    """
    auditToNode: 审核至某个节点，为空时全部节点审核
    eurekaGroup: 项目标识，有项目标识即为项目环境,不传即为DEFAULT
    processId: 流程id
    auditType: str审核类型，terminate 拒绝，complete 通过，withdraw 为撤回
    envType: 环境类型，test:测试环境，simulation/sml：模拟环境
    """
    processId: str
    envType: Optional[str] = 'test'
    auditType: str
    eurekaGroup: Optional[str] = 'undefined'
    auditToNode: Optional[str] = None


class WorkFlowData(BaseModel):
    processId: Optional[str]
    envType: Optional[str] = 'test'
    eurekaGroup: Optional[str] = 'undefined'
    auditToNode: Optional[str]
    processStatus: Optional[str]


class RedirectData(BaseModel):
    assignee: Optional[str]
    taskName: Optional[str]
    toAssignee: Optional[str]
    taskStatus: Optional[str]


class WorkFlowOut(BaseModel):
    """
    工作流审批返回值
    """
    code: int = 200
    message: str = '成功'
    data: Any = None


class WorkFlowRedirectIn(BaseModel):
    """
    工作流转交
    """
    processId: str
    envType: Optional[str] = 'test'
    eurekaGroup: Optional[str] = None
    toAssignee: str
