# -*- coding: utf-8 -*-
# @Description :
# @Time : 2023/2/27 17:45
# <AUTHOR> z<PERSON><PERSON>
# @File : loginService.py
import base64
from urllib.parse import unquote
import ddddocr
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from httpRequest.api.apiLogin import ApiLogin
from httpRequest.response.loginResp import LoginResp
from httpRequest.schemas.loginSchema import AccountLoginIn, LoginIn, SendDynamicCodeIn, ToLoginParams, ToLoginIn, \
    VerificationCodeObj, TokenObj
from utils import log


class LoginService:
    def __init__(self, baseUrl):
        self._loginResp = LoginResp(ApiLogin(baseUrl))

    def getVerificationCode(self) -> VerificationCodeObj:
        """
        统一门户 获取图形验证码
        :return:
        """
        resp = self._loginResp.sendVerifyCode()
        headerCode = resp.headers.get('verificationCode')
        verificationCodeObj = VerificationCodeObj(headerCode=headerCode,
                                                  imgCode=resp.content)
        return verificationCodeObj

    def managerVerifyCode(self) -> VerificationCodeObj:
        """
        管理后台 获取图形验证码
        :return:
        """
        self._loginResp.verifyCode()  # 避免失败预请求一次
        resp = self._loginResp.verifyCode()
        headerCode = resp.headers.get('verificationCode')

        verificationCodeObj = VerificationCodeObj(headerCode=headerCode,
                                                  imgCode=resp.content)
        try:
            ocr = ddddocr.DdddOcr()
            code = ocr.classification(verificationCodeObj.imgCode)
            verificationCodeObj.code = code
            log.info('解析到图形验证码: %s' % code)
        except Exception as e:
            raise Exception('解析图形验证码失败：%s' % str(e))
        return verificationCodeObj

    def createToken(self, projectId, account, password) -> TokenObj:
        """
        统一门户 内部用户账密获取登录token
        :param projectId:
        :param account:
        :param password:
        :return:
        """
        verificationCodeObj = self.getVerificationCode()
        headers = {'x-timevale-project-id': projectId,
                   'verificationCode': verificationCodeObj.headerCode}

        accountLoginIn = AccountLoginIn(account=account,
                                        password=password,
                                        verificationCode=verificationCodeObj.code)
        accountLoginResp = self._loginResp.accountLogin(headers, accountLoginIn)
        jumpUrl = accountLoginResp.get('jumpUrl')
        jumpUrlStr = unquote(jumpUrl)
        token = accountLoginResp.get('token')
        refreshToken = accountLoginResp.get('refreshToken')
        codeSplit = jumpUrlStr.split('&code=')[1]
        code = codeSplit.split('&refreshToken=')[0]
        tokenObj = TokenObj(token=token,
                            code=code,
                            refreshToken=refreshToken)
        return tokenObj

    def verCodeLogin(self, encryptPhoneOrMail, userTerritory=None):
        """
        外部/内部签署人，通过验证码登录获取token
        :param encryptPhoneOrMail: 手机号或者邮箱 密文
        :param userTerritory: '2'-外部，None-内部
        :return:
        """
        verificationCodeObj = self.getVerificationCode()
        headers = {'verificationCode': verificationCodeObj.headerCode}

        sendDynamicCodeIn = SendDynamicCodeIn(account=encryptPhoneOrMail,
                                              verificationCode=verificationCodeObj.code,
                                              userTerritory=userTerritory)
        if userTerritory:
            sendDynamicCodeIn.scene = 7
        self._loginResp.sendDynamicCode(headers, sendDynamicCodeIn)
        loginIn = LoginIn(account=encryptPhoneOrMail,
                          verificationCode=verificationCodeObj.code,
                          userTerritory=userTerritory)
        loginResp = self._loginResp.login(headers, loginIn)
        token = loginResp.get('token')
        return token

    def getManageToken(self, account, password):
        """
        管理后台登录token
        :param account:
        :param password:
        :return:
        """
        verificationCodeObj = self.managerVerifyCode()

        headers = {'verificationCode': verificationCodeObj.headerCode,
                   'tenantCode': 'vnyqCnpMi3sLP39RVU85ww=='}

        toLoginParams = ToLoginParams(userCode=account,
                                      password=password,
                                      verificationCode=verificationCodeObj.code,
                                      vertificationCodeHeader=verificationCodeObj.headerCode)

        toLoginIn = ToLoginIn(params=toLoginParams)
        token = self._loginResp.toLogin(headers, toLoginIn)
        return token

    def getDynamicCodeService(self, account):
        """
        通过手机或是邮箱登录的时候需要用到的图形验证码 （需要是rsa加密后的值）
        :param account:
        :return:
        """
        verificationCodeObj = self.getVerificationCode()
        headers = {'verificationCode': verificationCodeObj.headerCode}

        sendDynamicCodeIn = SendDynamicCodeIn(account=account,
                                              verificationCode=verificationCodeObj.code)
        res = self._loginResp.sendDynamicCode(headers, sendDynamicCodeIn)
        return res

    def parseTokenByCode(self, aesKey, willingUrl):
        willingUrlStr = unquote(willingUrl)
        tCode = willingUrlStr.split('tCode=')[1]
        encryptedToken = self._loginResp.getTokenByCode(tCode).get('token')
        decryptBytes = base64.b64decode(encryptedToken.encode('utf-8'))
        decryptBytes = base64.b64decode(decryptBytes)
        key = aesKey.encode("utf-8")
        # 创建AES解密器
        cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
        decryptor = cipher.decryptor()
        # 解密数据
        tokenDraft = decryptor.update(decryptBytes) + decryptor.finalize()
        token = tokenDraft.decode('utf-8').split('<!@>')[0]
        log.info('AES解密到token：%s' % token)
        return token