#查询签署回调
variables:
  callbackEventType: "1"  #1签署回调;2配置签署区回调;3用例用印回调；4填写回调;5存证回调;6邀请注册回调
  callbackEventBusinessId: "27bbea12fb7fd55f1671577954cd8cad"

request:
  url: ${ENV(esign.projectHost)}/manage/custom/callback/getCallBackEvent
  method: POST
  headers:
      token: ${getManageToken()}
      X-timevale-project-id: ${ENV(esign.projectId)}
  json:
    {
      "callbackEventType": $callbackEventType,
      "callbackEventBusinessId": "$callbackEventBusinessId"
    }

