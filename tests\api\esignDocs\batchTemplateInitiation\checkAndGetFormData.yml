#批量发起任务
variables:
    json_checkAndGFD:
        includeIds: [ $includeIds_checkAndGFD ]
        query:
            status_: [ "AUDIT_PASS", "UN_AUDIT" ]
        activeStatus: "MANAGE"
        formKey: $formKey_checkAndGFD
        taskKey: ""
        TSIGN_PAGE: "lc_eform_collection_data"
        TSIGN_TAB: "manage_list"
        type: "MANAGE"

request:
    url: ${ENV(esign.projectHost)}/esign-docs/batchTemplateInitiation/checkAndGetFormData
    method: POST
    headers:
        Eformnavid: "lc_eform_collection_data"
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}

    json:
        params:
            $json_checkAndGFD