#获取批量发起数据
variables:
    batchTemplateInitiationUuid:
    flowId:
    createNewDraft: 1
    params:
        batchTemplateInitiationUuid: $batchTemplateInitiationUuid
        flowId: $flowId
        physicalSeal: 0
        createNewDraft: $createNewDraft
request:
    url: ${ENV(esign.projectHost)}/esign-docs/batchTemplateInitiation/getInfo
    method: POST
    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
    json:
        params: $params