#根据流程信息预览发起文件
variables:
  authorization0: ${getPortalToken()}
  batchTemplateInitiationUuid_previewLaunchFile: ""
  excelFileKey_previewLaunchFile: ""
  page_previewLaunchFile: 0
  size_previewLaunchFile: 0

request:
  url: ${ENV(esign.projectHost)}/esign-docs/batchTemplateInitiation/previewLaunchFile
  method: POST
  headers:
    Content-Type: "application/json"
    authorization: $authorization0
    X-timevale-project-id": ${ENV(esign.projectId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid_previewLaunchFile,
      excelFileKey: $excelFileKey_previewLaunchFile,
      page: $page_previewLaunchFile,
      size: $size_previewLaunchFile
    }
    platform:
    tenantCode:
    userCode: