#预览发起文件-查询流程列表
variables:
  authorization0: ${getPortalToken()}
  batchTemplateInitiationUuid_previewLaunchFileFlowList: ""
  excelFileKey_previewLaunchFileFlowList: ""
  page_previewLaunchFileFlowList: 1
  size_previewLaunchFileFlowList: 15

request:
  url: ${ENV(esign.projectHost)}/esign-docs/batchTemplateInitiation/previewLaunchFileFlowList
  method: POST
  headers:
    Content-Type: "application/json"
    authorization: $authorization0
    X-timevale-project-id": ${ENV(esign.projectId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid_previewLaunchFileFlowList,
      excelFileKey: $excelFileKey_previewLaunchFileFlowList,
      page: $page_previewLaunchFileFlowList,
      size: $size_previewLaunchFileFlowList
    }
    platform:
    tenantCode:
    userCode: