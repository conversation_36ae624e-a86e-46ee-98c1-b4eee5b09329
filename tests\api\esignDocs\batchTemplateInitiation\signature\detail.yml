name: 签署区设置页详情接口
variables:
  filePreTaskIdDetail: ""
  batchTemplateInitiationUuid: ""
  signFlowIdDetail: ""

request:
  url: ${ENV(esign.projectHost)}/esign-docs/batchTemplateInitiation/signature/detail
  method: POST
  headers: ${gen_main_headers()}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      batchTemplateInitiationUuid: $batchTemplateInitiationUuid,
      filePreTaskId: $filePreTaskIdDetail,
      signFlowId: $signFlowIdDetail
    }
    platform:
    tenantCode:
    userCode: