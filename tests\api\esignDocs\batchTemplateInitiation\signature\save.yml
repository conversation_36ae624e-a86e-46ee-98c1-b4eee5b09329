#设置签署区
variables:
  batchTemplateInitiationUuid:
  filePreTaskId:
  filePreTaskInfos:
#  filePreTaskInfos:
#      departmentName: $departmentName   #部门名称
#      sealTypeName: $sealTypeName   #印章类型名称
#      organizationName: $organizationName   #组织名称
#      departmentCode: $departmentCode   #部门代码
#      userName: $userName   #用户名称
#      userId: $userId   #用户id
#      userCode: $userCode   #用户Code
#      organizationId: $organizationId   #所属组织ID
#      signerId: $signerId   #关联签署方signerId
#      legalSignFlag: $legalSignFlag   #是否添加法人签署：0否，1是；默认为0
#      organizationCode: $organizationCode   #所属组织code
#      sealTypeCode: $sealTypeCode   #印章类型编码
#      signerType: $signerType   #签署方类型 1个人 2企业
#      userType: $userType   #签署方地域 1内部 2相对方
#      sealInfos: $sealInfos
#  sealInfos:
#      sealIdList: $sealIdList
#          fileKey: $fileKey   #文档fileKey
#          sealId: $sealId   #印章id
#          signatureType: $signatureType   #指定签署印章类型：PERSON-SEAL-个人印章、COMMON-SEAL-企业
#      signConfigs: $signConfigs
#          sealId: $sealId   #指定印章ID
#          keywordInfo:
#            offsetPosX: $offsetPosX   #x轴 偏移量
#            offsetPosY: $offsetPosY   #y轴 偏移量
#            keyword: $keyword   #关键字查询
#          edgeScope: $edgeScope   #骑缝签范围 0全部 1奇数 2偶数 默认0
#          signatureType: $signatureType   #指定签署印章类型 PERSON-SEAL:个人 COMMON-SEAL:机构
#          uuid: $uuid   #uuid
#          posX: $posX   #坐标 x轴
#          posY: $posY   #坐标y轴
#          allowMove: $allowMove   #签署区是否可移动
#          pageNo: $pageNo   #页码 为空表示不限制
#          addSignDate: $addSignDate   #签署区是否添加签署时间 false否true是 默认false
#          signPosName: $signPosName   #签署区名称，signConfigType为2（选择类型），则只需要传signPo
#          signType: $signType   #签署类型 COMMON-SIGN : 普通 EDGE-SIGN:骑缝 KEYWO
#          sealSignDatePositionInfo: $sealSignDatePositionInfo   #时间坐标信息
#          selected: $selected   #是否被选择
#      signatureTypeList: $signatureTypeList   #签署区类型
#  params:
#    batchTemplateInitiationUuid: $batchTemplateInitiationUuid   #批量发起业务Uuid
#    filePreTaskId: $filePreTaskId   #预签署设置任务id
#    filePreTaskInfos: $filePreTaskInfos

request:
  url: ${ENV(esign.projectHost)}/esign-docs/batchTemplateInitiation/signature/save
  method: POST
  headers: ${gen_main_headers()}
  json:
    customerIP:
    deptId:
    domain:
    params: $params
    platform:
    tenantCode:
    userCode: