# 暂存批量任务
variables:
  data:
  params: $data
request:
  url: ${ENV(esign.projectHost)}/esign-docs/batchTemplateInitiation/staging
  method: POST
#  headers: ${gen_main_headers()}
  headers:
    Content-Type: 'application/json'
    authorization: ${getPortalToken()}
    X-timevale-project-id: ${ENV(esign.projectId)}
  json:
    customerIP:
    deptId:
    domain:
    params: $params
    platform:
    tenantCode:
    userCode: