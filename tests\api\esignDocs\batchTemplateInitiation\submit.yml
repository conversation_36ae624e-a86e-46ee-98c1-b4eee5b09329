#批量发起任务
variables:
    signerList:
        -   signerType: 1
            signerTerritory: 1
            draggable: true
            organizeCode: ''
            userCode: ${ENV(ceswdzxzdhyhwgd1.userCode)}
            userName: ${ENV(ceswdzxzdhyhwgd1.userName)}
            sealTypeCode: ''
            autoSign: 0
            onlyUkeySign: 0
            assignSigner: 1
    signersList:
        -   signMode: 1
            signerList: $signerList

    extensionList:
    presetVersion: 1
    fileFormat: 1           #签署文件类型 1:pdf 2:ofd
    batchTemplateInitiationName: '业务模板20230309164504.075820'
    batchTemplateInitiationUuid: '59605d7219b8a6547351b7faae3ae7de'
    excelFileKey:
    initiatorOrganizeCode: ${ENV(sign01.main.orgCode)}
    initiatorOrganizeName: ${ENV(sign01.main.orgName)}
    initiatorUserName: '测试签署一'
    initiatorDepartmentName: ''
    initiatorDepartmentCode: ''
    needCheckSignArea: False             # 是否需要校验签署区
    repeatedFlag:
    submit:
    businessPresetUuid: '77631cc74d7cd161eede2930d4dcdf4f'
    businessPresetSnapshotUuid: 'ae2eb5f30f5db17ee84f476515d2f57f'
    wordList: []
    saveSigners:

    type: 3
    contentList: []
    sort: 0
    advertisement: False
    chargingType: 1
    readComplete: False
    remark:
    signFlowExpireTime:
    businessNo:
    appendList: []
#        -   appendType: 1
#            attachmentInfo:
#                fileKey: 1678352185982_6HTRnheO.pdf
    attachments: []
    ccInfos: []
    auditInfo:
    sealReason:
    physicsSealUserList:
    physicalSeal: 0
    fillList: []

    params:
        extensionList: $extensionList
        advertisement: $advertisement
        appendList: $appendList
        attachments: $attachments
        auditInfo: $auditInfo
        batchTemplateInitiationName: $batchTemplateInitiationName
        batchTemplateInitiationUuid: $batchTemplateInitiationUuid
        businessNo: $businessNo
        businessPresetSnapshotUuid: $businessPresetSnapshotUuid
        businessPresetUuid: $businessPresetUuid
        ccInfos: $ccInfos
        chargingType: $chargingType
        contentList: $contentList
        fileFormat: $fileFormat
        initiatorDepartmentCode: $initiatorDepartmentCode
        initiatorDepartmentName: $initiatorDepartmentName
        initiatorOrganizeCode: $initiatorOrganizeCode
        initiatorOrganizeName: $initiatorOrganizeName
        initiatorUserName: $initiatorUserName
        needCheckSignArea: $needCheckSignArea
        readComplete: $readComplete
        remark: $remark
        repeatedFlag: $repeatedFlag
        saveSigners: $saveSigners
        signFlowExpireTime: $signFlowExpireTime
        signersList: $signersList
        fillList: $fillList
        sort: $sort
        submit: $submit
        type: $type
        wordList: $wordList
        sealReason: $sealReason
        physicsSealUserList: $physicsSealUserList
        physicalSeal: $physicalSeal

request:
    url: ${ENV(esign.projectHost)}/esign-docs/batchTemplateInitiation/submit
    method: POST
    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
    json:
        customerIP:
        deptId:
        domain:
        params: $params
        platform:
        tenantCode:
        userCode: