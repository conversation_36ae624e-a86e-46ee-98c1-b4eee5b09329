variables:
  authorization0: ${getPortalToken()}
  project0: ${ENV(esign.projectId)}
  checkRepetition: 1
  allowAddFile: 1
  initiatorAll: 1
  initiatorList: []
  templateId:
  templateName:
  version: 1
  templateList:
    - templateId: $templateId
      templateName: $templateName
      version: $version
  supplementModelKey:
  allowSupplement: 0
  workFlowModelName:
  presetName: "测试业务模板-${get_randomNo_16()}"
  supplementModelName:
  workFlowModelKey:
  presetId_addDetail:
  presetId: $presetId_addDetail
  fileFormat: 1
  presetType: 0
  authUserList: []
  authAll: 1
  params: {
      checkRepetition: $checkRepetition,   #是否校验内容域重复 0否 1是
      templateList: $templateList,
      initiatorAll: $initiatorAll,   #发起人权限 1表示所有用户 0指定
      allowAddFile: $allowAddFile,   #发起时追加文件 0不允许 1允许
      supplementModelKey: $supplementModelKey,   #绑定流程引擎模板（补签）
      allowSupplement: $allowSupplement,   #是否允许补签
      workFlowModelName: $workFlowModelName,   #绑定流程引擎模板名称
      presetName: $presetName,   #业务模板配置名称
      supplementModelName: $supplementModelName,   #绑定流程引擎模板（补签）名称
      workFlowModelKey: $workFlowModelKey,   #绑定流程引擎模板
      presetId: $presetId,   #业务模板配置id
      fileFormat: $fileFormat,   #模板文件类型 1pdf，2ofd
      initiatorList: $initiatorList,
      presetType: $presetType,
      authAll: $authAll,
      authUserList: $authUserList
    }

request:
  url: ${ENV(esign.projectHost)}/esign-docs/businessPreset/addDetail
  method: POST
  headers:
      Content-Type: 'application/json'
      authorization: $authorization0
      X-timevale-project-id: $project0
      navId: "1523545065987903488"
  json:
    customerIP:
    deptId:
    domain:
    params: $params
    platform:
    tenantCode:
    userCode: