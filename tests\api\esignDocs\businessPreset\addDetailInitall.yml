variables:
  businessPreset_navId_key: "businessPreset_navId"
  checkRepetition: 1
  allowAddFile: 1
  initiatorAll: 1
  initiatorList: []
  templateList: []
  supplementModelKey:
  allowSupplement: 0
  workFlowModelName:
  presetName: "测试业务模板-${get_randomNo_16()}"
  supplementModelName:
  workFlowModelKey:
  presetId:
  fileFormat: 1
  presetType: 0

request:
  url: ${ENV(esign.projectHost)}/esign-docs/businessPreset/addDetail
  method: POST
  headers: ${gen_main_headers_navId($businessPreset_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      checkRepetition: $checkRepetition,   #是否校验内容域重复 0否 1是
      templateList: $templateList,
      initiatorAll: $initiatorAll,   #发起人权限 1表示所有用户 0指定
      allowAddFile: $allowAddFile,   #发起时追加文件 0不允许 1允许
      supplementModelKey: $supplementModelKey,   #绑定流程引擎模板（补签）
      allowSupplement: $allowSupplement,   #是否允许补签
      workFlowModelName: $workFlowModelName,   #绑定流程引擎模板名称
      presetName: $presetName,   #业务模板配置名称
      supplementModelName: $supplementModelName,   #绑定流程引擎模板（补签）名称
      workFlowModelKey: $workFlowModelKey,   #绑定流程引擎模板
      presetId: $presetId,   #业务模板配置id
      fileFormat: $fileFormat,   #模板文件类型 1pdf，2ofd
      initiatorList: [$initiatorList],
      presetType: $presetType
    }
    platform:
    tenantCode:
    userCode: