name: 添加用印方式
base_url: ${ENV(esign.projectHost)}
variables:
  remindInterval_addSealConfig: 15
  authCodeVisibleRoles_addSealConfig: 1
  subjectRule_addSealConfig: 0
  verifyQrCode_addSealConfig: 0
  watermarkEnable_addSealConfig: 0
  allowApplicantSeal_addSealConfig: 0
  fileNameEnable_addSealConfig: 0
  subjectEnable_addSealConfig: 0
  subjectText_addSealConfig:
  fileNameText_addSealConfig:
  messageConfigList_addSealConfig: [
    { templateCode: "seal_start", templateName: "用印通知", ownerFlag: "2", sealerFlag: "1" },
    { templateCode: "seal_finish", templateName: "用印完成通知", ownerFlag: "1", sealerFlag: "1" },
    { templateCode: "seal_expiring", templateName: "用印过期通知", ownerFlag: "1", sealerFlag: "1" }
  ]
  watermarkList_addSealConfig: []
  fileNameRule_addSealConfig: 1
  presetId_addSealConfig:

request:
  headers: ${gen_main_headers()}
  url: ${ENV(esign.projectHost)}/esign-docs/businessPreset/addSealConfig
  method: post
  json:
    params:
      remindInterval: $remindInterval_addSealConfig   #催办时间间隔 单位分钟
      authCodeVisibleRoles: $authCodeVisibleRoles_addSealConfig   #授权码可见角色, 1-用印人，2-发起人，3-有数据权限的用户多个用,隔开
      subjectRule: $subjectRule_addSealConfig   #流程主体生成规则 多个逗号分隔，0手动输入,1业务模板名称,2固定文字3发起组织
      watermarkEnable: $watermarkEnable_addSealConfig   #水印开关
      allowApplicantSeal: $allowApplicantSeal_addSealConfig   #是否允许申请人用印，0否，1是；默认为0
      fileNameEnable: $fileNameEnable_addSealConfig   #文件名称设置 0手动输入 1按照规则设置
      verifyQrCode: $verifyQrCode_addSealConfig   #是否核验二维码，0否，1是；默认为0
      subjectEnable: $subjectEnable_addSealConfig   #流程主体设置 0手动输入 1按照规则设置
      messageConfigList: $messageConfigList_addSealConfig
      subjectText: $subjectText_addSealConfig   #流程主体固定文字
      fileNameText: $fileNameText_addSealConfig   #文件名称固定文字
      watermarkList: $watermarkList_addSealConfig
      fileNameRule: $fileNameRule_addSealConfig   #文件名称生成规则 多个逗号分隔 0手动输入,1文件名称,2文件类型 3固定文字4
      presetId: $presetId_addSealConfig   #业务模板配置id
