name: 添加业务模板配置用印方
base_url: ${ENV(esign.projectHost)}
variables:
  businessPreset_navId: "businessPreset_navId"
  sealerList_addSealers: []
  allowAddSealer_addSealers: 1
  presetId_addSealers:
  status_addSealers: 1 #默认启用
request:
  headers: ${gen_main_headers($businessPreset_navId)}
  url: ${ENV(esign.projectHost)}/esign-docs/businessPreset/addSealers
  method: post
  json:
    params:
      allowAddSealer: $allowAddSealer_addSealers   #允许发起时设置用印方 0不允许 1允许
      sealerList: $sealerList_addSealers
      presetId: $presetId_addSealers   #业务模板配置id
      status: $status_addSealers   #状态 是否启用 0 未启用 1已启用
