#添加业务模板配置签署方
variables:
    allowAddSigner: 1  #允许发起时设置签署方 0不允许 1允许
    presetId: ''
    sort: 0  #是否顺序签署 0 否 1是
    status: 1
    allowAddSealer: 1
    allowAddFile: 1
    initiatorAll: 1
    initiatorEdit: 0
    presetVersion: 0
    authAll: 1
    fileFormat: 1
    authUserList: []
    signatoryList0: []
    fillingList: []
    signatoryList:
        - sealType: ''
          signatoryId: ''
          signatoryName: ''
          templateId: ''
          templateName: ''

    signerList:
        -   assignSigner: 0     #是否指定签署方 0 不指定 1指定
            autoSign: 0    #是否为静默签 0否 1是
            departmentCode: ''
            organizeCode: ''
            sealTypeCode: ''
            signMode: ''
            signNode: ''
            signOrder: ''
            needGather: 0  #是否需要采集 0否 1是
            onlyUkeySign: 0  #仅支持UKey签署 0否 1是
            signerId: ''
            signerTerritory: 1 #1-内部，2-相对方
            signerType: 1  #1-个人，2-企业
            userCode: ''
            signatoryList: []

    signerNodeList:
        -   signMode: 1               #签署模式 0顺序 1无序 2或签
            signerList: $signerList

    params:
        allowAddSigner: $allowAddSigner
        allowAddSealer: $allowAddSealer
        presetId: $presetId
        signerNodeList: $signerNodeList
        sort: $sort
        status: $status
        allowAddFile: $allowAddFile
        initiatorAll: $initiatorAll
        initiatorEdit: $initiatorEdit
        fileFormat: $fileFormat
        presetVersion: $presetVersion
        authUserList: $authUserList
        fillingList: $fillingList
        authAll: $authAll
request:
    url: ${ENV(esign.projectHost)}/esign-docs/businessPreset/addSigners
    method: POST
    #  headers: ${gen_main_headers_navId($businessPreset_navId_key)}
    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
        navId: ${ENV(businessPreset_navId)}
    json:
        customerIP:
        deptId:
        domain:
        params: $params
        platform:
        tenantCode:
        userCode: