variables:
    presetName: '业务模板${getDateTime()}'
    presetType_businessPresetCreate: 0 #电子签署
request:
    url: ${ENV(esign.projectHost)}/esign-docs/businessPreset/create
    method: POST
    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
        navId: ${ENV(businessPreset_navId)}
    json:
        customerIP:
        deptId:
        domain:
        params:
            presetName: $presetName
            presetType: $presetType_businessPresetCreate
        platform:
        tenantCode:
        userCode: