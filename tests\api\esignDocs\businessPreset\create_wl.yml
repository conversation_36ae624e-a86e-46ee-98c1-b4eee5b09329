variables:
    presetName: '业务模板物理用印${getDateTime()}'
    presetType_businessPresetCreate: 1 #物理用印
request:
    url: ${ENV(esign.projectHost)}/esign-docs/businessPreset/create
    method: POST
    headers:
        Content-Type: 'application/json'
        authorization: ${get_main_token()}
        X-timevale-project-id: ${ENV(esign.projectId)}
        navId: ${ENV(businessPreset_navId)}
    json:
        customerIP:
        deptId:
        domain:
        params:
            presetName: $presetName
            presetType: $presetType_businessPresetCreate
        platform:
        tenantCode:
        userCode: