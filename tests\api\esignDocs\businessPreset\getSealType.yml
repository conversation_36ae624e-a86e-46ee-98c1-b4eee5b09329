#通过机构编码和经办人编码获取印章类型
variables:
  businessPreset_navId_key: "businessPreset_navId"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/businessPreset/getSealType
  method: POST
  headers: ${gen_main_headers_navId($businessPreset_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      organizeCode: $organizeCode,
      userCode: $userCode,
      userType: $userType,
    }
    platform:
    tenantCode:
    userCode:
