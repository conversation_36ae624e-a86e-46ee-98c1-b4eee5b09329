variables:
  businessPreset_navId_key: "businessPreset_navId"
  status: ""
  page: 1
  size: 10
  fileFormat_businessPresetList: ""
  presetType_businessPresetList: ""
  businessTypeId: ""
  businessTypeId_businessPresetList: "$businessTypeId"
  queryPresetName: ""
request:
  url: ${ENV(esign.projectHost)}/esign-docs/businessPreset/list
  method: POST
  headers: ${gen_main_headers_navId($businessPreset_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      presetName: $queryPresetName,
      status: $status,
      page: $page,
      size: $size,
      fileFormat: $fileFormat_businessPresetList,
      presetType: $presetType_businessPresetList,
      businessTypeId: $businessTypeId_businessPresetList
    }
    platform:
    tenantCode:
    userCode: