#添加填写方
variables:
    businessPreset_navId: "businessPreset_navId"
    presetId_updateFillingUsers: ""
    fillingList_updateFillingUsers:
        - name:
          userTerritory:
          key: ${get_snowflake()}
    params_updateFillingUsers:
        presetId: $presetId_updateFillingUsers
        fillingList: $fillingList_updateFillingUsers
    json_updateFillingUsers:
        params: $params_updateFillingUsers

request:
    url: ${ENV(esign.projectHost)}/esign-docs/businessPreset/updateFillingUsers
    method: POST
    headers: ${get_main_headers($businessPreset_navId)}
    json: $json_updateFillingUsers