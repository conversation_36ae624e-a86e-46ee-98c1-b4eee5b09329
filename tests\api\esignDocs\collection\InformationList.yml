#查询已采信息列表
variables:
  collectedInformationId: ""
  collectedInformationStatus: ""
  collectionTaskId: ""
  collectionTemplateId: ""
  pageNo: ""
  pageSize: ""
  moduleInfos: ""
  json: {
    "collectedInformationId": $collectedInformationId,
    "collectedInformationStatus": $collectedInformationStatus,
    "collectionTaskId": $collectionTaskId,
    "collectionTemplateId": $collectionTemplateId,
    "moduleInfos":$moduleInfos,
    "pageNo": $pageNo,
    "pageSize": $pageSize
}
request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/collection/informationList
  method: POST
  headers: ${gen_openapi_post_headers_getway($json)}
  json: $json