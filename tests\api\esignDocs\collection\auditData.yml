#验证采集登录账号的采集任务当前状态
variables:
    authorization_audit_data:
    formKey_auditData:
    includeIds_dataId: []
    auditType_audit: "AUDIT_REJECT"
    reason_audit: "audit,data"
    json_audit_data:
        excludeIds: []
        formKey: $formKey_auditData
        includeIds: $includeIds_dataId
        taskKey:
        reason: $reason_audit
        auditType: $auditType_audit
        type: "MANAGE"
        query: {}
        confirm: true

request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/lc/collection/form/${formKey_auditData}/data/audit
    method: POST
    headers:
        x-tisgn-lowcdoe-tab: "manage_list"
        x-tsign-lowcode-page: "lc_eform_collection_data"
        authorization: $authorization_audit_data
        esa-token: $authorization_audit_data
        esa-token-source: 'PORTAL'
        X-timevale-project-id: ${ENV(esign.projectId)}
    json: $json_audit_data