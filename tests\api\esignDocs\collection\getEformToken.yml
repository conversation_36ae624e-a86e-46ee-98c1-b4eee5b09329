#获取备注签引用采集登录态
variables:
    accountNumber: "${ENV(sign01.accountNo)}"
    password: ${ENV(password01)}
    eform_token: ""
    json_form_token:
        params: $eform_token
        domain: "admin_platform"

request:
    url: ${ENV(esign.projectHost)}/esign-signs/remarkSign/getEformToken
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}
    json: $json_form_token