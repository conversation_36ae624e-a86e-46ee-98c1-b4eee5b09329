#新建采集任务
variables:
  collectionTemplateId: ""
  collectionTaskName: ""
  collectionTaskType: ""
  collectionTaskCreatorUserCode: ""
  collectionTaskCreatorCustomOrgNo: ""
  collectionTaskCreateOrganizationCode: ""
  collectionTaskCreateCustomOrgNo: ""
  collectionTaskExpireTime: ""
  collectionTaskApprove: ""
  automaticInitiation: ""
  businessTemplateCode: ""
  assignUserName: ""
  contactType: ""
  contactContent: ""
  json: {
    "collectionTemplateId": $collectionTemplateId,
    "collectionTaskName": $collectionTaskName,
    "collectionTaskType": $collectionTaskType,
    "collectionTaskCreatorUserCode": $collectionTaskCreatorUserCode,
    "collectionTaskCreatorCustomAccountNo": $collectionTaskCreatorCustomAccountNo,
    "collectionTaskCreatorOrganizationCode": $collectionTaskCreatorOrganizationCode,
    "collectionTaskCreatorCustomOrgNo": $collectionTaskCreatorCustomOrgNo,
    "collectionTaskExpireTime": $collectionTaskExpireTime,
    "collectionTaskApprove": $collectionTaskApprove,
    "automaticInitiation": $automaticInitiation,
    "businessTemplateCode": $businessTemplateCode,
    "assignUsers": $assignUsers
}
request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/collection/taskCreate
  method: POST
  headers: ${gen_openapi_post_headers_getway($json)}
  json: $json