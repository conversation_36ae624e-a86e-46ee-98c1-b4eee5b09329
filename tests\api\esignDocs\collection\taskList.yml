#查询采集任务列表
variables:
  collectionTaskCreatorCustomAccountNo: ""
  collectionTaskCreatorCustomOrgNo: ""
  collectionTaskCreatorOrganizationCode: ""
  collectionTaskCreatorUserCode: ""
  collectionTaskEndCreateTime: ""
  collectionTaskEndExpireTime: ""
  collectionTaskEndUpdateTime: ""
  collectionTaskId: ""
  collectionTaskName: ""
  collectionTaskStartCreateTime: ""
  collectionTaskStartExpireTime: ""
  collectionTaskStartUpdateTime: ""
  collectionTaskStatus: ""
  collectionTemplateId: ""
  pageNo: 1
  pageSize: 50
  json: {
    "collectionTaskCreatorCustomAccountNo": $collectionTaskCreatorCustomAccountNo,
    "collectionTaskCreatorCustomOrgNo": $collectionTaskCreatorCustomOrgNo,
    "collectionTaskCreatorOrganizationCode": $collectionTaskCreatorOrganizationCode,
    "collectionTaskCreatorUserCode": $collectionTaskCreatorUserCode,
    "collectionTaskEndCreateTime": $collectionTaskEndCreateTime,
    "collectionTaskEndExpireTime": $collectionTaskEndExpireTime,
    "collectionTaskEndUpdateTime": $collectionTaskEndUpdateTime,
    "collectionTaskId": $collectionTaskId,
    "collectionTaskName": $collectionTaskName,
    "collectionTaskStartCreateTime": $collectionTaskStartCreateTime,
    "collectionTaskStartExpireTime": $collectionTaskStartExpireTime,
    "collectionTaskStartUpdateTime": $collectionTaskStartUpdateTime,
    "collectionTaskStatus": $collectionTaskStatus,
    "collectionTemplateId": $collectionTemplateId,
    "pageNo": $pageNo,
    "pageSize": $pageSize
}
request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/collection/taskList
  method: POST
  headers: ${gen_openapi_post_headers_getway($json)}
  json: $json