#查询采集模板列表
variables:
  collectionTemplateName: "" #采集模板名称，支持模糊查询
  collectionTemplateId: ""  #采集模板ID
  collectionTemplateStartCreateTime: "2023-01-01 00:00:00 " #采集模板创建时间：开始时间查询：开始时间-格式：yyyy-MM-dd HH:mm:ss.（搜索范围大于等于开始时间）
  collectionTemplateEndCreateTime: "2024-01-01 00:00:00" #采集模板创建时间：结束时间查询：结束时间-格式：yyyy-MM-dd HH:mm:ss.（搜索范围小于等于结束时间）
  pageNo: 1
  pageSize: 50
  json: {
    "collectionTemplateName": $collectionTemplateName,
    "collectionTemplateId": $collectionTemplateId,
    "collectionTemplateStartCreateTime": $collectionTemplateStartCreateTime,
    "collectionTemplateEndCreateTime": $collectionTemplateEndCreateTime,
    "pageNo": $pageNo,
    "pageSize": $pageSize
  }
request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/collection/templateList
  method: POST
  headers:  ${gen_openapi_post_headers_getway($json)}
  json: $json
