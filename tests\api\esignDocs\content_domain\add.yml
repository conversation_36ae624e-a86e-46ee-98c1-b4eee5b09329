#内容域库列表
request:
  url: ${ENV(esign.projectHost)}/esign-docs/content_domain/add
  method: POST
  headers: ${gen_main_headers()}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      contentName: $contentName,
      contentCode: $contentCode,
      formatType: $formatType,
      formatRule: $formatRule,
      required: $required,
      dataSource: $dataSource,
      sourceField: $sourceField,
      description: $description
    }
    platform:
    tenantCode:
    userCode: