name: 创建自定义控件组
request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/custom-field/group/create
  method: POST
  headers:
    authorization: $authorization_token
    x-tsign-client-appname: "epaas-template-front"
    x-tsign-client-id: "pc"
    x-tsign-open-operator-id: "XXXtest"
    x-tsign-tenant-id: "XXXtest"
    x-tsign-tpl-token: $tplToken_content
  json:
    groupName: $groupName
variables:
  groupName:
  tplToken_content:
  authorization_token: ${getPortalToken()}
