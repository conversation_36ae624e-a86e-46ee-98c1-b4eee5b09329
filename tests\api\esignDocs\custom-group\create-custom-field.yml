name: 创建自定义控件
request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/custom-field/create
  method: POST
  headers:
    authorization: $authorization_token
    x-lang: "zh-CN"
    x-tsign-client-appname: "epaas-template-front"
    x-tsign-client-id: "pc"
    x-tsign-open-language: "zh-CN"
    x-tsign-open-operator-id: "XXXtest"
    x-tsign-tenant-id: "XXXtest"
    x-tsign-tpl-token: $tplToken_content
  json:
    fieldId: $fieldId
    label: $label
    custom: $custom
    type: $type
    subType: $subType
    sort: $sort
    formula: $formula
    style: $style
    settings: $settings
    options: $options
    instructions: $instructions
    contentFieldId: $contentFieldId
    bizId: $bizId
    fieldKey: $fieldKey
    fillGroupKey: $fillGroupKey
    fieldValue: $fieldValue
    defaultValue: $defaultValue
    position: $position
    formField: $formField
    groupId: $groupId
    structType: $structType
    id: $id
variables:
  fieldId: ""
  label: ""
  custom: ""
  type: ""
  subType: null
  sort: 0
  formula: ""
  style: {"font":"1","fontSize":42,"textColor":"#000","width":160,"height":49,"bold":false,"italic":false,"underLine":false,"lineThrough":false,"verticalAlignment":"TOP","horizontalAlignment":"LEFT","styleExt":{"signDatePos":null,"units":"px","imgType":null,"usePageTypeGroupId":"","hideTHeader":null,"selectLayout":null,"borderWidth":"1","borderColor":"#000","keyword":"","groupKey":"","tickOptions":null,"elementId":"","posKey":"","linkedId":null,"imgCrop":null,"paddingLeftAndRight":"0"}}
  settings: {"defaultValue":null,"required":false,"dateFormat":null,"validation":{"type":"REGEXP","pattern":""},"selectableDataSource":[],"numberFormat":{"integerDigits":null,"fractionDigits":null,"thousandsSeparator":""},"editable":true,"encryptAlgorithm":"","fillLengthLimit":"20","overflowType":"1","minFontSize":"8","remarkInputType":null,"content":null,"remarkAICheck":null,"dateRule":null,"tickOptions":null,"configExt":{"cooperationerSubjectType":"","icon":"epaas-icon-text","fastCheck":null,"addSealRule":"","keyPosX":null,"keyPosY":null,"ext":"{}","version":null,"mergeId":null},"sealTypes":[],"columnMapping":null,"positionMovable":null,"cellEditableOnFilling":true,"signDatePosition":null}
  options: null
  instructions: ""
  contentFieldId: ""
  bizId: ""
  fieldKey: null
  fillGroupKey: ""
  fieldValue: null
  defaultValue: null
  position: {"x":219.3816273642189,"y":580.1997655342852,"page":"1","scope":"default","intervalType":null}
  formField: false
  groupId: ""
  structType: 1
  id: ""
  tplToken_content:
  authorization_token: ${getPortalToken()}