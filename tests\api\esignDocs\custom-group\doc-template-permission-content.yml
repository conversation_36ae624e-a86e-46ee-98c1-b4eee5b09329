#文档模板权限内容接口
variables:
    tplToken_content:
    authorization_token: ${getPortalToken()}
    timestamp: ${get_timestamp()}

request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/permission/content
    method: GET
    headers:
        authorization: $authorization_token
        x-tsign-client-appname: "epaas-template-front"
        x-tsign-client-id: "pc"
        x-tsign-open-operator-id: "XXXtest"
        x-tsign-tenant-id: "XXXtest"
        x-tsign-tpl-token: $tplToken_content
    params:
        t: $timestamp
    json: null