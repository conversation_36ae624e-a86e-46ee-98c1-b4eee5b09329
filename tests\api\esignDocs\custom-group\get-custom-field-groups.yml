variables:
    tplToken_content:
    authorization_token: ${getPortalToken()}
request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/custom-field/groups?t=${get_timestamp()}&label=
  method: GET
  headers:
    authorization: $authorization_token
    x-lang: zh-CN
    x-tsign-client-appname: epaas-template-front
    x-tsign-client-id: pc
    x-tsign-open-language: zh-CN
    x-tsign-open-operator-id: XXXtest
    x-tsign-tenant-id: XXXtest
    x-tsign-tpl-token: $tplToken_content