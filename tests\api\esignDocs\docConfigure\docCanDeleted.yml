#添加文件夹和文件类型
request:
  url: ${ENV(esign.projectHost)}/esign-docs/docConfigure/docCanDeleted
  method: POST
  headers:
    Content-Type: 'application/json'
    authorization: ${getPortalToken()}
    X-timevale-project-id: ${ENV(esign.projectId)}
    navId: "1523545065937572841"
  json:
    customerIP:
    deptId:
    domain:
    params: {
      docUuid: $docUuid,
      docType: $docType
    }
    platform:
    tenantCode:
    userCode: