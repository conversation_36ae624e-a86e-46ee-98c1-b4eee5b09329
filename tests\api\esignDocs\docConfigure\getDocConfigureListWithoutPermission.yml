#列表搜索文件夹与文件类型（判断数据是否有管理权限）
variables:
  account: "ceswdzxzdhyhwgd1.account"
  pwd: "ceswdzxzdhyhwgd1.password"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/docConfigure/getDocConfigureListWithTemplatePermission
  method: POST
  headers: ${gen_main_headers_for_user($account, $pwd)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      docName: $docName,
      docType: $docType,
      parentUid: $parentUid
    }
    platform:
    tenantCode:
    userCode: