name: 我管理的-查看文档流程详情
variables:
  flowId_getRelationDocFlowDetail: ""
  flowId: ${flowId_getRelationDocFlowDetail}
  authorization0: ${getPortalToken()}

request:
  url: ${ENV(esign.projectHost)}/esign-docs/docFlow/manage/getDocFlowDetail?flowId=$flowId
  method: GET
  headers:
    Content-Type: "application/json"
    authorization: $authorization0
    X-timevale-project-id": ${ENV(esign.projectId)}
    navId: "1523545065987903690"