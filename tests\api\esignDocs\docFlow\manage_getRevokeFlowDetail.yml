variables:
  name: "作废流程详情（我管理的）"
  flowId_getRevokeFlowDetail: ""
  authorization0: ${getPortalToken()}
#  accountNo0: ${ENV(sign01.accountNo)}
#  authorization0: ${getPortalToken($accountNo0)}
request:
  url: ${ENV(esign.projectHost)}/esign-docs/docFlow/manage/getRevokeFlowDetail?flowId=$flowId_getRevokeFlowDetail
  method: GET
  headers:
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
    navId: '1523545065987903690'
