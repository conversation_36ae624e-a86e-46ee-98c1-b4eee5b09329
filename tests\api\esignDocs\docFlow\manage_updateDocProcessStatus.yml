name: 我管理的-获取文档流程列表
base_url: ${ENV(esign.projectHost)}
variables:
  flowId: ""
  auditOpinion: ""
  auditResult: ""
  flowOperationType: ""
  whetherTerminationWf: ""
request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/docProcess/manage/updateDocProcessStatus
  method: POST
  headers: ${gen_main_headers_navId(process_manage_navId)}
  json:
    params:
      "flowId": $flowId
      "auditOpinion": $auditOpinion
      "auditResult": $auditResult
      "flowOperationType": $flowOperationType
      "whetherTerminationWf": $whetherTerminationWf

