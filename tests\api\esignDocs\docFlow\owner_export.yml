name: 我发起的-导出签署文档
variables:
  includeIdList: []
  excelHeadAndSequence: {
    "flowName": "流程名称",
    "processId": "流程编号",
    "signerUserNameList": "签署人",
    "currentHandlerName": "当前环节处理人",
    "flowStatusName": "流程状态",
    "initiatorTime": "发起时间",
    "gmtModified": "最近处理时间",
    "gmtFinish": "结束时间",
    "projectName": "项目名称",
    "projectId": "项目id",
    "initiatorUserName": "发起人",
    "initiatorOrganizeName": "发起人组织",
    "flowTypeName": "流程分类",
    "refuseReason": "拒签原因",
    "revokedReason": "作废原因"  }

request:
  url: ${ENV(esign.projectHost)}/esign-docs/docFlow/owner/export
  method: POST
  headers: ${gen_main_headers_navId(process_manage_navId)}
  json:
    params:
      "excelHeadAndSequence": $excelHeadAndSequence
      "includeFlowIdList": $includeIdList