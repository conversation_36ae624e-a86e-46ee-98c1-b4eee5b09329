name: 我制作的的-获取文档流程列表
base_url: ${ENV(esign.projectHost)}
variables:
  flowId: ""
  flowName: ""
  startTime: ""
  endTime: ""
  flowStatus: ""
  initiatorOrganizeCode: ""
  gmtFinishBegin: ""
  gmtFinishEnd: ""
  gmtModifiedBegin: ""
  gmtModifiedEnd: ""
  businessNo: ""
  dynamicCode: ""
  signerUserName: ""
  size: 10
  page: 1
  processId: ""
  flowExtensions:

request:
  url: ${ENV(esign.projectHost)}/esign-docs/docFlow/owner/getDocFlowLists
  method: POST
  headers: ${gen_main_headers_navId(process_manage_navId)}
  json:
    params:
      "flowId": $flowId
      "flowName": $flowName
      "startTime": $startTime
      "endTime": $endTime
      "flowStatus": $flowStatus
      "initiatorOrganizeCode": $initiatorOrganizeCode
      "gmtFinishBegin": $gmtFinishBegin
      "gmtFinishEnd": $gmtFinishEnd
      "gmtModifiedBegin": $gmtModifiedBegin
      "gmtModifiedEnd": $gmtModifiedEnd
      "businessNo": $businessNo
      "dynamicCode": $dynamicCode
      "signerUserName": $signerUserName
      "page": $page
      "size": $size
      "processId": $processId
      "flowExtensions": $flowExtensions