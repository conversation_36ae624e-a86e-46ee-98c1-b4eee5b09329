variables:
  name: "关联项目-查看（我发起的）"
  flowId_getRelationDocFlowDetail: ""
  authorization0: ${getPortalToken()}
#  accountNo0: ${ENV(sign01.accountNo)}
#  authorization0: ${getPortalToken($accountNo0)}
request:
  url: ${ENV(esign.projectHost)}/esign-docs/docFlow/owner/getRelationDocFlowDetail?flowId=$flowId_getRelationDocFlowDetail
  method: GET
  headers:
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
    navId: '1523545065987903691'
