#设置采集表单(老采集)
variables:
  account: "ceswdzxzdhyhwgd1.account"
  password: "ceswdzxzdhyhwgd1.password"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/docGatherForm/buildForm
  method: POST
#  headers: ${gen_main_headers_for_user($account, $password)}
  headers: ${gen_main_headers()}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      formJson: $formJson,
      formName: $formName,
      presetUuid: $presetUuid,
      signerId: $signerId,
      signerSnapshotId: $signerSnapshotId
    }
    platform:
    tenantCode:
    userCode: