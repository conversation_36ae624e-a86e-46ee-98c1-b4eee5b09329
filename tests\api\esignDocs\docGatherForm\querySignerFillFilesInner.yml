#签署人填写的表单信息页查询文件列表
variables:
  account: ${ENV(ceswdzxzdhyhwgd1.account)}
  password: ${ENV(ceswdzxzdhyhwgd1.password)}
  phoneOrMail:
  isPreview: 0
request:
  url: ${ENV(esign.projectHost)}/esign-docs/docGatherForm/querySignerFillFiles
  method: POST
  headers: ${gen_main_headers_for_user($account, $password)}
  json:
    customerIP:
    deptId:
    domain:
    params:
      templateInitiationSignersUuid: $templateInitiationSignersUuid
      templateInitiationUuid: $templateInitiationUuid
      isPreview: $isPreview
    platform:
    tenantCode:
    userCode: