#签署人正文信息和追加采集信息填写
variables:
  account:
  password:
  phoneOrMail:
request:
  url: ${ENV(esign.projectHost)}/esign-docs/docGatherForm/signerFill
  method: POST
  headers: ${gen_main_headers_for_interirl_or_external($account, $password, $phoneOrMail)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      formValues: $formValues,
      hasSubmit: $hasSubmit,
      signerContents: $signerContents,
      templateInitiationSignersUuid: $templateInitiationSignersUuid,
      templateInitiationUuid: $templateInitiationUuid,
      wordList: $wordList
    }
    platform:
    tenantCode:
    userCode: