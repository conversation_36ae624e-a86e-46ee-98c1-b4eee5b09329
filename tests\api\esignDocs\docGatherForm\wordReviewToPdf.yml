#预览时word文件转换为pdf文件
variables:
  account:
  password:
  phoneOrMail:
  templateInitiationUuid:
  templateInitiationSignersUuid:
  templateId:
  signerContents: [{
        "contentName": "",
        "contentValue": "",
        "formatRule": "",
        "formatType": 0
      }]

request:
  url: ${ENV(esign.projectHost)}/esign-docs/docGatherForm/wordReviewToPdf
  method: POST
#  headers: ${gen_main_headers_for_interirl_or_external($account, $password, $phoneOrMail)}
  headers: ${gen_main_headers()}
  json:
    customerIP:
    deptId:
    domain:
    params: {
              signerContents: $signerContents,
              templateId: $templateId,
              templateInitiationSignersUuid: $templateInitiationSignersUuid,
              templateInitiationUuid: $templateInitiationUuid
    }
    platform:
    tenantCode:
    userCode: