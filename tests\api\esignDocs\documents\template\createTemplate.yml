#openapi创建文件模板
variables:
  - userCode:
  - customAccountNo:
  - organizationCode:
  - customOrgNo:
  - templateName:
  - docTypeId:
  - docTypeName:
  - description:
  - fileKey:
  - json: {
      userCode: $userCode,
      customAccountNo: $customAccountNo,
      organizationCode: $organizationCode,
      customOrgNo: $customOrgNo,
      templateName: $templateName,
      docTypeId: $docTypeId,
      docTypeName: $docTypeName,
      description: $description,
      fileKey: $fileKey
  }
request:
  url: ${ENV(esign.gatewayHost)}/esign-signs/esign-docs/v1/documents/template/createTemplate
  method: POST
  ####headers: ${gen_main_headers()}
  headers: ${gen_openapi_post_headers_getway($json)}
  json: $json
