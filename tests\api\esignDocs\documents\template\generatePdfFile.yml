#openapi填写模板并转为pdf
variables:
  contentId:
  contentCode:
  contentValue:
  contentsControl: [{
      contentId: $contentId,
      contentCode: $contentCode,
      contentValue: $contentValue
    }]
  body: {
    templateId: $templateId,
    fileName: $fileName,
    contentsControl: $contentsControl
  }
request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/documents/template/generatePdfFile
  method: POST
  headers: ${gen_openapi_post_headers_getway($body)}
  json: $body
