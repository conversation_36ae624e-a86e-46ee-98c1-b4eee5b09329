#openapi填写模板并转为pdf
variables:
  body: "testbody"
request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/documents/template/generatePdfFile
  method: POST
  headers: ${gen_openapi_post_headers_getway($body)}
  json:
    templateId: $templateId
    fileName: $fileName
    contentsControl: [{
        contentId: $contentId_generatePdfFile2,
        contentCode: $contentCode_generatePdfFile2,
        contentValue: $contentValue_generatePdfFile2
    },{
        contentId: $contentId2_generatePdfFile2,
        contentCode: $contentCode2_generatePdfFile2,
        contentValue: $contentValue2_generatePdfFile2
    }]
