#openapi获取文件模板填写页
variables:
  - data: $data
#  - data: {
#      businessNo: $businessNo,
#      templateId: $templateId,
#      fileName: $fileName,
#      redirectUrl: $redirectUrl,
#      fillNotifyUrl: $fillNotifyUrl,
#      expirationDate: $expirationDate,
#      contentsControl: [ {
#        contentId: $contentId,
#        contentCode: $contentCode,
#        contentValue: $contentValue,
#        editFillingValue: $editFillingValue
#      } ]
#  }
request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/documents/template/getTemplateFillUrl
  method: POST
  headers: ${gen_openapi_post_headers_getway($data)}
  json: $data
