#openapi查询己发布的模板列表
variables:
  templateName:
  docTypeId: ""
  organizationCode: ""
  organizationName: ""
  createStartTime: ""
  createEndTime: ""
  pageSize: 10
  pageNo: 1
  json: {
    "templateName": $templateName,
    "docTypeId": $docTypeId,
    "organizationCode": $organizationCode,
    "organizationName": $organizationName,
    "createStartTime": $createStartTime,
    "createEndTime": $createEndTime,
    "pageSize": $pageSize,
    "pageNo": $pageNo
  }
request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/documents/template/list
  method: POST
  headers: ${gen_openapi_post_headers_getway($json)}
  json:
    "templateName": $templateName
    "docTypeId": $docTypeId
    "organizationCode": $organizationCode
    "organizationName": $organizationName
    "createStartTime": $createStartTime
    "createEndTime": $createEndTime
    "pageSize": $pageSize
    "pageNo": $pageNo
