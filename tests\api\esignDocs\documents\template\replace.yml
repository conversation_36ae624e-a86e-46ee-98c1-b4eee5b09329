#替换文档底稿文件
variables:
    template_manage_navId_key: "template_manage_navId"
    templateId:
    fileKey:
    version: 1
    json:
      params: {
        "templateId": $templateId,
        "version": $version,
        "fileKey": $fileKey
      }
request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/template/manage/file/replace
  method: POST
  headers: ${gen_main_headers_navId($template_manage_navId_key)}
  json:
    params: {
      "templateId": $templateId,
      "version": $version,
      "fileKey": $fileKey
    }
