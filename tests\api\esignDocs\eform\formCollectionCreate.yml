#新建登记表
variables:
    formJsonCreate:
    formFields:
    formNameCreate: "全链路登记表单"
    formOperations: [{"key":"collection_list","code":"lc_eform_collection_list","label":"登记信息表","type":"SYSTEM"},
        {"key":"collection_add","code":"lc_eform_collection_add","label":"新建登记表","type":"SYSTEM"},
        {"key":"collection_task_add","code":"lc_eform_collection_task_add","label":"新建采集任务","type":"SYSTEM"},
        {"key":"collection_template_relation","code":"lc_eform_collection_template_relation","label":"关联业务模版","type":"CUSTOM"},
        {"key":"collection_edit","code":"lc_eform_collection_edit","label":"编辑登记表","type":"SYSTEM"},
        {"key":"collection_view","code":"lc_eform_collection_view","label":"查看登记表","type":"SYSTEM"},
        {"key":"collection_delete","code":"lc_eform_collection_delete","label":"删除登记表","type":"SYSTEM"},
        {"key":"collection_task_start_list","code":"lc_eform_collection_task_start_list","label":"我发起的","type":"SYSTEM"},
        {"key":"collection_task_manage_list","code":"lc_eform_collection_task_manage_list","label":"我管理的","type":"SYSTEM"},
        {"key":"collection_task_view","code":"lc_eform_collection_task_view","label":"任务详情","type":"SYSTEM"},
        {"key":"collection_task_enable","code":"lc_eform_collection_task_enable","label":"启用","type":"SYSTEM"},
        {"key":"collection_task_disable","code":"lc_eform_collection_task_disable","label":"停用","type":"SYSTEM"},
        {"key":"collection_task_delete","code":"lc_eform_collection_task_delete","label":"删除","type":"SYSTEM"},
        {"key":"collection_task_url","code":"lc_eform_collection_task_url","label":"复制链接","type":"SYSTEM"},
        {"key":"collection_data_start_list","code":"lc_eform_collection_data_start_list","label":"我发起的","type":"SYSTEM"},
        {"key":"collection_data_manage_list","code":"lc_eform_collection_data_manage_list","label":"我管理的","type":"SYSTEM"},
        {"key":"collection_data_sign","code":"lc_eform_collection_data_sign","label":"选择数据发起签署","type":"SYSTEM"},
        {"key":"collection_data_export","code":"lc_eform_collection_data_export","label":"导出数据","type":"SYSTEM"},
        {"key":"collection_data_view","code":"lc_eform_collection_data_view","label":"查看","type":"SYSTEM"},
        {"key":"collection_data_approve","code":"lc_eform_collection_data_approve","label":"审核","type":"SYSTEM"},
        {"key":"collection_data_delete","code":"lc_eform_collection_data_delete","label":"删除","type":"SYSTEM"},
        {"key":"collection_data_edit","code":"lc_eform_collection_data_edit","label":"编辑","type":"SYSTEM"}]

request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/lc/collection/form
    method: POST
    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        esa-token: ${getPortalToken()}
        esa-token-source: 'PORTAL'
        X-timevale-project-id: ${ENV(esign.projectId)}
    json:
        formJson: $formJsonCreate #采集表单绘制的数据
        name: $formNameCreate #登记表名称
        operations: $formOperations  #登记表操作
        pageConfig:
            fields: $formFields  #这个登记表的字段