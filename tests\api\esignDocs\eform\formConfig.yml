#配置登记表
variables:
    baseUrl: ${ENV(esign.projectHost)}
    formJsonConfig: "{\"config\":{\"customClass\":\"\",\"hideErrorMessage\":false,\"hideLabel\":false,\"labelPosition\":\"top\",\"labelWidth\":100,\"layout\":\"horizontal\",\"size\":\"default\",\"ui\":\"element\",\"width\":\"100%\",\"version\":\"2.1\",\"eventScript\":[{\"key\":\"mounted\",\"name\":\"mounted\",\"func\":\"\"}]},\"list\":[{\"type\":\"input\",\"showNameText\":\"单行文本\",\"availableInTable\":true,\"options\":{\"strLen\":{\"enable\":false,\"max\":100,\"min\":0,\"maxLimit\":191},\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"测试全链路一\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hasAppendText\":true,\"hidden\":false,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"\",\"patternCheck\":false,\"patternMessage\":\"\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"input\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"姓名\",\"key\":\"r105pwcg\",\"model\":\"input_r105pwcg\",\"alias\":\"input_r105pwcg\",\"rules\":[],\"row\":0,\"col\":0},{\"type\":\"cellphone\",\"showNameText\":\"手机号\",\"availableInTable\":true,\"options\":{\"conditionList\":{},\"controlList\":{},\"customClass\":\"\",\"dataBind\":true,\"dataOnly\":false,\"defaultStatus\":1,\"defaultValue\":\"19112100001\",\"defaultValueConfigurable\":true,\"disabled\":false,\"eventConfigEnabled\":true,\"hidden\":false,\"hidePatternConfig\":true,\"isLabelWidth\":false,\"labelWidth\":100,\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"patternCheck\":true,\"patternMessage\":\"请输入正确的手机号码\",\"placeholder\":\"请输入\",\"placeholderText\":\"输入提示\",\"required\":false,\"requiredMessage\":\"\",\"showField\":\"\",\"showPassword\":false,\"supportFileName\":true,\"tips\":\"\",\"type\":\"cellphone\",\"validator\":\"\",\"validatorCheck\":false,\"width\":\"100%\",\"widthType\":\"1\",\"maxlength\":100,\"tableColumn\":false},\"events\":{\"onChange\":\"\",\"onFocus\":\"\",\"onBlur\":\"\"},\"name\":\"手机号\",\"key\":\"y9sirtub\",\"model\":\"cellphone_y9sirtub\",\"alias\":\"cellphone_y9sirtub\",\"rules\":[{\"pattern\":\"^(?:(?:\\\\+|00)86)?1[3-9]\\\\d{9}$\",\"message\":\"请输入正确的手机号码\"}],\"row\":1,\"col\":0}],\"remoteComponents\":{\"matter-form_input\":\"$baseUrl/esign-docs/lowcode/test/components/matter-form/input/1.0.58/input.umd.js\",\"matter-form_cellphone\":\"$baseUrl/esign-docs/lowcode/test/components/matter-form/cellphone/1.0.59/cellphone.umd.js\"}}"

request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/lc/collection/form/config
    method: POST
    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        esa-token: ${getPortalToken()}
        esa-token-source: 'PORTAL'
        X-timevale-project-id: ${ENV(esign.projectId)}
    json:
        formJson: $formJsonConfig #采集表单绘制的数据