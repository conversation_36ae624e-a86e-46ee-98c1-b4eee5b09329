#查询登记表
variables:
    formNameQuery: ""
    formKeyQuery:

request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/lc/collection/form?currentPage=1&pageSize=20&name=$formNameQuery&key=$formKeyQuery
    method: GET
    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        esa-token: ${getPortalToken()}
        esa-token-source: 'PORTAL'
        X-timevale-project-id: ${ENV(esign.projectId)}