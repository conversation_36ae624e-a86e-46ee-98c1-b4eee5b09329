#创建采集任务
variables:
    formKeyTask:
    creatorTask: "${ENV(sign01.accountNo)}"
    creatorNameTask: "${ENV(sign01.userName)}"
    organizationCodeTask: "${ENV(sign01.main.orgCode)}"
    nameTask: "全链路采集任务"
    auditTask: false
    endTimeTask: "${getTimestamp(5)}"
    startTask: false
    templateKeyTask:
    taskType: PUBLIC
    json_form_task:
        formKey: $formKeyTask #采集表单绘制的数据
        taskType: $taskType
        creator: $creatorTask
        creatorName: $creatorNameTask
        organizationCode: $organizationCodeTask
        name: $nameTask
        audit: $auditTask
        endTime: $endTimeTask
        start: $startTask
        templateKey: $templateKeyTask
request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/lc/collection/task
    method: POST
    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        esa-token: ${getPortalToken()}
        esa-token-source: 'PORTAL'
        X-timevale-project-id: ${ENV(esign.projectId)}
    json: $json_form_task
