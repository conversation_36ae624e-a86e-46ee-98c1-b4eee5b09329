#创建采集任务
variables:
    formData:
    formDataKey:
    taskKeyData:
    authorizationformData: ${getPortalToken()}

request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/lc/open/collection/form/$formDataKey/data
    method: POST
    headers:
        Content-Type: 'application/json'
#        authorization: ${getPortalToken()}
        X-Tsign-Lowcode-Collection-Task-Key: $taskKeyData
        X-Tsign-Lowcode-Collection-Token: $authorizationformData
    json:
        data: $formData
        taskKey: $taskKeyData