#低代码采集-关联业务模板-第一步
variables:
    businessPreset_navId: "1523545022283709184"
#    params_relationBP1: {"params":{"formKey":"form65b315dbe4b0780e1c68c50a","presetId":"e230aa00a07cd3a9b0859d66be8fd762","relationFile":{"annexFileFiledKeys":null,"signFileFiledKeys":null},"relationContentDomains":[{"contentName":"自动化测试-文本0508","fieldKey":"input_r105pwcg"},{"contentName":"自动化测试-文本0507","fieldKey":"input_r105pwcg"}]}}
#    params_relationBP1:

request:
    url: ${ENV(esign.projectHost)}/esign-docs/collectionForm/relation/batchPreset/1
    method: POST
    headers: ${get_main_headers($businessPreset_navId)}
    json:
        params: $params_relationBP1