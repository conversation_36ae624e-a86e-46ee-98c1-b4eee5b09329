#文档模板编辑页-保存
variables:
  authorization0: "${getPortalToken()}"
  tplToken_draft:
  baseFile_draft:
  originFile_draft:
  fields_draft: [ ]
  pageFormatInfoParam_draft: null
  name_draft:
  contentId_draft:
  entityId_draft:
  json_save_draft: {
    "data": [
      {
        "baseFile": $baseFile_draft,
        "originFile": $originFile_draft,
        "fields": $fields_draft,
        "pageFormatInfoParam": $pageFormatInfoParam_draft,
        "name": $name_draft,
        "contentId": $contentId_draft,
        "entityId": $entityId_draft
      }
    ]
  }

request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/content/batch-save-draft
  method: POST
  headers:
    X-Tsign-Client-AppName: "epaas-template-front"
    X-Tsign-Client-Id: "pc"
    X-Tsign-Open-Operator-Id: "XXXtest"
    X-Tsign-Tenant-ID: "XXXtest"
    X-Tsign-Tpl-Token: $tplToken_draft
    authorization: $authorization0
    Content-Type: "application/json;charset=UTF-8"
  json: $json_save_draft