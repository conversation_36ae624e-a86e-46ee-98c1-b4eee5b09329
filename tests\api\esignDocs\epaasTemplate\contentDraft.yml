#文档模板编辑页-保存
variables:
    tplToken_content:
    entityId1:
    contentId1:
    authorization0: ${getPortalToken()}
request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/content/draft?contentId=$contentId1&entityId=$entityId1
    method: GET
    headers:
        X-Tsign-Client-AppName: "epaas-template-front"
        X-Tsign-Client-Id: "pc"
        X-Tsign-Open-Operator-Id: "XXXtest"
        X-Tsign-Tenant-ID: "XXXtest"
        X-Tsign-Tpl-Token: $tplToken_content
        authorization: $authorization0
    json: null