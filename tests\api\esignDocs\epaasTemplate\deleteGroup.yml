#文档模板编辑页-删除自定义字段
variables:
  authorization0: "${getPortalToken()}"
  tplToken_delete: ""
  groupId_delete: ""
  json_delete_field: {
    "groupId": $groupId_delete
  }

request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/custom-field/group/delete
  method: POST
  headers:
    content-type: "application/json"
    referer: "${ENV(esign.projectHost)}/epaas-file-template/set?context=&tplToken=${tplToken_delete}"
    X-Tsign-Client-AppName: "epaas-template-front"
    X-Tsign-Open-Language: "zh-CN"
    X-Tsign-Open-Operator-Id: "XXXtest"
    X-Tsign-Tenant-Id: "XXXtest"
    X-Tsign-Tpl-Token: $tplToken_delete
  json: $json_delete_field