#文档模板详情页-epaas模板对接
variables:
    tplToken_detail:
    contentId_detail:
    entityId_detail:
    authorization0: "${getPortalToken()}"
request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/content/detail?contentId=${contentId_detail}&entityId=${entityId_detail}
    method: GET
    headers:
        X-Tsign-Client-AppName: "epaas-template-front"
        X-Tsign-Client-Id: "pc"
        X-Tsign-Open-Operator-Id: "XXXtest"
        X-Tsign-Tenant-ID: "XXXtest"
        X-Tsign-Tpl-Token: $tplToken_detail
        authorization: $authorization0
    json: null