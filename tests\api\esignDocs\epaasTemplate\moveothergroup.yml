#文档模板编辑页-批量移动自定义字段
variables:
  authorization0: "${getPortalToken()}"
  tplToken_move: ""
  srcGroupId_move: ""
  targetGroupId_move: ""
  json_move_field: {
    "srcGroupId": $srcGroupId_move,
    "targetGroupId": $targetGroupId_move
  }
request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/custom-field/group/move-other-group
  method: POST
  headers:
    authorization: $authorization0
    content-type: "application/json"
    origin: "${ENV(esign.projectHost)}"
    X-Tsign-Client-AppName: "epaas-template-front"
    X-Tsign-Client-Id: "pc"
    X-Tsign-Open-Language: "zh-CN"
    X-Tsign-Open-Operator-Id: "XXXtest"
    X-Tsign-Tenant-Id: "XXXtest"
    X-Tsign-Tpl-Token: $tplToken_move
  json: $json_move_field