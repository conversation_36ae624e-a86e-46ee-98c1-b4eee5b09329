#批量发起任务
variables:
    tplToken_config:
    authorization0: "${getPortalToken()}"
request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/content/service-config
    method: GET
    headers:
        X-Tsign-Client-AppName: "epaas-template-front"
        X-Tsign-Client-Id: "pc"
        X-Tsign-Open-Operator-Id: "XXXtest"
        X-Tsign-Tenant-ID: "XXXtest"
        X-Tsign-Tpl-Token: $tplToken_config
        authorization: $authorization0
    json: null