#文档模板编辑页-批量保存草稿(签署方添加)
variables:
  authorization0: "${getPortalToken()}"
  tplToken_draft: ""
  contentId_signatory: ""
  resourceId_signatory: ""
  entityId_signatory: ""
  contentFieldId_signatory: ""
  bizId_signatory: ""
  fileName_signatory: "key30page.pdf"
  type: SIGN
  label: 签署区
  posx: 100
  posY: 200
  width: 150
  height: 150
  dateFormat: "yyyy-MM-dd"
  positionMovable: false
  page: "1"
  scope: "default"
  intervalType: null
  dateRule: "1"
  fieldId: ""
  addSealRule: "followSeal"
  json_signatory_add: {
    "data": [
      {
        "contentId": $contentId_signatory,
        "baseFile": {
          "resourceId": $resourceId_signatory,
          "fileType": "PDF"
        },
        "originFile": {
          "resourceId": $resourceId_signatory
        },
        "fields": [
          {
            "fieldId": "$fieldId",
            "label": "$label",
            "custom": false,
            "type": $type,
            "subType": null,
            "sort": 1,
            "style": {
              "font": "1",
              "fontSize": 12,
              "textColor": "#000",
              "width": $width,
              "height": $height,
              "bold": false,
              "italic": false,
              "underLine": false,
              "lineThrough": false,
              "verticalAlignment": "TOP",
              "horizontalAlignment": "LEFT",
              "styleExt": {
                "units": "px",
                "imgType": null,
                "hideTHeader": null,
                "selectLayout": null,
                "borderWidth": "1",
                "borderColor": "#000",
                "groupKey": "",
                "tickOptions": null,
                "imgCrop": null,
                "paddingLeftAndRight": "0"
              }
            },
            "settings": {
              "defaultValue": null,
              "required": true,
              "dateFormat": $dateFormat,
              "validation": {
                "type": "REGEXP",
                "pattern": ""
              },
              "selectableDataSource": [],
              "numberFormat": {
                "integerDigits": null,
                "fractionDigits": null,
                "thousandsSeparator": ""
              },
              "editable": true,
              "encryptAlgorithm": "",
              "fillLengthLimit": null,
              "overflowType": "2",
              "minFontSize": "8",
              "remarkInputType": null,
              "content": null,
              "remarkAICheck": null,
              "dateRule": $dateRule,
              "tickOptions": null,
              "configExt": {
                "cooperationerSubjectType": "",
                "icon": "epaas-icon-stamp",
                "fastCheck": "true",
                "addSealRule": "$addSealRule",
                "keyPosX": "0",
                "keyPosY": "0",
                "ext": "{}",
                "version": null,
                "mergeId": null
              },
              "sealTypes": [],
              "columnMapping": null,
              "positionMovable": $positionMovable,
              "signDatePosition": {
                "intervalType": null,
                "page": $page,
                "scope": $scope,
                "x": $posx,
                "y": $posY
              }
            },
            "options": null,
            "contentFieldId": $contentFieldId_signatory,
            "bizId": $bizId_signatory,
            "fieldKey": null,
            "fillGroupKey": "",
            "fieldValue": null,
            "defaultValue": null,
            "position": {
              "x": $posx,
              "y": $posY,
              "page": $page,
              "scope": $scope,
              "intervalType": $intervalType
            },
            "formField": false
          }
        ],
        "pageFormatInfoParam": null,
        "name": "测试文档",
        "entityId": $entityId_signatory
      }
    ]
  }

request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/content/batch-save-draft
  method: POST
  headers:
    authorization: $authorization0
    X-Tsign-Client-AppName: "epaas-template-front"
    X-Tsign-Client-Id: "pc"
    X-Tsign-Open-Language: "zh-CN"
    X-Tsign-Open-Operator-Id: "XXXtest"
    X-Tsign-Tenant-Id: "XXXtest"
    X-Tsign-Tpl-Token: $tplToken_draft
  json: $json_signatory_add