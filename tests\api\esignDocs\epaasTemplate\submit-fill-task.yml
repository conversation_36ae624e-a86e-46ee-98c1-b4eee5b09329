#填写页提交
variables:
    tplToken_submit:
    fieldId_submit:
    fillValue_submit:
    contentId_submit:
    documentId_submit:
    json_submit_fill_task: {
        "fillData": [
            {
                "fieldId": $fieldId_submit,
                "fillValue": $fillValue_submit
            }
        ],
        "contents": [
            {
              "contentId": $contentId_submit,
              "documentId": $documentId_submit
            }
        ]
    }

request:
    url: ${ENV(esign.projectHost)}/epaas-template/v1/doc-template/fill-task/submit-fill-task
    method: POST
    headers:
        X-Tsign-Client-AppName: "epaas-template-front"
        X-Tsign-Client-Id: "pc"
        X-Tsign-Open-Operator-Id: "XXXtest"
        X-Tsign-Tenant-ID: "XXXtest"
        X-Tsign-Tpl-Token: $tplToken_submit
    json: $json_submit_fill_task