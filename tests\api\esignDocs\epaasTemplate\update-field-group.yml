#文档模板编辑页-更新自定义字段分组
variables:
  authorization0: "${getPortalToken()}"
  tplToken_update: ""
  groupId_update: ""
  groupName_update: ""
  json_update_group: {
    "groupId": $groupId_update,
    "groupName": $groupName_update
  }

request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/custom-field/group/update
  method: POST
  headers:
    authorization: $authorization0
    referer: "${ENV(esign.projectHost)}/epaas-file-template/set?context=&tplToken=${tplToken_update}"
    X-Tsign-Client-AppName: "epaas-template-front"
    X-Tsign-Client-Id: "pc"
    X-Tsign-Open-Language: "zh-CN"
    X-Tsign-Open-Operator-Id: "XXXtest"
    X-Tsign-Tenant-Id: "XXXtest"
    X-Tsign-Tpl-Token: $tplToken_update
  json: $json_update_group