name: '文件上传，切割文件上传，返回fileKey相关信息'
variables:
    fileName: '${ENV(fileNamePdf)}'
    fileType: 'multipart/form-data'

request:
    url: ${ENV(esign.projectHost)}/esign-docs/fileSystem/commonUpload
    method: POST
    headers:
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
    files:
        uploadFile: [$fileName,'${getFileForUpload($fileName)}',$fileType]