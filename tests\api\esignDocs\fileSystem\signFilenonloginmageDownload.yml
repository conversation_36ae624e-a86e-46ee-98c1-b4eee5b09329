name: '图片下载'
variables:
    flowId: ""
request:
    url: ${ENV(esign.projectHost)}/esign-signs/anon/signFile/nonLogin/imageDownload
    method: POST
    headers:
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
    json:
        params:
          {
             pageNo: $pageNo,
             processId: $processId,
             fileKey: $fileKey
          }
