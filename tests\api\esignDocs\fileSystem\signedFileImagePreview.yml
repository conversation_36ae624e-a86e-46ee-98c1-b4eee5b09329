name: '图片下载'
variables:
    templateSnapshotId: ""
request:
    url: ${ENV(esign.projectHost)}/esign-docs/docFlow/owner/signedFileImagePreview
    method: POST
    headers:
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
        navId: "1523545065987903690"
    json:
        params:
          {
             fileKey: $fileKey,
             flowId: $flowId,
             templateSnapshotId: $templateSnapshotId
          }
