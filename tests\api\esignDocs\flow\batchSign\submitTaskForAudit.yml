#审批
request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/batchSign/submitTaskForAudit
  method: POST
  headers: ${gen_main_headers()}
  json:
    {
      "params": {
        "auditOpinion": "",
        "auditResult": "",
        "businessId": "$businessId",
        "carbonCopyList": [],
        "nextAssigneeList": [{
                               "nextAssignee": "$nextAssignee",
                               "nextAssigneeOrganizationCode": "$nextAssigneeOrganizationCode"
                             }],
        "nodeConfigCode": "$nodeConfigCode",
        "nodeSkip": false,
        "processDefinitionKey": "$processDefinitionKey",
        "processInstanceId": "$processInstanceId",
        "requestUrl": "http://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=$processInstanceId&bussinessId=$businessId&noWorkflowCodePageName=FQDZQS",
        "sendNotice": "0",
        "subBusinessId": "$businessId",
        "todoTaskId": "$todoTaskId",
        "variables": {},
        "projectId": 1000000
      }
    }