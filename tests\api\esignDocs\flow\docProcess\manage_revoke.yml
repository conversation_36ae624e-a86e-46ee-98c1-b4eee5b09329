#我管理的-作废电子签署
variables:
  reason1: "作废-全链路为了测试"
  authorization0: ${getPortalToken()}
  signConfigs: false
  iniDepartmentCode:  null
  iniOrganizationCode: ${ENV(sign01.main.orgCode)}
  signerInfos:
  revokeSignFiles: []
  initiatorInfo: {
    departmentCode: $iniDepartmentCode,
    organizationCode: $iniOrganizationCode
  }
  revokeSignFiles: []
request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/docProcess/manage/revoke
  method: POST
  headers: ${gen_main_headers_navId(process_manage_navId)}
  json:
    params:
      flowId: $flowId
      reason: $reason1
      signConfigs: $signConfigs
      initiatorInfo: $initiatorInfo
      signerInfos: $signerInfos
      revokeSignFiles: $revokeSignFiles