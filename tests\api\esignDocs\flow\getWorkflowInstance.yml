#查询流程实例
variables:
  account: "ceswdzxzdhyhwgd1.account"
  password: "ceswdzxzdhyhwgd1.password"
  workflowConfigCode: ${ENV(processDefinitionKey)}
  navId: "process_manage_navId"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/manage/getWorkflowInstance
  method: POST
  headers: ${gen_main_headers_for_user($account, $password, $navId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      processInstanceId: $processInstanceId,
      workflowConfigCode: $workflowConfigCode
    }
    platform:
    tenantCode:
    userCode:
