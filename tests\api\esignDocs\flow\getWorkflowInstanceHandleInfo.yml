#查询流程处理信息
variables:
  account: "ceswdzxzdhyhwgd1.account"
  password: "ceswdzxzdhyhwgd1.password"
  workflowConfigCode: ${ENV(processDefinitionKey)}
  authorization0: ${getPortalToken()}

request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/manage/getWorkflowInstanceHandleInfo
  method: POST
#  headers: ${gen_main_headers_for_user($account, $password)}
#  headers: ${gen_main_headers()}
  headers:
    Content-Type: "application/json"
    authorization: $authorization0
    X-timevale-project-id": ${ENV(esign.projectId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      processInstanceId: $processInstanceId
    }
    platform:
    tenantCode:
    userCode: