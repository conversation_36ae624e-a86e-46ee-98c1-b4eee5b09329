#查询是否暂停
variables:
  account: "ceswdzxzdhyhwgd1.account"
  password: "ceswdzxzdhyhwgd1.password"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/manage/hasSuspended
  method: POST
  headers: ${gen_main_headers_for_user($account, $password)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      processInstanceId: $processInstanceId
    }
    platform:
    tenantCode:
    userCode: