#加签
variables:
  account: "ceswdzxzdhyhwgd1.account"
  password: "ceswdzxzdhyhwgd1.password"
  insertTaskVariables: { }

request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/manage/insertTask
  method: POST
  headers: ${gen_main_headers_for_user($account, $password)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
        auditOpinion: $auditOpinion,
        behindCarbonCopyList: $behindCarbonCopyList,
        behindNextAssigneeList: $behindNextAssigneeList,
        behindSendNotice: $behindSendNotice,
        nextAssignee: $nextAssignee,
        nextAssigneeOrganizationCode: $nextAssigneeOrganizationCode,
        processInstanceId: $processInstanceId,
        requestUrl: $requestUrl,
        signType: $signType,
        variables: $insertTaskVariables
    }
    platform:
    tenantCode:
    userCode: