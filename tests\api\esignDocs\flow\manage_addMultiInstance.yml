#会签加签
request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/manage/addMultiInstance
  method: POST
  headers: ${gen_main_headers_navId(process_manage_navId)}
  json:
    {
      "params": {
        "assignee": "$assignee",
        "assigneeOrganizationCode": "$assigneeOrganizationCode",
        "beforeAssignee": "",
        "beforeAssigneeOrganizationCode": "",
        "processInstanceId": "$processInstanceId",
        "requestUrl": "http://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=$processInstanceId&bussinessId=$businessId&noWorkflowCodePageName=FQDZQS"
      }
    }