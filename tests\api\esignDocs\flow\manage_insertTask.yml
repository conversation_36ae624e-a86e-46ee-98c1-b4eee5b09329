#加签（前加签、后加签）
request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/manage/hasSuspended
  method: POST
  headers: ${gen_main_headers_navId(process_manage_navId)}
  json:
    {
      "params": {
        "auditOpinion": "",
        "behindSendNotice": "0",
        "behindNextAssigneeList": [],
        "behindCarbonCopyList": [],
        "nextAssignee": "$nextAssignee",
        "nextAssigneeOrganizationCode": "$nextAssigneeOrganizationCode",
        "processInstanceId": "$processInstanceId",
        "requestUrl": "requestUrl",
        "signType": "1",
        "variables": {}
      }
    }