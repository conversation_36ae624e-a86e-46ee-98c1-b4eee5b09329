#审核提交
variables:
  account: "ceswdzxzdhyhwgd1.account"
  password: "ceswdzxzdhyhwgd1.password"
  processDefinitionKey: ${ENV(processDefinitionKey)}
  projectId: ${ENV(esign.projectId)}
  submitVariables: { }
  subBusinessId: null
  todoTaskId: null
  nodeSkip: false
  carbonCopyList: [ ]
  businessId: null
  auditResult: ""
  auditOpinion: ""
  nodeConfigCode: ${ getNextNodeCode($processInstanceId) }

request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/batchSign/submitTaskForAudit
  method: POST
#  headers: ${gen_main_headers_for_user($account, $password)}
  headers: ${gen_main_headers()}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      auditOpinion: $auditOpinion,
      auditResult: $auditResult,
      businessId: $businessId,
      carbonCopyList: $carbonCopyList,
      nextAssigneeList: $nextAssigneeList,
      nodeConfigCode: $nodeConfigCode,
      nodeSkip: $nodeSkip,
      processDefinitionKey: $processDefinitionKey,
      processInstanceId: $processInstanceId,
      requestUrl: $requestUrl,
      sendNotice: $sendNotice,
      subBusinessId: $subBusinessId,
      todoTaskId: $todoTaskId,
      variables: $submitVariables,
      projectId: $projectId
    }
    platform:
    tenantCode:
    userCode: