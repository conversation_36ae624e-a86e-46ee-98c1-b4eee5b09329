#查询流程实例
variables:
  account: "ceswdzxzdhyhwgd1.account"
  password: "ceswdzxzdhyhwgd1.password"
  nodeConfigCode: ${getNextNodeCode($processInstanceId,$batchTemplateInitiationUuid)}
  submitVariables: {}
  processDefinitionKey: ${ENV(processDefinitionKey)}
  backfillExtOrg: null
  backfillExtUser: null




request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/batchSign/submitTaskForWrite
  method: POST
  headers: ${gen_main_headers_for_outer_user()}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      wfSubmitTaskRequest: {
        processInstanceId: $processInstanceId,
        nodeConfigCode: $nodeConfigCode,
        requestUrl: $requestUrl,
        processDefinitionKey: $processDefinitionKey,
        variables: $submitVariables,
        sendNotice: $sendNotice
      },
      fillFormValueRequest: {
        formValues: [
          {
            elementValue: $elementValue,
            elementKey: $elementKey,
            elementType: $elementType,
            elementName: $elementName,
            formatType: $formatType,
            formatRule: $formatRule,
            asSignFile: 0,
            model: $model,
            backfillExtOrg: $backfillExtOrg,
            backfillExtUser: $backfillExtUser
          }
        ],
        hasSubmit: $hasSubmit,
        templateInitiationUuid: $batchTemplateInitiationUuid
      }
    }
    platform:
    tenantCode:
    userCode: