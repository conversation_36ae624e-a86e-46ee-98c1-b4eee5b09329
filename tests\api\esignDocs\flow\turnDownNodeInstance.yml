#查询可驳回环节
variables:
  account: "ceswdzxzdhyhwgd1.account"
  password: "ceswdzxzdhyhwgd1.password"
  auditOpinion: null

request:
  url: ${ENV(esign.projectHost)}/esign-docs/flow/batchSign/turnDownNodeInstance
  method: POST
  headers: ${gen_main_headers()}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      businessId: $businessId,
      distTodoTaskId: $distTodoTaskId,
      nodeConfigCode : $nodeConfigCode,
      processInstanceId: $processInstanceId,
      requestUrl: $requestUrl,
      sendNotice: $sendNotice,
      todoTaskId: $todoTaskId,
      auditOpinion: $auditOpinion
    }
    platform:
    tenantCode:
    userCode: