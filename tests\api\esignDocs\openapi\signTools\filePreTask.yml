name: "创建签署配置（预盖章签署页面）-openapi"
variables:

    callbackUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/"
    signFileName: ${ENV(fileNamePdf)}
    fileKey: ${attachment_upload($signFileName)}
    fileKeyList:
        -   fileKey: $fileKey
    signerList:
        -   customAccountNo: ${ENV(sign01.userCode)}
            customDepartmentNo: ''
            customOrgNo: ${ENV(sign01.main.orgNo)}
            departmentCode: ''
            legalSignFlag: 1
            organizationCode: ${ENV(sign01.main.orgCode)}
            sealId: ''
            sealIdList:
                -   sealId: ${ENV(sign01.sealId)}
                    signatureType: 'PERSON-SEAL'
                -   sealId: ${ENV(org01.sealId)}
                    signatureType: 'COMMON-SEAL'
            sealTypeCode: ""
            userCode: ''
            userType: 1

    json:
        callbackUrl: $callbackUrl  #文件类型名称，支持模糊查询
        fileKeyList: $fileKeyList
        signerList: $signerList   #签署方信息集合



request:
    url: ${ENV(esign.gatewayHost)}/esign-docs/v1/documents/signTools/filePreTask
    method: POST

    headers: ${gen_openapi_post_headers_getway($json)}

    json: $json


