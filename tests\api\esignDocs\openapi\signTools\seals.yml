name: 签署区设置页面-印章列表查询接口
base_url: ${ENV(esign.projectHost)}
variables:
  batchTemplateInitiationUuid:
  params:
    organizationCode: $organizationCode
    userCode: $userCode
    fileType: $fileType
    legalSignFlag: $legalSignFlag #是否为法人签
    sealId: $sealId
    sealIdList: $sealIdList
    sealName: $sealName
    sealTypeCode: $sealTypeCode
    batchTemplateInitiationUuid: $batchTemplateInitiationUuid
  fileType: "pdf"
  legalSignFlag: 0 #非法人
  sealIdList: []
  sealId:
  sealName:
  sealTypeCode:

request:
  headers: ${gen_main_headers()}
  url: ${ENV(esign.projectHost)}/esign-docs/signTools/seals
  method: POST
  json:
     params: $params

