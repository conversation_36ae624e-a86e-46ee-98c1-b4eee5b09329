name: 创建签署配置（预盖章签署页面）
base_url: ${ENV(esign.gatewayHost)}
variables:
  businessNo:
  sealId:
  departmentCode:
  signatureType:
  fileKey: '${ENV(fileKey)}'
  customDepartmentNo:
  userCode: ${ENV(sign01.userCode)}
  customAccountNo:
  legalSignFlag: false
  organizationCode:
  sealTypeCode:
  callbackUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/"
  filePreRedirectUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/"
  expirationDate: 30
  signNode: 1
  signMode: 0
  userType: 1
  customOrgNo: ${ENV(sign01.main.orgNo)}
  body:
    businessNo: $businessNo #********-beta2新增业务编码
    callbackUrl: $callbackUrl   #文件类型名称，支持模糊查询
    filePreRedirectUrl: $filePreRedirectUrl
    expirationDate: $expirationDate #签署配置结果有效期：默认30天 支持1～365天
    signerInfos:
      - customAccountNo: $customAccountNo   #签署人编码（客户系统的用户唯一标识）
        signNode: $signNode
        signMode: $signMode
        legalSignFlag: $legalSignFlag   #是否添加法人签 false否true是 默认0
        organizationCode: $organizationCode   #签署机构编码 不为空则视为机构章
        departmentCode: $departmentCode   #个人签署不传， 发起人所在组织 若当前人在企业下，则传入企业编码，若在部门则传入
        sealTypeCode: $sealTypeCode   #指定印章类型，指定印章类型编码,多个印章类型以逗号分割
        userType: $userType   #用户类型 1内部 2相对方
        customDepartmentNo: $customDepartmentNo   #用户所属组织编号/兼职组织账号（客户系统的唯一标识）
        customOrgNo: $customOrgNo   #用户所属企业账号（客户系统的唯一标识）。不为空时，签署主体视为企业
        userCode: $userCode   #签署人编码（天印系统的用户唯一标识）
        sealInfos:
          - fileKey: $fileKey
            signConfigs:
              - sealId: $sealId   #印章id
                signatureType: $signatureType   #指定签署印章类型：PERSON-SEAL-个人印章、COMMON-SEAL-企业
request:
  headers: ${gen_openapi_post_headers_getway($body)}
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/documents/signTools/signFilePreTask
  method: post
  json: $body

  ###json结构体：
#  {
#    "callbackUrl": "",
#    "filePreRedirectUrl": "",
#    "expirationDate": "",
#    "signerInfos": [
#      {
#        "userType": "",
#        "userCode": "",
#        "customAccountNo": "",
#        "departmentCode ": "",
#        "customDepartmentNo ": "",
#        "organizationCode": "",
#        "customOrgNo": "",
#        "signNode": "",
#        "signMode": "",
#        "legalSignFlag": "",
#        "sealTypeCode": "",
#        "sealInfos": [
#          {
#            "flieKey": "",
#            "signConfigs": [
#              {
#                "signatureType": "",
#                "sealId": ""
#              }
#            ]
#          }
#        ]
#      }
#    ]
#  }

