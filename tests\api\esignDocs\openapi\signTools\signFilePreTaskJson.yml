name: 创建签署配置（预盖章签署页面）
base_url: ${ENV(esign.gatewayHost)}
variables:
  signerInfos:
  businessNo:
  callbackUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/"
  filePreRedirectUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice/"
  expirationDate: 30
  signNode: 1
  signMode: 0
  userType: 1
  customOrgNo: ${ENV(sign01.main.orgNo)}
  body:
    callbackUrl: $callbackUrl   #文件类型名称，支持模糊查询
    filePreRedirectUrl: $filePreRedirectUrl
    expirationDate: $expirationDate #签署配置结果有效期：默认30天 支持1～365天
    signerInfos: $signerInfos
    businessNo: $businessNo


request:
  headers: ${gen_openapi_post_headers_getway($body)}
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/documents/signTools/signFilePreTask
  method: post
  json: $body

  ###json结构体：
#  {
#    "callbackUrl": "",
#    "filePreRedirectUrl": "",
#    "expirationDate": "",
#    "signerInfos": [
#      {
#        "userType": "",
#        "userCode": "",
#        "customAccountNo": "",
#        "departmentCode ": "",
#        "customDepartmentNo ": "",
#        "organizationCode": "",
#        "customOrgNo": "",
#        "signNode": "",
#        "signMode": "",
#        "legalSignFlag": "",
#        "sealTypeCode": "",
#        "sealInfos": [
#          {
#            "flieKey": "",
#            "signConfigs": [
#              {
#                "signatureType": "",
#                "sealId": ""
#              }
#            ]
#          }
#        ]
#      }
#    ]
#  }

