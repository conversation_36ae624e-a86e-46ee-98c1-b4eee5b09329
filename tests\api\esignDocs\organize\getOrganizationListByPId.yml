#根据上级组织id查询组织列表
variables:
  outFlag: true

request:
  url: ${ENV(esign.projectHost)}/esign-docs/organize/getOrganizationListByPId
  method: POST
  headers: ${gen_main_headers()}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      organizationTerritory: $organizationTerritory,
      outFlag: $outFlag,
      parentOrganizationId: $parentOrganizationId
    }
    platform:
    tenantCode:
    userCode: