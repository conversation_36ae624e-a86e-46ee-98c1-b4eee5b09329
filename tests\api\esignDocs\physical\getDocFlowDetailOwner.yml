name: 我管理的-查看物理用印流程详情
base_url: ${ENV(esign.projectHost)}
variables:
  processId:
  flowId:
  flowName: ""
  startTime: ""
  endTime: ""
  userAccountNumber0: "" #${ENV(sign01.accountNo)}
  orgAccountNumber0: ""
  accountNumber: ${ENV(userCode)}
  password: ${ENV(password_ceshy)}
  navId: "1523545066701935170"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/physical/seal/owner/getDocFlowDetail?flowId=$flowId
  method: GET
  headers: ${gen_token_header_permissions($accountNumber,$password,$navId)}