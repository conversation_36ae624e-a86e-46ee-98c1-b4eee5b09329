name: 通过物理用印签署记录
variables:
  flowId:
  authorization0: ${getPortalToken()}
  code0: ${getPortalCode()}
  flowName: ""
  startTime: ""
  endTime: ""
  userAccountNumber3: "" #${ENV(sign01.accountNo)}
  orgAccountNumber3: ""
  businessNo: ""
  currentHandlerUserId: ""
  createTime: ""
  size: 10
  page: 1
  flowStatus: ""
  gmtFinishRange: ""
  gmtFinishBegin: ""
  gmtFinishEnd: ""
  projectId: ""
  signerUserName: ""
  initiatorUserName: ""
  initiatorOrganizeCode: ""
request:
  url: ${ENV(esign.projectHost)}/esign-docs/physical/seal/owner/getDocFlowLists
  method: POST
  headers:
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
    navId: '1523545066702935170'
    code: $code0
  json:
    params:
      businessNo: $businessNo
      currentHandlerUserId: $currentHandlerUserId
      createTime: $createTime
      size: 10
      page: 1
      flowId: $flowId
      flowName: $flowName
      startTime: $startTime
      endTime: $endTime
      flowStatus: $flowStatus
      userAccountNumber: $userAccountNumber3
      orgAccountNumber: $orgAccountNumber3
      gmtFinishRange: $gmtFinishRange
      gmtFinishBegin: $gmtFinishBegin
      gmtFinishEnd: $gmtFinishEnd
      projectId: $projectId
      signerUserName: $signerUserName
      initiatorUserName: $initiatorUserName
      initiatorOrganizeCode: $initiatorOrganizeCode
