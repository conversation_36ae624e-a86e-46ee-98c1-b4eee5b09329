name: 物理用印-我管理的-导出
variables:
    includeIdList: []
    excelHeadAndSequence: {
    "flowName": "流程名称",
    "flowId": "流程编号",
    "signerUserNameList": "用印人",
    "currentHandlerName": "当前环节处理人",
    "flowStatusName": "流程状态",
    "initiatorTime": "发起时间",
    "gmtFinish": "结束时间",
    "projectName": "项目名称",
    "projectId": "项目id",
    "initiatorUserName": "发起人",
    "initiatorOrganizeName": "发起人组织",
    "flowTypeName": "流程分类"
  }

request:
  url: ${ENV(esign.projectHost)}/esign-docs/physical/seal/manage/export
  method: POST
  headers: ${gen_main_headers_navId(process_manage_navId)}
  json:
    params:
      "excelHeadAndSequence": $excelHeadAndSequence
      "includeFlowIdList": $includeIdList