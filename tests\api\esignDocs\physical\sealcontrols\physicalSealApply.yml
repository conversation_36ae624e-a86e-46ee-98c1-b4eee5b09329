name: 创建物理用印流程
variables:
  - subject: openapi自动化创建物理用印流程-${get_randomNo()}
  - businessNo: ""
  - expireTime: ""
  - sealReason: "openapi自动化测试"
  - callBackUrl: "http://datafactory.smlk8s.esign.cn/simpleTools/notice"
  - redirectUrl: ""
  - onlyConsole: 0
  - applyUserCode: ${ENV(ceswdzxzdhyhwgd1.userCode)}
  - applyAccountNo: ${ENV(ceswdzxzdhyhwgd1.account)}
  - applyOrganizationCode: ${ENV(ceswdzxzdhyhwgd1.orgCode)}
  - applyOrgNo: ${ENV(ceswdzxzdhyhwgd1.orgNo)}
  - sealId: ${ENV(org01.physical.sealId)}
  - sealUserCode: ${ENV(sign01.userCode)}
  - sealAccountNo: ${ENV(sign01.accountNo)}
  - sealOrganizationCode: ${ENV(sign01.main.orgCode)}
  - sealOrgNo: ${ENV(sign01.main.orgNo)}
  - applyCount: 5
  - takeOut: 0
  - outAddress:
  - longitude:
  - latitude:
  - verifyFingerprint: 0
  - signfileKey: ${ENV(fileKey)}
  - attachmentFileKey: ${ENV(1PageFileKey)}
  - verifyQrCode: 0
  - qrFileInfos: null
  - authCodeVisibleRoles_physicalSealApply: #用印人
  - signFiles: [
        {
            "fileKey": $signfileKey
        }
    ]
  - attachments: [
        {
            "fileKey": $attachmentFileKey
        }
    ]
  - sealDetailsInfos:
      [
        {
          "sealId": $sealId,
          "sealUserCode": $sealUserCode,
          "sealAccountNo": $sealAccountNo,
          "sealOrganizationCode": $sealOrganizationCode,
          "sealOrgNo": $sealOrgNo,
          "applyCount": $applyCount,
          "takeOut": $takeOut,
          "outAddress": $outAddress,
          "longitude": $longitude,
          "latitude": $latitude,
          "verifyFingerprint": $verifyFingerprint,
          "onlyConsole": $onlyConsole
        }
      ]

  - json:
      {
        "subject": $subject,
        "businessNo": $businessNo,
        "expireTime": $expireTime,
        "sealReason": $sealReason,
        "callBackUrl": $callBackUrl,
        "redirectUrl": $redirectUrl,
        "verifyQrCode": $verifyQrCode,
        "authCodeVisibleRoles": $authCodeVisibleRoles_physicalSealApply,
        "qrFileInfos": $qrFileInfos,
        "applyUserInfo": {
          "applyUserCode": $applyUserCode,
          "applyAccountNo": $applyAccountNo,
          "applyOrganizationCode": $applyOrganizationCode,
          "applyOrgNo": $applyOrgNo
        },
        "signFiles": $signFiles,
        "attachments": $attachments,
        "sealDetailsInfos": $sealDetailsInfos
      }

request:
    url: ${ENV(esign.gatewayHost)}/esign-docs/v1/sealcontrols/physicalSealApply
    method: POST
    headers: ${gen_openapi_post_headers_getway($json)}
    json: $json