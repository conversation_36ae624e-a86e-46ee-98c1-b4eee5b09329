#查询未读通知
variables:
  account: "ceswdzxzdhyhwgd1.account"
  password: "ceswdzxzdhyhwgd1.password"
request:
  url: ${ENV(esign.projectHost)}/portal/notice/listUnreadNotice
  method: POST
  headers: ${gen_main_headers_for_user($account, $password)}
  json:
    customerIP:
    deptId:
    domain: ${ENV(portal_domain)}
    params: {
      currPage: $currPage,
      pageSize: $pageSize
    }
    platform:
    tenantCode:
    userCode: