#我已处理任务
variables:
  account: "ceswdzxzdhyhwgd1.account"
  password: "ceswdzxzdhyhwgd1.password"
  header0: "${gen_main_headers_for_user($account, $password)}"
  timeType: 2
  workflowConfigCode: ${ENV(processDefinitionKey)}
  businessIdDoneTask: ""
request:
  url: ${ENV(esign.projectHost)}/portal/task/queryDoneTask
  method: POST
  headers: $header0
  json:
    customerIP:
    deptId:
    domain: ${ENV(portal_domain)}
    params: {
      currPage: $currPage,
      pageSize: $pageSize,
      timeType: $timeType,
      businessId: $businessIdDoneTask,
      endTime: "",
      startTime: "",
      startUserName: "",
      workflowCategory: "",
      workflowConfigCode: $workflowConfigCode,
      workflowConfigName: ""
    }
    platform:
    tenantCode:
    userCode: