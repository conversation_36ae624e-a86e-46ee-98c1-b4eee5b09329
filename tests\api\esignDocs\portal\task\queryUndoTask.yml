#待我处理任务
variables:
  account: ${ENV(ceswdzxzdhyhwgd1.account)}
  password: ${ENV(ceswdzxzdhyhwgd1.password)}
  businessId: ""
  flowExtensions:
request:
  url: ${ENV(esign.projectHost)}/portal/task/queryUndoTask
  method: POST
  headers: ${gen_main_headers_for_user($account, $password)}
  json:
    customerIP:
    deptId:
    domain: "unified_portal_service"
    params: {
      currPage: $currPage,
      pageSize: $pageSize,
      businessId: $businessId,
      flowExtensions: $flowExtensions
    }
    platform:
    tenantCode:
    userCode: