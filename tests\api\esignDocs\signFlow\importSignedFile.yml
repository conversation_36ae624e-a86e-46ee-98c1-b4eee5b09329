name: 已签文件导入(open-api)
variables:
  - signFlowCreateTime:
  - signFlowEndTime:
  - signType:
  - subject:
  - signedFileKey:
  - departmentName:
  - customAccountNo:
  - customDepartmentNo:
  - organizationName:
  - userName:
  - userName1:
  - customAccountNo1:
  - departmentName1:
  - customDepartmentNo1:
  - signFiles: [
    {
      signedFileKey: $signedFileKey
    } ]
  - customOrgNo:
  - userType:
  - signerInfos: [
    {
      userType: $userType,
      userName: $userName,
      customAccountNo: $customAccountNo,
      departmentName: $departmentName,
      customDepartmentNo: $customDepartmentNo,
      organizationName: $organizationName,
      customOrgNo: $customOrgNo
    } ]
  - json:
      {
        subject:  $subject,   #流程主题
        signFlowCreateTime: $signFlowCreateTime,   #签署流程创建时间，格式：yyyy-MM-dd HH:mm:ss
        signFlowEndTime: $signFlowEndTime,   #签署流程结束时间，格式：yyyy-MM-dd HH:mm:ss
        signType: $signType,   #签署类型：1-电子签署，2-物理用印
        signFiles: $signFiles,
        initiatorInfo:
          {
            userName: $userName1,
            customAccountNo: $customAccountNo1,
            departmentName: $departmentName1,
            customDepartmentNo: $customDepartmentNo1
          },
        signerInfos: $signerInfos
      }
request:
#  headers: ${gen_main_headers()}
  headers: ${gen_headers_signature($json)}
  url: ${ENV(esign.gatewayHost)}/esign-signs/esign-docs/v1/signFlow/importSignedFile
  method: post
  json: $json