#openapi 作废(open-api)
name: 作废
variables:
  - businessNo: ""
  - signFlowId: ""
  - signConfigs: ""
  - reason: "测试作废原因"
  - customAccountNo_init:
  - customDepartmentNo_init:
  - customOrgNo_init:
  - departmentCode_init:
  - organizationCode_init:
  - userCode_init:
  - CCInfos: []
  - customAccountNo_cc:
  - customDepartmentNo_cc:
  - customOrgNo_cc:
  - departmentCode_cc:
  - organizationCode_cc:
  - userCode_cc:
  - customAccountNo_signer:
  - customDepartmentNo_signer:
  - customOrgNo_signer:
  - departmentCode_signer:
  - organizationCode_signer:
  - userCode_signer:
  - userType_signer:
  - initiatorInfo: {
    "customAccountNo": $customAccountNo_init,
    "customDepartmentNo": $customDepartmentNo_init,
    "customOrgNo": $customOrgNo_init,
    "departmentCode": $departmentCode_init,
    "organizationCode": $organizationCode_init,
    "userCode": $userCode_init,
    "userType": 1
  }
  - CCInfos:  [
    {
      "customAccountNo": $customAccountNo_cc,
      "customDepartmentNo": $customDepartmentNo_cc,
      "customOrgNo": $customOrgNo_cc,
      "departmentCode": $departmentCode_cc,
      "organizationCode": $organizationCode_cc,
      "userCode": $userCode_cc,
      "userType": 1
    }
  ]
  - signerInfos: [
    {
      "customAccountNo": $customAccountNo_signer,
      "customDepartmentNo": $customDepartmentNo_signer,
      "customOrgNo": $customOrgNo_signer,
      "departmentCode": $departmentCode_signer,
      "organizationCode": $organizationCode_signer,
      "userCode": $userCode_signer,
      "userType": $userType_signer
    }
  ]
  - revokeSignFiles: []
  - json: {
    "businessNo": $businessNo,
    "signFlowId": $signFlowId,
    "signConfigs": $signConfigs,
    "reason": $reason,
    "revokeSignFiles": $revokeSignFiles,
    "initiatorInfo": $initiatorInfo,
    "CCInfos": $CCInfos,
    "signerInfos": $signerInfos
  }
request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/signFlow/revoke
  method: POST
  headers: ${gen_openapi_post_headers_getway($json)}
  json: $json
