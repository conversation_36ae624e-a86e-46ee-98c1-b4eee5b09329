# 更新签署流程接口定义
name: 更新签署流程

variables:
  - signFlowId: ""
  - businessNo: ""
  - signFlowExpireTime: ""
  - contractExpirationTime: ""
  - customAccountNo_init: ""
  - customDepartmentNo_init: ""
  - customOrgNo_init: ""
  - departmentCode_init: ""
  - organizationCode_init: ""
  - userCode_init: ""
  - initiatorInfo: {
    "customAccountNo": $customAccountNo_init,
    "customDepartmentNo": $customDepartmentNo_init,
    "customOrgNo": $customOrgNo_init,
    "departmentCode": $departmentCode_init,
    "organizationCode": $organizationCode_init,
    "userCode": $userCode_init,
    "userType": 1
  }
  - json: {
    "signFlowId": $signFlowId,
    "businessNo": $businessNo,
    "signFlowExpireTime": $signFlowExpireTime,
    "contractExpirationTime": $contractExpirationTime
  }

request:
  url: ${ENV(esign.gatewayHost)}/esign-docs/v1/signFlow/update
  method: POST
  headers: ${gen_openapi_post_headers_getway($json)}
  json: $json
