#签署中心-创建并直接发起流程（一步发起）
variables:
  subject: "文档自动化发起流程${get_randomNo_32()}"
  params:
      CCInfos:
      advertisement: true
      appRedirectInfo:
      attachments:
      businessNo:
      businessTypeCode: ${ENV(businessTypeCode)}
      chargingType: 1
      initiatorInfo:
         customAccountNo: ""
         customDepartmentNo: ""
         customOrgNo: ""
         departmentCode: ${ENV(ceswdzxzdhyhwgd1.orgCode)}
         organizationCode: ${ENV(ceswdzxzdhyhwgd1.orgCode)}
         userCode: ${ENV(ceswdzxzdhyhwgd1.userCode)}
         userType: 1
      readComplete: false
      redirectUrl: ""
      remark: "文档自动化测试备注"
      signFiles: $signFiles
      signFlowExpireTime: ""
      signNotifyUrl: ""
      signerInfos: $signerInfos
      subject: $subject


request:
  url: ${ENV(esign.gatewayHost)}/esign-signs/v1/signFlow/createAndStart
  method: POST
  headers: ${gen_openapi_post_headers_getway($params)}
  json: $params
