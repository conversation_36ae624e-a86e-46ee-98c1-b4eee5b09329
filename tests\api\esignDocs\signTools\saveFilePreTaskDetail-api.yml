name: "保存签署区配置接口"
variables:
    signFileName: ${ENV(fileNamePdf)}
    fileKey: ${attachment_upload($signFileName)}
    filePreTaskId: '19b6be9339eb428bb66a55143791c523'
    sealInfos:
        -   fileKey: $fileKey
            signConfigs:
                -   addSignDate: false
                    keywordInfo: null
                    pageNo: '1-2'
                    posX: 62
                    posY: 779
                    edgeScope: null
                    sealSignDatePositionInfo: null
                    signPosName: 测试签署一-esigntest自动化签署中心CI测试
                    signType: COMMON-SIGN
                    signatureType: COMMON-SEAL

                -   addSignDate: false
                    keywordInfo: null
                    pageNo: ''
                    posX: 531
                    posY: 779
                    edgeScope: null
                    sealSignDatePositionInfo: null
                    signPosName: 测试签署一-esigntest自动化签署中心CI测试
                    signType: COMMON-SIGN
                    signatureType: LEGAL-PERSON-SEAL

                -   addSignDate: false
                    keywordInfo: null
                    pageNo: '1'
                    posX: 531
                    posY: 62
                    edgeScope: null
                    sealSignDatePositionInfo: null
                    sealId: ''
                    signPosName: 测试签署一-esigntest自动化签署中心CI测试
                    signType: COMMON-SIGN
                    signatureType: PERSON-SEAL

                -   addSignDate: false
                    keywordInfo: null
                    pageNo: '1-2'
                    posX: null
                    posY: 543
                    edgeScope: null
                    sealSignDatePositionInfo: null
                    signPosName: 测试签署一-esigntest自动化签署中心CI测试
                    signType: EDGE-SIGN
                    signatureType: COMMON-SEAL
    signerId: '${departmentCode}:${customAccountNo}'
    userType: 1
    userCode: ${ENV(sign01.userCode)}
    userName: '测试签署一'
    customAccountNo: ${ENV(sign01.userCode)}
    organizationCode: ${ENV(sign01.main.orgCode)}
    organizationName: ${ENV(sign01.main.orgName)}
    customOrgNo: ${ENV(sign01.main.orgNo)}
    signerType: 2
    departmentCode: '58bec88e506142b0bf05342c68518827'
    departmentName: 'esigntest自动化签署中心CI测试'
    customDepartmentNo: ''
    legalSignFlag: True
    sealTypeCode: ''
    sealId:
    sealIdList:
        -   signatureType: PERSON-SEAL
            sealId: ${ENV(sign01.sealId)}


request:
    url: ${ENV(esign.projectHost)}/esign-docs/signTools/saveFilePreTaskDetail
    method: POST

    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}

    json:
        params:
            filePreTaskId: $filePreTaskId

            filePreTaskInfos:
                -   sealInfos: $sealInfos
                    signerId: $signerId
                    userType: $userType
                    userCode: $userCode
                    userName: $userName
                    customAccountNo: $customAccountNo
                    organizationCode: $organizationCode
                    organizationName: $organizationName
                    customOrgNo: $customOrgNo
                    signerType: $signerType
                    departmentCode: $departmentCode
                    departmentName: $departmentName
                    customDepartmentNo: $customDepartmentNo
                    legalSignFlag: $legalSignFlag
                    sealTypeCode: $sealTypeCode
                    sealId: $sealId
                    sealIdList: $sealIdList



