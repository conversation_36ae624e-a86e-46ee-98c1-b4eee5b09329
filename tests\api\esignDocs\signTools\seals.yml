name: "签署区设置  落章筛选印章可用项目和自动落章"
variables:
   authorization0: ${getPortalToken()}
   sealTypeCode: null
   organizationCode:
   userCode:
   sealIdList: []
   fileType: pdf
   batchTemplateInitiationUuid:
   autoSign: 0
   json:
     params: {
       "sealTypeCode": $sealTypeCode,
       "organizationCode": $organizationCode,
       "userCode": $userCode,
       "sealIdList": $sealIdList,
       "fileType": $fileType,
       "batchTemplateInitiationUuid": $batchTemplateInitiationUuid,
       "autoSign": $autoSign
     }
request:
    url: ${ENV(esign.projectHost)}/esign-docs/signTools/seals
    method: POST
    headers:
      Content-Type: "application/json"
      Authorization: $authorization0
      X-timevale-project-id: ${ENV(esign.projectId)}
      Navid: "1532247977769439252"
    json: $json

