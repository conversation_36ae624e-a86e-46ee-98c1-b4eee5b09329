#已签署文件流程列表
variables:
  authorization0: ${getPortalToken()}

request:
  url: ${ENV(esign.projectHost)}/esign-docs/signedFileProcess/manage/detail
  method: POST
#  headers: ${gen_main_headers_navId(${ENV(signedFile_manage_navId)})}
  headers:
    Content-Type: "application/json"
    Authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
    Navid: "1532247977769439252"
  json:
    {
      "params": {
        "signedFileProcessUuid": "$signedFileProcessUuid",
        "detailSource": 0
      }
    }