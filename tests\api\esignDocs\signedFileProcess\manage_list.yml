name: 已签署文件流程列表
variables:
  initiatorAccountNumber5:
  flowExtensions:
  userAccountNumber5: ""
  orgAccountNumber5: ""
  businessNo: ""
  dynamicCode: ""
  excludeSignedFileProcessUuidList: ""
  fileName: ""
  flowName: ""
  gmtSignFinishEnd: ""
  gmtSignFinishStart: ""
  includeSignedFileProcessUuidList: ""
  initiatorOrganizeCode: ""
  initiatorUserName: ""
  page: 1
  processId: ""
  signMode: ""
  signOrgName: ""
  signedFileProcessUuid: ""
  size: 10
  viewType: 1
  data:
    businessNo: $businessNo
    flowExtensions: $flowExtensions
    dynamicCode: $dynamicCode
#    excludeSignedFileProcessUuidList: $excludeSignedFileProcessUuidList
    fileName: $fileName
    flowName: $flowName
    gmtSignFinishEnd: $gmtSignFinishEnd
    gmtSignFinishStart: $gmtSignFinishStart
#    includeSignedFileProcessUuidList: $includeSignedFileProcessUuidList
    initiatorOrganizeCode: $initiatorOrganizeCode
    initiatorAccountNumber: $initiatorAccountNumber5
    initiatorUserName: $initiatorUserName
    page: $page
    processId: $processId
    signMode: $signMode
    signOrgName: $signOrgName
    signedFileProcessUuid: $signedFileProcessUuid
    signerUserName: $signedFileProcessUuid
    size: $size
    viewType: $viewType
    userAccountNumber: $userAccountNumber5
    orgAccountNumber: $orgAccountNumber5
  authorization0: ${getPortalToken()}
  code0: ${getPortalCode()}
request:
  url: ${ENV(esign.projectHost)}/esign-docs/signedFileProcess/manage/list
  method: POST
  headers:
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
    navId: '1532247977769439252'
    code: $code0
  json:
    params: $data