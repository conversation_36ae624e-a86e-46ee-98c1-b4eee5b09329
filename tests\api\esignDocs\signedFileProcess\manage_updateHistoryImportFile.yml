#已签署文件流程列表
request:
  url: ${ENV(esign.projectHost)}/esign-docs/signedFileProcess/manage/updateHistoryImportFile
  method: POST
  headers: ${gen_main_headers_navId(signedFile_manage_navId)}
  json:
    {
      "params": {
        "flowName": "$flowName",
        "gmtSignFinish": "$gmtSignFinish",
        "gmtSignInitiate": "$gmtSignInitiate",
        "initiatorOrganizeName": "$initiatorOrganizeName",
        "initiatorUserCode": "$initiatorUserCode",
        "initiatorUserName": "$initiatorUserName",
        "signInfoRequestList": [{
                                  "id": $id0,
                                  "uuid": "$uuid0",
                                  "signedFileProcessId": $signedFileProcessId0,
                                  "userCode": "$userCode0",
                                  "userName": "$userName0",
                                  "departmentName": "$departmentName0",
                                  "departmentCode": "$departmentCode0",
                                  "organizeCode": "$organizeCode0",
                                  "organizeName": "$organizeName0",
                                  "userType": $userType0,
                                  "signerType": $signerType0
                                }],
        "signedFileProcessUuid": "$signedFileProcessUuid"
      }
    }