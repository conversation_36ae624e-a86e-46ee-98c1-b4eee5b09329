#已签署文件流程列表
variables:
  authorization0: ${getPortalToken()}

request:
  url: ${ENV(esign.projectHost)}/esign-docs/signedFileProcess/owner/detail
  method: POST
  headers:
    Content-Type: "application/json"
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
    navId: "1532247977769439262"
  json:
    {
      "params": {
        "signedFileProcessUuid": "$signedFileProcessUuid",
        "detailSource": 0
      }
    }