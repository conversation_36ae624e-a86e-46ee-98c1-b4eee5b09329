#已签署文件流程列表
name: 已签署文件流程列表
variables:
  flowExtensions:
  userAccountNumber6: ""
  orgAccountNumber6: ""
  fileName: ""
  flowName: ""
  initiatorUserName: ""
  initiatorOrganizeName: ""
  signerUserName: ""
  signOrgName: ""
  gmtSignFinishStart: ""
  gmtSignFinishEnd: ""
  signMode: ""
#  includeSignedFileProcessUuidList: ""
#  excludeSignedFileProcessUuidList: ""
  page: 1
  size: 10
  viewType:
  processId:
  account0:
  password0:
  authorization0: ${getPortalToken()}
request:
  url: ${ENV(esign.projectHost)}/esign-docs/signedFileProcess/owner/list
  method: POST
#  headers: ${gen_main_headers_navId(signedFile_mine_navId)}
  headers:
    Navid: '1532247977769439262'
    Content-Type: 'application/json'
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      "fileName": $fileName,
      "flowName": $flowName,
      "initiatorUserName": $initiatorUserName,
      "initiatorOrganizeName": $initiatorOrganizeName,
      "signerUserName": $signerUserName,
      "signOrgName": $signOrgName,
      "gmtSignFinishStart": $gmtSignFinishStart,
      "gmtSignFinishEnd": $gmtSignFinishEnd,
      "signMode": $signMode,
#      "includeSignedFileProcessUuidList": $includeSignedFileProcessUuidList,
#      "excludeSignedFileProcessUuidList": $excludeSignedFileProcessUuidList,
      "page": $page,
      "size": $size,
      "viewType": $viewType,
      "processId": $processId,
      "userAccountNumber": $userAccountNumber6,
      "orgAccountNumber": $orgAccountNumber6,
      "flowExtensions": $flowExtensions
    }
    platform:
    tenantCode:
    userCode: