#获取已签署流程列表
request:
  url: ${ENV(esign.projectHost)}/esign-docs/signedFileProcess/list
  method: POST
  headers: ${gen_main_headers_navId(signedFileNavId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      fileName: $fileName,
      flowName: $flowName,
      initiatorUserName: $initiatorUserName,
      initiatorOrganizeName: $initiatorOrganizeName,
      signerUserName: $signerUserName,
      signOrgName: $signOrgName,
      gmtSignFinishStart: $gmtSignFinishStart,
      gmtSignFinishEnd: $gmtSignFinishEnd,
      signMode: $signMode,
      page: $page,
      size: $size
    }
    platform:
    tenantCode:
    userCode: