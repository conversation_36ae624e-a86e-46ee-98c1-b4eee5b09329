#添加已签署文件下载任务
variables:
    flowName:
    gmtSignFinishStart: "2022-08-01 00:00:00"
    gmtSignFinishEnd: "2022-09-13 23:59:59"
    fileName:
    initiatorUserName:
    initiatorOrganizeName:
    signerUserName:
    signOrgName:
    signMode:
    includeSignedFileProcessUuidList:
    excludeSignedFileProcessUuidList:
    signedFileProcessUuid:
    page: 1
    size: 10
    viewType: 3
request:
    url: ${ENV(esign.projectHost)}/esign-docs/signedFile/manage/addTask
    method: POST
        #    headers: ${gen_main_headers_navId(signedFile_manage_navId)}
    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
        navId: ${ENV(signedFile_manage_navId)}
    json:
        customerIP:
        deptId:
        domain:
        params:
            fileName: $fileName
            flowName: $flowName
            initiatorUserName: $initiatorUserName
            initiatorOrganizeName: $initiatorOrganizeName
            signerUserName: $signerUserName
            signOrgName: $signOrgName
            gmtSignFinishStart: $gmtSignFinishStart
            gmtSignFinishEnd: $gmtSignFinishEnd
            signMode: $signMode
            includeSignedFileProcessUuidList: $includeSignedFileProcessUuidList
            excludeSignedFileProcessUuidList: $excludeSignedFileProcessUuidList
            page: $page
            size: $size
            signedFileProcessUuid: $signedFileProcessUuid
            viewType: $viewType

        platform:
        tenantCode:
        userCode: