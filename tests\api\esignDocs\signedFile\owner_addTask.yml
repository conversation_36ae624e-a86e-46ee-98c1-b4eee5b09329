#我发起的-添加已签署文件下载任务
variables:
  account0:
  password0:
  authorization0: ${getPortalToken($account0,$password0)}
request:
  url: ${ENV(esign.projectHost)}/esign-docs/signedFile/owner/addTask
  method: POST
  headers:
    Navid: '1532247977769439262'
    Content-Type: 'application/json'
    authorization: "$authorization0"
    X-timevale-project-id: ${ENV(esign.projectId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      "fileName": $fileName,
      "flowName": $flowName,
      "initiatorUserName": $initiatorUserName,
      "initiatorOrganizeName": $initiatorOrganizeName,
      "signerUserName": $signerUserName,
      "signOrgName": $signOrgName,
      "gmtSignFinishStart": $gmtSignFinishStart,
      "gmtSignFinishEnd": $gmtSignFinishEnd,
      "signMode": $signMode,
      "includeSignedFileProcessUuidList": $includeSignedFileProcessUuidList,
      "excludeSignedFileProcessUuidList": $excludeSignedFileProcessUuidList,
      "page": $page,
      "size": $size,
      "signedFileProcessUuid": $signedFileProcessUuid,
    }
    platform:
    tenantCode:
    userCode: