name: 已签署文件-我的文件-导出明细
variables:
  includeSignedFileProcessUuidListOwner: []
  excelHeadAndSequenceOwner:  {
      "flowName": "流程名称",
      "processId": "流程编号",
      "initiatorUserName": "发起人",
      "initiatorOrganizeName": "发起人组织",
      "gmtSignInitiate": "签署发起时间",
      "signMode": "签署方式",
      "signerOrgUserVOList": "签署方",
      "gmtSignFinish": "签署完成时间",
      "projectName": "项目名称",
      "projectId": "项目id"
    }

request:
  url: ${ENV(esign.projectHost)}/esign-docs/signedFile/owner/export
  method: POST
  headers: ${gen_main_headers(signedFile_manage_navId)}
  json:
    params:
      "includeSignedFileProcessUuidList": $includeSignedFileProcessUuidListOwner
      "excelHeadAndSequence": $excelHeadAndSequenceOwner