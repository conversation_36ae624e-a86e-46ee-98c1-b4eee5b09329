#模版添加内容域
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/content_domain/add
  method: POST
  headers: ${gen_main_headers()}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      contents: [
        {
        contentUuid: $contentUuid1,
        contentCode: $contentCode1,
        contentName: $contentName1,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid2,
        contentCode: $contentCode2,
        contentName: $contentName2,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid3,
        contentCode: $contentCode3,
        contentName: $contentName3,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid4,
        contentCode: $contentCode4,
        contentName: $contentName4,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid5,
        contentCode: $contentCode5,
        contentName: $contentName5,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid6,
        contentCode: $contentCode6,
        contentName: $contentName6,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid7,
        contentCode: $contentCode7,
        contentName: $contentName7,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid8,
        contentCode: $contentCode8,
        contentName: $contentName8,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid9,
        contentCode: $contentCode9,
        contentName: $contentName9,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid10,
        contentCode: $contentCode10,
        contentName: $contentName10,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid11,
        contentCode: $contentCode11,
        contentName: $contentName11,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid12,
        contentCode: $contentCode12,
        contentName: $contentName12,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid13,
        contentCode: $contentCode13,
        contentName: $contentName13,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid14,
        contentCode: $contentCode14,
        contentName: $contentName14,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid15,
        contentCode: $contentCode15,
        contentName: $contentName15,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid16,
        contentCode: $contentCode16,
        contentName: $contentName16,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid17,
        contentCode: $contentCode17,
        contentName: $contentName17,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid18,
        contentCode: $contentCode18,
        contentName: $contentName18,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid19,
        contentCode: $contentCode19,
        contentName: $contentName19,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid20,
        contentCode: $contentCode20,
        contentName: $contentName20,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid21,
        contentCode: $contentCode21,
        contentName: $contentName21,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid22,
        contentCode: $contentCode22,
        contentName: $contentName22,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid23,
        contentCode: $contentCode23,
        contentName: $contentName23,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid24,
        contentCode: $contentCode24,
        contentName: $contentName24,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid25,
        contentCode: $contentCode25,
        contentName: $contentName25,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid26,
        contentCode: $contentCode26,
        contentName: $contentName26,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid27,
        contentCode: $contentCode27,
        contentName: $contentName27,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid28,
        contentCode: $contentCode28,
        contentName: $contentName28,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid29,
        contentCode: $contentCode29,
        contentName: $contentName29,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid30,
        contentCode: $contentCode30,
        contentName: $contentName30,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid31,
        contentCode: $contentCode31,
        contentName: $contentName31,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid32,
        contentCode: $contentCode32,
        contentName: $contentName32,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid33,
        contentCode: $contentCode33,
        contentName: $contentName33,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid34,
        contentCode: $contentCode34,
        contentName: $contentName34,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid35,
        contentCode: $contentCode35,
        contentName: $contentName35,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid36,
        contentCode: $contentCode36,
        contentName: $contentName36,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid37,
        contentCode: $contentCode37,
        contentName: $contentName37,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid38,
        contentCode: $contentCode38,
        contentName: $contentName38,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid39,
        contentCode: $contentCode39,
        contentName: $contentName39,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid40,
        contentCode: $contentCode40,
        contentName: $contentName40,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid41,
        contentCode: $contentCode41,
        contentName: $contentName41,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid42,
        contentCode: $contentCode42,
        contentName: $contentName42,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid43,
        contentCode: $contentCode43,
        contentName: $contentName43,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid44,
        contentCode: $contentCode44,
        contentName: $contentName44,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid45,
        contentCode: $contentCode45,
        contentName: $contentName45,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid46,
        contentCode: $contentCode46,
        contentName: $contentName46,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid47,
        contentCode: $contentCode47,
        contentName: $contentName47,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid48,
        contentCode: $contentCode48,
        contentName: $contentName48,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid49,
        contentCode: $contentCode49,
        contentName: $contentName49,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      },{
        contentUuid: $contentUuid50,
        contentCode: $contentCode50,
        contentName: $contentName50,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      }
      ]
    }
    platform:
    tenantCode:
    userCode:

