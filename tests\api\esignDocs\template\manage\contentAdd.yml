#模版添加内容域
variables:
    template_manage_navId_key: "template_manage_navId"
    dataSource: ""
    sourceField: ""
    font: "SimSun"
    fontSize: 16
    fontColor: "BLACK"
    fontStyle: "Normal"
    textAlign: "Left"
    formatType: 0
    formatRule: ""
    required: 0
    length: 11
    pageNo: 1
    posX: 200
    posY: 200
    width: 200
    height: 50

request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/content_domain/add
  method: POST
  headers: ${gen_main_headers_navId($template_manage_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      contents: [
        {
        contentUuid: $contentUuid,
        contentCode: $contentCode,
        contentName: $contentName,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      }
      ]
    }
    platform:
    tenantCode:
    userCode:

