#模版删除内容域
variables:
    template_manage_navId_key: "template_manage_navId"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/content_domain/del
  method: POST
  headers: ${gen_main_headers_navId($template_manage_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      templateContentUuid: $templateContentUuid
    }
    platform:
    tenantCode:
    userCode:

