#pdf模版添加内容域
variables:
    template_manage_navId_key: "template_manage_navId"
    templateUuid:
    version: 1
    templateContentUuid:
    contentUuid:
    contentCode:
    contentName:
    description:
    dataSource:
    sourceField:
    font: "SimSun"
    fontSize: "12"
    fontColor: "BLACK"
    fontStyle: "Normal"
    textAlign: "Left"
    leaveGroup: true
    formatType:
    formatRule: ""
    required: 0
    length: 11
    edgeScope:
    pageNo: "1"
    posX: 200
    posY: 300
    width: 56
    height: 36
    multipliedLeading: 1
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/content_domain/update
  method: POST
  headers: ${gen_main_headers_navId($template_manage_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      templateContentUuid: $templateContentUuid,
      contentUuid: $contentUuid,
      contentCode: $contentCode,
      contentName: $contentName,
      description: $description,
      dataSource: $dataSource,
      sourceField: $sourceField,
      font: $font,
      fontSize: $fontSize,
      fontColor: $fontColor,
      fontStyle: $fontStyle,
      textAlign: $textAlign,
      formatType: $formatType,
      leaveGroup: $leaveGroup,
      formatRule: $formatRule,
      required: $required,
      length: $length,
      multipliedLeading:$multipliedLeading,
      position: {
        edgeScope: $edgeScope,
        pageNo: $pageNo,
        posX: $posX,
        posY: $posY,
        width: $width,
        height: $height
      }
    }
    platform:
    tenantCode:
    userCode:

