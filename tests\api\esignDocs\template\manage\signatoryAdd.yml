#模版添加内容域
variables:
    template_manage_navId_key: "template_manage_navId"
    timePosX: 100
    timePosY: 100
    allowMove: false
    name: "pos1"
    signType: 2
    addSignTime: 0
    dateFormat: "yyyy-MM-dd"
    edgeScope: null
    pageNo: "1"
    posX: 100
    posY: 100
    signatories: [
        {
        name: $name,
        signType: $signType,
        addSignTime: $addSignTime,
        dateFormat: $dateFormat,
        edgeScope: $edgeScope,
        pageNo: $pageNo,
        posX: $posX,
        posY: $posY,
        timePosX: $timePosX,
        timePosY: $timePosY,
        allowMove: $allowMove
      }
      ]

request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/signatory_domain/add
  method: POST
  headers: ${gen_main_headers_navId($template_manage_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      signatories: $signatories
    }
    platform:
    tenantCode:
    userCode:

