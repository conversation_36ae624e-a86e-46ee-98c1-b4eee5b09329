#模板列表
variables:
  template_manage_navId_key: "template_manage_navId"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/manage/list
  method: POST
  headers: ${gen_main_headers_navId($template_manage_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      docUuid: $docUuid,
      templateName: $templateName,
      standard: $standard,
      status: $status,
      minSignCount: $minSignCount,
      page: $page,
      size: $size
    }
    platform:
    tenantCode:
    userCode: