#新增版本
variables:
  template_manage_navId_key: "template_manage_navId"
  editUrl:
  originFileKey:
  docUid:
  fileName:
  fileType:
  templateType:
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/manage/new
  method: POST
  headers: ${gen_main_headers_navId($template_manage_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      sourceTemplateUuid: $sourceTemplateUuid,
      sourceVersion: $sourceVersion,
      fileKey: $fileKey,
      zipFileKey: $zipFileKey,
      templateName: $templateName,
      createUserOrg: $createUserOrg,
      description: $description,
      docUuid: $docUuid,
      allRange: $allRange,
      organizeRange: $organizeRange,
      editUrl: $editUrl,
      originFileKey: $originFileKey,
      docUid: $docUid,
      fileName: $fileName,
      fileType: $fileType,
      templateType: $templateType
    }
    platform:
    tenantCode:
    userCode:

