#保存并发布pdf模版-我管理的
variables:
  template_manage_navId_key: "template_manage_navId"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/manage/saveOrPublish
  method: POST
#  headers: ${gen_main_headers_navId($template_manage_navId_key)}

  headers:
    Content-Type: 'application/json'
    authorization: ${getPortalToken()}
    X-timevale-project-id: ${ENV(esign.projectId)}
    navId: ${ENV(template_manage_navId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      isPublish: $isPublish
    }
    platform:
    tenantCode:
    userCode:

