#编辑模板
variables:
  template_manage_navId_key: "template_manage_navId"
  templateUuid: ""
  version: ""
  fileKey: ""
  zipFileKey: ""
  templateName: ""
  createUserOrg: ${ENV(sign01.main.orgCode)}
  description: "自动化模板更新"
  docUuid: ""
  allRange: 1
  organizeRange: [ ]
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/manage/update
  method: POST
  headers: ${gen_main_headers_navId($template_manage_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      fileKey: $fileKey,
      zipFileKey: $zipFileKey,
      templateName: $templateName,
      createUserOrg: $createUserOrg,
      description: $description,
      docUuid: $docUuid,
      allRange: $allRange,
      organizeRange: $organizeRange
    }
    platform:
    tenantCode:
    userCode:

