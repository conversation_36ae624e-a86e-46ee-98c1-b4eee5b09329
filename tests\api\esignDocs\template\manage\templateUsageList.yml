#模板列表
variables:
  template_manage_navId_key: "template_manage_navId"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/manage/usage_list
  method: POST
  headers: ${gen_main_headers_navId($template_manage_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      templateName: $templateName,
      initiationUser: $initiationUser,
      beginUsageTime: $beginUsageTime,
      endUsageTime: $endUsageTime,
      page: $page,
      size: $size
    }
    platform:
    tenantCode:
    userCode: