#word模版发布-我管理的
variables:
  template_manage_navId_key: "template_manage_navId"
  addSignTime: 0
  allowMove: false
  edgeScope:
  keywordOrder: ""
  keywordType: 0
  name: "甲方企业"
  signType: 1
  thirdKey: "ele-1685069041333"

  contents: [ ]
  fileKey:
  isPublish: True
  templateUuid:
  version: 1
  signatories: [
    addSignTime: $addSignTime,
    allowMove: $allowMove,
    edgeScope: $edgeScope,
    keywordOrder: $keywordOrder,
    keywordType: $keywordType,
    name: $name,
    signType: $signType,
    thirdKey: $thirdKey
  ]
  json:
    params:
      contents: $contents
      fileKey: $fileKey
      isPublish: $isPublish
      signatories: $signatories
      templateUuid: $templateUuid
      version: $version
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/word/manage/saveOrPublish
  method: POST
#  headers: ${gen_main_headers_navId($template_manage_navId_key)}

  headers:
    Content-Type: 'application/json'
    authorization: ${getPortalToken()}
    X-timevale-project-id: ${ENV(esign.projectId)}
    navId: ${ENV(template_manage_navId_key)}
  json: $json