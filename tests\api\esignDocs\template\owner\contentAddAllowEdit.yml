#pdf模板添加内容域
variables:
  template_mine_navId_key: "template_mine_navId"
  required: 0
  encrypted: ""
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/content_domain/add
  method: POST
  headers: ${gen_main_headers_navId($template_mine_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      contents: [
        {
        contentUuid: $contentUuid,
        contentCode: $contentCode,
        contentName: $contentName,
        description: $description,
        dataSource: $dataSource,
        sourceField: $sourceField,
        font: $font,
        fontSize: $fontSize,
        fontColor: $fontColor,
        fontStyle: $fontStyle,
        textAlign: $textAlign,
        formatType: $formatType,
        formatRule: $formatRule,
        required: $required,
        length: $length,
        encrypted: $encrypted,
        allowEdit: $allowEdit,
        defaultContentValue: $defaultContentValue,
        position: {
          edgeScope: 0,
          pageNo: $pageNo,
          posX: $posX,
          posY: $posY,
          width: $width,
          height: $height
        }
      }
      ]
    }
    platform:
    tenantCode:
    userCode:

