#模版添加内容域
variables:
  template_mine_navId_key: "template_mine_navId"
  encrypted: ""
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/content_domain/update
  method: POST
  headers: ${gen_main_headers_navId($template_mine_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      templateContentUuid: $templateContentUuid,
      contentUuid: $contentUuid,
      contentCode: $contentCode,
      contentName: $contentName,
      description: $description,
      encrypted: $encrypted,
      dataSource: $dataSource,
      sourceField: $sourceField,
      font: $font,
      fontSize: $fontSize,
      fontColor: $fontColor,
      fontStyle: $fontStyle,
      textAlign: $textAlign,
      formatType: $formatType,
      formatRule: $formatRule,
      required: $required,
      length: $length,
      position: {
        edgeScope: $edgeScope,
        pageNo: $pageNo,
        posX: $posX,
        posY: $posY,
        width: $width,
        height: $height
      }
    }
    platform:
    tenantCode:
    userCode:

