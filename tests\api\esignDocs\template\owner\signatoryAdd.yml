#pdf模版添加签名域
variables:
  template_mine_navId_key: "template_mine_navId"
  allowMove: false
  remarkSignatory:
  signFieldType: 0
  areaType: 0 #根据印章尺寸自适应
  addSignTime: 0
  dateFormat: "yyyy-MM-dd"
  timePosX: 400
  timePosY: 700
  width:
  height:
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/signatory_domain/add
  method: POST
  headers: ${gen_main_headers_navId($template_mine_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      signatories: [
        {
        name: $name,
        signType: $signType, #签署方式:1 single单页签章,2 multi多页签章, 3 edges签骑缝章
        addSignTime: $addSignTime, #是否添加签署时间;0否，1是
        dateFormat: $dateFormat,
        edgeScope: $edgeScope,
        pageNo: $pageNo,
        posX: $posX,
        posY: $posY,
        timePosX: $timePosX,
        timePosY: $timePosY,
        allowMove: $allowMove,
        remarkSignatory: $remarkSignatory,
        signFieldType: $signFieldType,
        width: $width,
        height: $height,
        areaType: $areaType #"签署区区域类型 0 - 根据印章尺寸自适应 1 - 自定义 默认为 0"
      }
      ]
    }
    platform:
    tenantCode:
    userCode:

