#新增模板
variables:
    fileKey:
    zipFileKey:
    description: '测试脚本模板说明'
    templateName: '自动化测试模版${getDateTime()}'
#    createUserOrg: ${ENV(ceswdzxzdhyhwgd1.orgCode)}
    createUserOrg: ${ENV(sign01.main.orgCode)}
    templateUuid:
    templateType: 1  # 模版类型，1 pdf，2 word
    version:
    docUuid: ${get_docConfig_type()}
    allRange: 1
    organizeRange: []
    account0: ${ENV(sign01.userCode)}
    password0: ${ENV(passwordEncrypt)}
    authorization0: ${getPortalToken($account0,$password0)}
request:
    url: ${ENV(esign.projectHost)}/esign-docs/template/owner/add
    method: POST
    #  headers: ${gen_main_headers_navId($template_mine_navId_key)}

    headers:
        Content-Type: 'application/json'
        authorization: $authorization0
        X-timevale-project-id: ${ENV(esign.projectId)}
        navId: ${ENV(template_mine_navId)}
    json:
        customerIP:
        deptId:
        domain:
        params: {
            fileKey: $fileKey,
            zipFileKey: $zipFileKey,
            templateName: $templateName,
            templateType: $templateType,
            createUserOrg: $createUserOrg,
            description: $description,
            docUuid: $docUuid,
            allRange: $allRange,
            organizeRange: $organizeRange
        }
        platform:
        tenantCode:
        userCode:

