#复制模板
variables:
  template_mine_navId_key: "template_mine_navId"
  editUrl:
  originFileKey:
  docUid:
  fileName:
  fileType:
  templateType:
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/owner/copy
  method: POST
  headers: ${gen_main_headers_navId($template_mine_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      sourceTemplateUuid: $sourceTemplateUuid,
      sourceVersion: $sourceVersion,
      fileKey: $fileKey,
      zipFileKey: $zipFileKey,
      templateName: $templateName,
      createUserOrg: $createUserOrg,
      description: $description,
      docUuid: $docUuid,
      allRange: $allRange,
      organizeRange: $organizeRange,
      editUrl: $editUrl,
      originFileKey: $originFileKey,
      docUid: $docUid,
      fileName: $fileName,
      fileType: $fileType,
      templateType: $templateType
    }
    platform:
    tenantCode:
    userCode:

