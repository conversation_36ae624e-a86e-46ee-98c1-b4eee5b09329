variables:
  template_mine_navId_key: "template_mine_navId"
  account0: ${ENV(sign01.userCode)}
  password0: ${ENV(passwordEncrypt)}
  authorization0: ${getPortalToken($account0,$password0)}
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/owner/info
  method: POST
#  headers: ${gen_main_headers_navId($template_mine_navId_key)}

  headers:
    Navid: '1523545065937572842'
    Content-Type: 'application/json'
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version
    }
    platform:
    tenantCode:
    userCode: