#模板列表
variables:
  template_mine_navId_key: "template_mine_navId"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/owner/list
  method: POST
#  headers: ${gen_main_headers_navId($template_mine_navId_key)}

  headers:
    Content-Type: 'application/json'
    authorization: ${getPortalToken()}
    X-timevale-project-id: ${ENV(esign.projectId)}
    navId: ${ENV(template_mine_navId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      docUuid: $docUuid,
      templateName: $templateName,
      standard: $standard,
      status: $status,
      minSignCount: $minSignCount,
      page: $page,
      size: $size
    }
    platform:
    tenantCode:
    userCode: