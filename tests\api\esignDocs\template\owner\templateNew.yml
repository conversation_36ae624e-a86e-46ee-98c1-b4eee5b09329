#新增版本
variables:
  template_mine_navId_key: "template_mine_navId"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/owner/new
  method: POST
  headers: ${gen_main_headers_navId($template_mine_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      sourceTemplateUuid: $sourceTemplateUuid,
      sourceVersion: $sourceVersion,
      fileKey: $fileKey,
      zipFileKey: $zipFileKey,
      templateName: $templateName,
      createUserOrg: $createUserOrg,
      description: $description,
      docUuid: $docUuid,
      allRange: $allRange,
      organizeRange: $organizeRange
    }
    platform:
    tenantCode:
    userCode:

