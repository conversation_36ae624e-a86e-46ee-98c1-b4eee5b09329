#保存并发布pdf模板-我制作的
variables:
  template_mine_navId_key: "template_mine_navId"
  account0: ${ENV(sign01.userCode)}
  password0: ${ENV(passwordEncrypt)}
  authorization0: ${getPortalToken($account0,$password0)}
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/owner/saveOrPublish
  method: POST
#  headers: ${gen_main_headers_navId($template_mine_navId_key)}

  headers:
    Content-Type: 'application/json'
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
    navId: ${ENV(template_mine_navId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      isPublish: $isPublish
    }
    platform:
    tenantCode:
    userCode:

