#模板列表，在用，owner和manage也有
variables:
  template_mine_navId_key: "template_mine_navId"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/owner/list
  method: POST
  headers: ${gen_main_headers_navId($template_mine_navId_key)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      docUuid: $docUuid,
      templateName: $templateName,
      standard: $standard,
      status: $status,
      minSignCount: $minSignCount,
      page: $page,
      size: $size
    }
    platform:
    tenantCode:
    userCode: