#停用模板
variables:
  account0:
  password0:
  authorization0: ${getPortalToken($account0,$password0)}
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/owner/suspend
  method: POST
  headers:
    Navid: '1523545065937572842'
    Content-Type: 'application/json'
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
    }
    platform:
    tenantCode:
    userCode:

