#编辑模板
variables:
  account0: ${ENV(sign01.userCode)}
  password0: ${ENV(passwordEncrypt)}
  authorization0: ${getPortalToken($account0,$password0)}
  templateUuid: ""
  version: ""
  fileKey: ""
  zipFileKey: ""
  templateName: ""
  createUserOrg: ${ENV(sign01.main.orgCode)}
  description: "自动化模板更新"
  docUuid: ""
  allRange: 1
  organizeRange: []
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/owner/update
  method: POST
  headers:
    Navid: '1523545065937572842'
    Content-Type: 'application/json'
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid,
      version: $version,
      fileKey: $fileKey,
      zipFileKey: $zipFileKey,
      templateName: $templateName,
      createUserOrg: $createUserOrg,
      description: $description,
      docUuid: $docUuid,
      allRange: $allRange,
      organizeRange: $organizeRange
    }
    platform:
    tenantCode:
    userCode:

