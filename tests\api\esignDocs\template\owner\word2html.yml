name: 'word转化为html'
variables:
    fileName: '${ENV(fileNameDocx)}'
    fileType: 'multipart/form-data'
request:
    url: ${ENV(esign.projectHost)}/esign-docs/template/convertor/owner/word2html
    method: POST
    headers:
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
        navId: ${ENV(template_mine_navId)}
    files:
        uploadFile: [$fileName,'${getFileForUpload($fileName)}',$fileType]

