#word模版发布-我制作的
variables:
    addSignTime: 0
    allowMove: false
    edgeScope:
    keywordOrder: ""
    keywordType: 0
    name: "甲方企业"
    signType: 1
    thirdKey: "ele-1685069041333"
    pageHeaderAndFooterData: null
    pageMarginsData: null

    contents: []
    fileKey:
    isPublish: True
    templateUuid:
    version: 1
    signatories: [
      {
                     addSignTime: $addSignTime,
                     allowMove: $allowMove,
                     edgeScope: $edgeScope,
                     keywordOrder: $keywordOrder,
                     keywordType: $keywordType,
                     name: $name,
                     signType: $signType,
                     thirdKey: $thirdKey
    }
    ]
    json:
        params:
            contents: $contents
            fileKey: $fileKey
            isPublish: $isPublish
            signatories: $signatories
            templateUuid: $templateUuid
            version: $version
            pageHeaderAndFooterData: $pageHeaderAndFooterData
            pageMarginsData: $pageMarginsData
request:
    url: ${ENV(esign.projectHost)}/esign-docs/template/word/owner/saveOrPublish
    method: POST
    #  headers: ${gen_main_headers_navId($template_mine_navId_key)}

    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
        navId: ${ENV(template_mine_navId)}
    json: $json