#word文档模板预览
variables:
  fileKeyPW: ${ENV(fileKey)}
  readSignatoriesPW: True
  thirdKey:
  headerFileKey:
  footerFileKey:
  signatoriesPW:
    - addSignTime: 1
      dateFormat: "yyyy-MM-dd"
      edgeScope: None
      pageNo: None
      signType: 1
      thirdKey: $thirdKey
      name: "甲方企业"
      allowMove: True
  pageHeaderAndFooterDataPW:
    headerIsShow: True
    footerIsShow: True
    pageNumAtHeader: True
    pageNumAtFooter: True
    headerFileKey: $headerFileKey
    footerFileKey: $footerFileKey
  pageMarginsDataPW:
    marginTop: 1
    marginBottom: 1
    marginLeft: "2.54"
    marginRight: "2.54"
    topMinHeight: "2.54"
    bottomMinHeight: "2.54"
    type: "custom"
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/convertor/word/preview
  method: POST
  headers: ${gen_main_headers_navId($template_mine_navId_key)}
  json:
    params:
      fileKey: $fileKeyPW
      readSignatories: $readSignatoriesPW
      signatories: $signatoriesPW
      pageHeaderAndFooterData: $pageHeaderAndFooterDataPW
      pageMarginsData: $pageMarginsDataPW

