#epaas文档模板填写页面接口
variables:
    tplToken_savefilldata:
    contents_savefilldata:
    fillData_savefilldata:
    authorization0: "${getPortalToken()}"
    data_savefilldata:
      contents: $contents_savefilldata
      fillData: $fillData_savefilldata
request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/fill-task/save-fill-data
  method: POST
  headers:
      X-Tsign-Client-AppName: "epaas-template-front"
      X-Tsign-Client-Id: "pc"
      X-Tsign-Open-Operator-Id: "XXXtest"
      X-Tsign-Tenant-ID: "XXXtest"
      X-Tsign-Tpl-Token: $tplToken_savefilldata
      authorization: $authorization0
  json: $data_savefilldata