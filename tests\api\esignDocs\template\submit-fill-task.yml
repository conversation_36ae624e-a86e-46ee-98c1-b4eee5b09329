#epaas文档模板填写页面接口
variables:
    tplToken_submitfilltask:
    authorization0: "${getPortalToken()}"
    contents_submitfilltask:
    fillData_submitfilltask:
    data_submitfilltask:
      contents: $contents_submitfilltask
      fillData: $fillData_submitfilltask
request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/doc-template/fill-task/submit-fill-task
  method: POST
  headers:
      X-Tsign-Client-AppName: "epaas-template-front"
      X-Tsign-Client-Id: "pc"
      X-Tsign-Open-Operator-Id: "XXXtest"
      X-Tsign-Tenant-ID: "XXXtest"
      X-Tsign-Tpl-Token: $tplToken_submitfilltask
      authorization: $authorization0
  json: $data_submitfilltask