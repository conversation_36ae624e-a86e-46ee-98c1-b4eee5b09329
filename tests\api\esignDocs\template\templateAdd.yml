#添加文件夹和文件类型
variables:
  authorization0: ${getPortalToken()}
  fileKey:
  zipFileKey:
  createUserOrg:
  description:
  docUuid:
  allRange: 1
  organizeRange: []
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/owner/add
  method: POST
  headers:
    authorization: $authorization0
    X-timevale-project-id: ${ENV(esign.projectId)}
    Content-Type: "application/json"
  json:
    params: {
      fileKey: $fileKey,
      zipFileKey: $zipFileKey,
      templateName: $templateName,
      createUserOrg: $createUserOrg,
      description: $description,
      docUuid: $docUuid,
      allRange: $allRange,
      organizeRange: $organizeRange
    }

