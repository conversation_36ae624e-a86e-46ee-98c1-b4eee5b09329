name: '获取模板列表'
variables:
    template_mine_navId_key: "template_mine_navId"
    docUuid:
    templateName: '自动化测试通用模版'
    standard: 1           #标准模板 0否 1是
    status: 'PUBLISH'   #模板状态:DRAFT-草稿,PUBLISH-发布,SUSPEND-停用, 可用值:AUDIT,DELETED,DRAFT,INIT,PUBLISH,SUSPEND,
    page: 1
    size: 5
request:
    url: ${ENV(esign.projectHost)}/esign-docs/template/list
    method: POST

    headers:
        Content-Type: 'application/json'
        authorization: ${getPortalToken()}
        X-timevale-project-id: ${ENV(esign.projectId)}
        navId: ${ENV(template_mine_navId_key)}
    json:
        customerIP:
        deptId:
        domain:
        params:
            docUuid: $docUuid
            templateName: $templateName
            standard: $standard
            status: $status
            page: $page
            size: $size

        platform:
        tenantCode:
        userCode: