#openapi生成模板预览页面后，模板预览调用的接口
variables:
  account: ${ENV(sign01.userCode)}
  password: ${ENV(password01)}
  phoneOrMail: ""
request:
  url: ${ENV(esign.projectHost)}/esign-docs/template/template-view
  method: POST
  headers: ${gen_main_headers_for_interirl_or_external($account, $password, $phoneOrMail)}
  json:
    customerIP:
    deptId:
    domain:
    params: {
      templateUuid: $templateUuid
    }
    platform:
    tenantCode:
    userCode: