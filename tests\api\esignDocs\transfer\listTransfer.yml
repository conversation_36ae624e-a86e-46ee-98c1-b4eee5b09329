#获取转交列表
variables:
    authorization_tranfer_list: ${getPortalToken()}
    flowId_list:
    flowStatus_list: ""
    dataType_list: ""
    transferType_list: 1
    timestamp_list: ""
    flowName_list: ""
    signerOrganizationName_list: ""
    enterpriseName_list: ""
    sealGroupName_list: ""
    sealName_list: ""
    pageSize_list: 30
    pageNo_list: 1
    transferUserCode_list: ""
    taskId_list: ""
    docName_list: ""
    status_list: ""
    templateName_list: ""
    templateUuid_list: ""
    businessTypeId_list: ""
    presetName_list: ""
    fileFormat_list: ""
request:
  url: ${ENV(esign.projectHost)}/esign-docs/transfer/list
  method: POST
  headers:
    authorization: $authorization_tranfer_list
    X-timevale-project-id: ${ENV(esign.projectId)}
    navid: "1764924302865543170"
  json:
    params:
      flowId: $flowId_list
      flowStatus: $flowStatus_list
      dataType: $dataType_list
      transferType: $transferType_list
      transferUserCode: $transferUserCode_list
      timestamp: $timestamp_list
      flowName: $flowName_list
      signerOrganizationName: $signerOrganizationName_list
      enterpriseName: $enterpriseName_list
      sealGroupName: $sealGroupName_list
      sealName: $sealName_list
      pageSize: $pageSize_list
      pageNo: $pageNo_list
      taskId: $taskId_list
      docName: $docName_list
      status: $status_list
      templateName: $templateName_list
      templateUuid: $templateUuid_list
      businessTypeId: $businessTypeId_list
      presetName: $presetName_list
      fileFormat: $fileFormat_list


