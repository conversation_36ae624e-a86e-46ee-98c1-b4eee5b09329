#用户转交记录
variables:
    authorization_record: ${getPortalToken()}
    transferTime_record: ""
    receiverUserName_record: ${ENV(sign01.userName)}
    transferType_record: 1
    dataType_record: ""
    transferUserName_record: ${ENV(sign01.userName)}
    flowName_record: ""
    flowId_record: ""
    signerOrganizationName_record: ""
    enterpriseName_record: ""
    sealGroupName_record: ""
    sealName_record: ""
    pageNo_record: 1
    pageSize_record: 10
    startTime_record: ""
    templateName_record: ""
    templateUuid_record: ""
    transferIdentity_record: ""
    businessTypeId_record: ""
    presetName_record: ""
    fileFormat_record: ""
request:
  url: ${ENV(esign.projectHost)}/esign-docs/transfer/record
  method: POST
  headers:
    authorization: $authorization_record
    X-timevale-project-id: ${ENV(esign.projectId)}
    navid: "1764924302865543170"
  json:
    params:
      transferTime: $transferTime_record
      receiverUserName: $receiverUserName_record
      transferType: $transferType_record
      dataType: $dataType_record
      transferUserName: $transferUserName_record
      flowName: $flowName_record
      flowId: $flowId_record
      signerOrganizationName: $signerOrganizationName_record
      enterpriseName: $enterpriseName_record
      sealGroupName: $sealGroupName_record
      sealName: $sealName_record
      pageNo: $pageNo_record
      pageSize: $pageSize_record
      startTime: $startTime_record
      templateName: $templateName_record
      templateUuid: $templateUuid_record
      transferIdentity: $transferIdentity_record
      businessTypeId: $businessTypeId_record
      presetName: $presetName_record
      fileFormat: $fileFormat_record


