#用户数据转交提交接口
variables:
    authorization_submit: ${getPortalToken()}
    endTime_record: "${getDateTime(0,2)} 23:59:59" #默认查询的结束时间为当天的23点
    transferUserCode_submit: ${ENV(sign01.userCode)}
    receiverUserCode_submit: ${ENV(csqs.userCode)}
    timestamp_submit: "${getDateTime(0)}"
    taskId_submit: ""
    receiverDepartmentCode_submit: "${ENV(csqs.orgCode)}"
    electronicSign_submit:
      all: 0  #0-全部不选 1-all  2-部分选择
      excludeIdList: [ ]
      includeIdList: [ ]
    enterpriseSeal_submit:
      all: 0
      excludeIdList: [ ]
      includeIdList: [ ]
    businessPreset_submit:
      all: 0
      excludeIdList: [ ]
      includeIdList: [ ]
    docTemplate_submit:
      all: 0
      excludeIdList: [ ]
      includeIdList: [ ]
    electronicSignProcess_submit:
      all: 0
      excludeIdList: [ ]
      includeIdList: [ ]
    importSignedProcess_submit:
      all: 0
      excludeIdList: [ ]
      includeIdList: [ ]
    autoSignProcess_submit:
      all: 0
      excludeIdList: [ ]
      includeIdList: [ ]
    params_submit:
      electronicSign: $electronicSign_submit
      enterpriseSeal: $enterpriseSeal_submit
      businessPreset: $businessPreset_submit
      docTemplate: $docTemplate_submit
      electronicSignProcess: $electronicSignProcess_submit
      importSignedProcess: $importSignedProcess_submit
      autoSignProcess: $autoSignProcess_submit
      transferUserCode: $transferUserCode_submit
      receiverUserCode: $receiverUserCode_submit
      receiverDepartmentCode: $receiverDepartmentCode_submit
      timestamp: $timestamp_submit
      taskId: $taskId_submit
request:
  url: ${ENV(esign.projectHost)}/esign-docs/transfer/submit
  method: POST
  headers:
    authorization: $authorization_submit
    X-timevale-project-id: ${ENV(esign.projectId)}
    navid: "1764924302865543170"
  json:
    params: $params_submit