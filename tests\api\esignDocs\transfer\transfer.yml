#获取转交列表
variables:
    authorization_transfer: ${getPortalToken()}
    enterpriseSeal_transfer:
      all: 0
    businessPreset_transfer:
      all: 0
    docTemplate_transfer:
      all: 0
    electronicSignProcess_transfer:
      all: 0
    importSignedProcess_transfer:
      all: 0
    autoSignProcess_transfer:
      all: 0
    transferUserCode_transfer: ${ENV(sign01.userCode)}
    receiverUserCode_transfer: ${ENV(csqs.userCode)}
    taskId_transfer: ""
    receiverDepartmentCode_transfer: "${ENV(csqs.orgCode)}"
    electronicSign_transfer:
      all: 0  #0-全部不选 1-all  2-部分选择
      excludeIdList: []
      includeIdList:
    params_transfer:
      electronicSign: $electronicSign_transfer
      enterpriseSeal: $enterpriseSeal_transfer
      businessPreset: $businessPreset_transfer
      docTemplate: $docTemplate_transfer
      electronicSignProcess: $electronicSignProcess_transfer
      importSignedProcess: $importSignedProcess_transfer
      autoSignProcess: $autoSignProcess_transfer
      transferUserCode: $transferUserCode_transfer
      receiverUserCode: $receiverUserCode_transfer
      receiverDepartmentCode: $receiverDepartmentCode_transfer
      taskId: $taskId_transfer
request:
  url: ${ENV(esign.projectHost)}/esign-docs/transfer/resignation/transfer
  method: POST
  headers:
    authorization: $authorization_transfer
    X-timevale-project-id: ${ENV(esign.projectId)}
    navid: "1764924302865543170"
  json:
    params: $params_transfer


