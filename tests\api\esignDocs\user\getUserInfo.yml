#获取用户列表
    variables:
      fileFormat: 1
      sealBodyStructure: 1
      sealType: ""
      userName: ""
      userType: ""
    request:
      url: ${ENV(esign.projectHost)}/esign-docs/user/getUserInfo
      method: POST
      headers: ${gen_main_headers()}
      json:
          customerIP:
          deptId:
          domain:
          params: {
            "fileFormat": $fileFormat,
            "sealBodyStructure": $sealBodyStructure,
            "sealType": $sealType,
            "userName": $userName,
            "userType": $userType
          }
          platform:
          tenantCode:
          userCode: