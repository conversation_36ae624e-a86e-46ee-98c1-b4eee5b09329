#获取用户信息-匹配范围为管理平台分配数据权限范围内的用户姓名
    request:
      url: ${ENV(esign.projectHost)}/esign-docs/user/getUserInfoWithPermissions
      method: POST
#      headers: ${gen_main_headers()}
      headers: ${gen_main_headers_navId(process_manage_navId)}
      json:
          customerIP:
          deptId:
          domain:
          params: {
            "userName": $userName
          }
          platform:
          tenantCode:
          userCode: