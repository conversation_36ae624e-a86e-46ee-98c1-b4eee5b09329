#通过组织id获取用户列表
    request:
      url: ${ENV(esign.projectHost)}/esign-docs/user/getUserListByOrganizationID
      method: POST
      headers: ${gen_main_headers()}
      json:
          customerIP:
          deptId:
          domain:
          params: {
            allChildOrganizationFlag: $allChildOrganizationFlag,
            userName: $userName,
            organizationId: $organizationId,
#            accountNumber: $accountNumber,
#            organizationCode: $organizationCode,
#            organizationName: $organizationName,
#            userCode: $userCode,
#            userStatusList: $userStatusList,
#            userTerritory: $userTerritory
          }
          platform:
          tenantCode:
          userCode:

