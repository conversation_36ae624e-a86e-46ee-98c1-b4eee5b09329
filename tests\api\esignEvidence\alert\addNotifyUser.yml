name: 预警管理-新增预警通知对象
variables:
    notifyType: 1 添加的是用户，2是手机号，3是邮箱号
    notifyObject: 类型1对应userCode 类型2手机号和类型3对应的邮箱(需要加密)
    accountNumber: ${ENV(csqs.accountNo)}
    password: ${ENV(passwordEncrypt)}
request:
    url: ${ENV(esign.projectHost)}/evidence/alert/addNotifyUser
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            notifyType: $notifyType
            notifyObject: $notifyObject
        domain: "evidence_system"