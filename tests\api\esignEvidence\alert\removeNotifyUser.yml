name: 预警管理-删除预警通知对象
variables:
    notifyType: 1 是用户，2是手机号，3是邮箱号
    notifyObject: 类型1对应 userCode 类型2是手机号(加密的)和类型3邮箱号(加密的)
    accountNumber: ${ENV(csqs.accountNo)}
    password: ${ENV(passwordEncrypt)}
request:
    url: ${ENV(esign.projectHost)}/evidence/alert/removeNotifyUser
    method: POST
    headers: ${gen_token_header_permissions($accountNumber, $password)}

    json:
        params:
            notifyType: $notifyType
            notifyObject: $notifyObject
        domain: "evidence_system"