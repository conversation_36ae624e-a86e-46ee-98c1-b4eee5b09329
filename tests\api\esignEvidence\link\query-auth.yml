name: 证据管理API-分页获取证据链数据
variables:
    currPage: 1
    bizId:
    pageSize: 10
    startTime:
    endTime:
    signContractName:
    signer:
    accountNumber: ${get_data(query, userCode)}
    password: ${get_data(query, password)}
    linkTemplateCode: ${get_data(query, linkTemplateCode)}
    authorization0: ${getPortalToken()}
    codeAuth0: ${getPortalCode()}
    project0: ${ENV(esign.projectId)}
request:
    url: ${ENV(esign.projectHost)}/evidence/link/query
    method: POST
    headers:
        Content-Type: 'application/json'
        authorization: $authorization0
        X-timevale-project-id: $project0
        code: $codeAuth0
        navId: "1523545067669819394"
    json:
        params:
            currPage: $currPage
            bizId: $bizId
            pageSize: $pageSize
            finishSignStartDate: $startTime
            finishSignEndDate: $endTime
            linkTemplateCode: $linkTemplateCode
            signContractName: $signContractName
            signer: $signer
        domain: "evidence_system"

