name: 通过流程编号查询存证数据
variables:
  linkTemplateCode: 'signFlow'
  navId: "1523545067669819394"
#  authorization0: ${getPortalToken()}
#  code0: ${getPortalCode()}
request:
  url: ${ENV(esign.projectHost)}/evidence/link/query
  method: POST
  headers:
      Content-Type: 'application/json'
      authorization: ${getPortalToken()}
      X-timevale-project-id: ${ENV(esign.projectId)}
      code: ${getPortalCode()}
      navId: $navId
  json:
    params:
      pageSize: 10
      currPage: 1
      linkTemplateCode: $linkTemplateCode
      bizId: $bizId
      finishSignStartDate:
      finishSignEndDate:
    domain: evidence_system
