#功能描述：
#上传文件，支持普通文件和加密文件。
#
#支持文件类型：pdf、ofd、doc、docx、xls、xlsx、jpg、jpeg、png、bmp、gif、zip、rar
#
#请求鉴权方案：对body中的部分字段（除了file字段不用进行鉴权，其他字段都需要鉴权）进行签名鉴权
name: 文件直传接口
variables:
#    fileName: '${ENV(fileNamePdf)}'
#    fileType: 'multipart/form-data'
#    fileContent: '${getFileForUpload($fileName)}'
#    hash: '${calculate_sha256(${ENV(fileNamePdf)})}'
    # 拆分需要签名的参数和文件参数
    file: [ $fileName, $fileContent, $fileType ]
    filePwd: ""
    signParams:
        hash: $hash
        filePwd: $filePwd


request:
    url: ${ENV(esign.gatewayHost)}/file/v1/uploadWithAuth
    method: POST
    headers:
        X-timevale-project-id: ${ENV(esign.projectId)}
        x-timevale-signature: ${get_signature_with_fileUpload($signParams)}
    files:
        file: $file
    data:
        hash: $hash
        filePwd: $filePwd




