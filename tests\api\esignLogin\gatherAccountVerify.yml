name: 采集账号校验
variables:
  account:
  platform: "pc"
  accountType: 4
  verificationCode: "9999"
  dynamicCode: "123456"
  taskKey: "from_opposite_register_page"
  userCode: ""
request:
  url: ${ENV(esign.projectOuterHost)}/sso/user/gatherAccountVerify
  method: POST
  json:
    account: $account
    accountType: $accountType
    dynamicCode: $dynamicCode
    platform: $platform
    referer: ""
    taskKey: $taskKey
    userCode: $userCode
    verificationCode: $verificationCode
  headers:
    Content-Type: application/json
    verificationCode: $headerVerificationCode