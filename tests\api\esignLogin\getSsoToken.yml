name: 通过手机号或是邮箱登录
variables:
  accessToken:
  clientId: ${ENV(esign.projectId)}
  loginAccount:
  redirectUrl: "http://"
  userType:
request:
  url: ${ENV(esign.projectHost)}/etl-integrate/v1/client/getSsoToken
  method: POST
  json:
    accessToken: $accessToken
    clientId: $clientId
    domain: "portal"
    loginAccount: $loginAccount
    redirectUrl: $redirectUrl
    userType: $userType
  headers:
    Content-Type: application/json