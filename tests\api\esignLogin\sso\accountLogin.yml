variables:
    base_url: ${ENV(esign.projectHost)}
    verificationCode: ${ENV(verificationCode)}
    verificationCodeHeader: ${get_verificationCode1()}
request:
        url: ${ENV(esign.gatewayHost)}/sso/accountLogin
        method: POST
        json:
            account: $account
            password: $password
            platform: $platform
            referer: ""
            verificationCode: $verificationCode
            type: $type
        headers:
            Content-Type: application/json
            verificationCode: $verificationCodeHeader