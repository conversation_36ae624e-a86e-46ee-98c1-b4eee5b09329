variables:
    base_url: ${ENV(esign.projectHost)}
    account:
    password:
    platform: "pc"
    target: "${ENV(esign.projectHost)}/main-index-web/home"
    verificationCode: ${ENV(verificationCode)}
    headerVerificationCode: ${get_verificationCode1()}
request:
    url: ${ENV(esign.gatewayHost)}/sso/doubleFactor/loginVerify
    method: POST
    json:
        account: $account
        password: $password
        platform: $platform
        target: $target
        verificationCode: $verificationCode
    headers:
        Content-Type: application/json
        verificationCode: $headerVerificationCode