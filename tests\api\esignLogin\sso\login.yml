variables:
    base_url: ${ENV(esign.projectHost)}
request:
        url: ${ENV(esign.gatewayHost)}/sso/login
        method: POST
        json:
            account: $account
            accountType: $accountType
            dynamicCode: $dynamicCode
            platform: $platform
            referer: ""
            userCode: $userCode
            verificationCode: $verificationCode
            userTerritory: $userTerritory
        headers:
            Content-Type: application/json
            verificationCode: $headerVerificationCode