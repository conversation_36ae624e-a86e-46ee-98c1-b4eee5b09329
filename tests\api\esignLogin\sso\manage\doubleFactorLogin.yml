variables:
    base_url: ${ENV(esign.projectHost)}
    account:
    accountType:
    dynamicCode:
request:
        url: ${ENV(esign.gatewayHost)}/sso/manage/doubleFactor/login
        method: POST
        json:
            account: $account
            accountType: $accountType
            dynamicCode: $dynamicCode
            domain: "admin_platform"
            platform: "pc"
            userTerritory: null
            verificationCode: ""
        headers:
            Content-Type: application/json