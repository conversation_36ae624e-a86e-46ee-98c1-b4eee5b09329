variables:
    base_url: ${ENV(esign.projectHost)}
    account:
    accountType:
    password:
    verificationCode: ${ENV(verificationCode)}
    headerVerificationCode: ${get_verificationCode1()}
request:
    url: ${ENV(esign.gatewayHost)}/sso/manage/doubleFactor/loginVerify
    method: POST
    json:
        account: $account
        accountType: $accountType
        password: $password
        platform: "pc"
        referer: ""
        target: "2"
        verificationCode: $verificationCode
    headers:
        Content-Type: application/json
        verificationCode: $headerVerificationCode