variables:
    base_url: ${ENV(esign.projectHost)}
request:
        url: ${ENV(esign.gatewayHost)}/sso/manage/sendDynamicCode
        method: POST
        json:
            account: $account
            accountType: $accountType
            domain: "admin_plateform"
            platform: "PC"
            scene: $scene
            verificationCode: $verificationCode
            userTerritory: $userTerritory
        headers:
            Content-Type: application/json
            verificationCode: $headerVerificationCode