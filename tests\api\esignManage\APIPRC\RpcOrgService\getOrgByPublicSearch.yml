name: 根据organizationCode获取本组织信息
variables:
  data: {"domain": "rpc_request",
         "params":
           {
             "class":"cn.esign.ka.manage.facade.model.orguser.org.RpcGetOrgByPublicSearchRequestVO",
             "publicSearchType": "$publicSearchType",
             "publicSearch": "$publicSearch",
             "logicDeleted": "$logicDeleted"
           },
         "tenantCode":"1000"}
request:
  url: cn.esign.ka.manage.facade.service.orguser.org.RpcOrgService#getOrgByPublicSearch      # 必填项，由服务名称+“#”+方法名组成，主要用于排查错误原因
  servicename: cn.esign.ka.manage.facade.service.orguser.org.RpcOrgService                    #必填项，服务名称
  methodname: getOrgByPublicSearch                                                                  #必填项, 方法名称
  method: DUBBO                                                                                          #必填项，请求协议分类，用于区别http协议
  ip: ${get_ip()}                                                                             #必填项，服务器ip
  port: 22222                                                                                            #必填项，服务器端口默认22222
  json: $data
