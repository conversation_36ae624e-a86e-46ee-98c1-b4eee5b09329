name: 新增用户组组员
variables:
  data: {"domain": "rpc_request",
         "params":
           {
             "class":"cn.esign.ka.manage.facade.model.login.RpcCreateTokenRequestVO",
             "accountNumber":"$accountNumber",
             "ip": "${ENV(rpc.request.ip)}",
             "browser": "telnet",
             "customOSName": "windows",

           },
         "tenantCode":"1000"}
request:
  url: cn.esign.ka.manage.facade.service.login.RpcTokenManageService#createToken     # 必填项，由服务名称+“#”+方法名组成，主要用于排查错误原因
  servicename: cn.esign.ka.manage.facade.service.login.RpcTokenManageService             #必填项，服务名称
  methodname: createToken                                                                  #必填项, 方法名称
  method: DUBBO                                                                                          #必填项，请求协议分类，用于区别http协议
  ip: ${get_ip()}                                                                             #必填项，服务器ip
  port: 22222                                                                                            #必填项，服务器端口默认22222
  json: $data
