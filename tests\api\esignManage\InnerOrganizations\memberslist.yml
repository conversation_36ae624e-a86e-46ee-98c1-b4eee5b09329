name: 查询内部组织成员列表

variables:
  data: {
    organizationCode: $organizationCode,
    customOrgNo: $customOrgNo,
    queryScope: $queryScope,
    pageNo: $pageNo,
    pageSize: $pageSize,
  }

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/innerOrganizations/members/list
  method: POST
  headers:
      Content-Type: 'application/json'
      x-timevale-project-id: ${ENV(esign.projectId)}
      x-timevale-signature: ${getSignature($data)}
  json: $data
