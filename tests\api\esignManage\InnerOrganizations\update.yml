name: 更新内部组织信息
variables:
  parentCode: 0
  parentOrgNo:
  customOrgNo:
  licenseType:
  licenseNo:
  legalRepUserCode:
  legalRepAccountNo:
  organizationCode:
  certChargingProject: ""
  data:
    organizationCode: $organizationCode
    customOrgNo: $customOrgNo
    name: $name
    parentCode: $parentCode
    parentOrgNo: $parentOrgNo
    licenseType: $licenseType
    licenseNo: $licenseNo
    legalRepUserCode: $legalRepUserCode
    legalRepAccountNo: $legalRepAccountNo
    certChargingProject: $certChargingProject

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/innerOrganizations/update
  method: POST
  headers:
      Content-Type: 'application/json'
      x-timevale-project-id: ${ENV(esign.projectId)}
      x-timevale-signature: ${getSignature($data)}
  json: $data