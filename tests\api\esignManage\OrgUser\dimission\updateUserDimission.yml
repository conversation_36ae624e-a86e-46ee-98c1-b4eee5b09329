name: 编辑离职交接
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: ${getManageToken()}
    json:
        domain: ${ENV(manage.domain)}
        params:
            handoverDate: $handoverDate
            handoverDesc: $handoverDesc
            handoverStatus: $handoverStatus
            id: $id
            needToResign: $needToResign
            userDimissionId: $userDimissionId
            userReceiveId: $userReceiveId
    method: post
    url: ${ENV(esign.projectHost)}/manage/orguser/dimission/updateUserDimission
