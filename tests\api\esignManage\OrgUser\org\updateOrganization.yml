name: 修改组织
variables:
  legalPersonId:
  licenseNumber:
  licenseType: 1
  organizationTerritory:
  organizationType:
  organizationStatus:
  organizationName:
  accountNumber:
  organizationId:
  parentOrganizationId:
  organizationCode:
  params:
    legalPersonId: $legalPersonId
    organizationCode: $organizationCode
    accountNumber: $accountNumber
    contactAddress:
    dingTalk:
    feiShu:
    licenseNumber: $licenseNumber
    licenseType: $licenseType
    orderNum: 100
    organizationName: $organizationName
    organizationStatus: $organizationStatus
    organizationTerritory: $organizationTerritory
    organizationType: $organizationType
    parentOrganizationId: $parentOrganizationId
    id: $organizationId
  updateData:
    customerIP:
    deptId:
    domain: "admin_platform"
    params: $params
    platform:
    tenantCode: 1000
    userCode:
request:
    url: ${ENV(esign.projectHost)}/manage/orguser/org/updateOrganization
    method: post
    headers:
        Content-Type: application/json;charset=UTF-8
        token: ${getManageToken()}
    json: $updateData