name: 新增人员调动
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: ${getManageToken()}
    json:
        domain: ${ENV(manage.domain)}
        params:
            personnelTransferDate: $personnelTransferDate
            personnelTransferDesc: $personnelTransferDesc
            toOrganizationId: $toOrganizationId
            userId: $userId
    method: post
    url: ${ENV(esign.projectHost)}/manage/orguser/personneltransfer/addPersonnelTransfer
