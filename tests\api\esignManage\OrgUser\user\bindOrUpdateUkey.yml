name: 绑定或换绑用户Ukey
variables:
  token0: ${getManageToken()}
  certData: ""
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    json: {
          "customerIP": "",
          "deptId": "",
          "domain": "",
          "params":{
            id: $id,
            userUkey: $ukey,
            certData: $certData
          },
          "platform": "",
          "tenantCode": "1000",
          "userCode": ""
        }
    method: post
    url: ${ENV(esign.projectHost)}/manage/orguser/user/bindOrUpdateUkey
