name: 通过用户主键Id获取用户对象
variables:
  token0: ${getManageToken()}
  id_getUserById: ""
  userCode_getUserById: ""
  data: {"params":{"id":$id_getUserById,"userCode":$userCode_getUserById},"domain":"admin_platform"}

request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    json: $data
    method: post
    url: ${ENV(esign.projectHost)}/manage/orguser/user/getUserById
