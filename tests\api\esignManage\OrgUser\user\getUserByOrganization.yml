name: 通过组织id获取用户列表分页
variables:
  accountNumber_getUserByOrganization:
  accountNumber: $accountNumber_getUserByOrganization
  data: {"params":{"organizationId":"0","currPage":1,"pageSize":10,"userName":"","accountNumber":"$accountNumber","userMobile":"","userEmail":"","searchType":"1","allChildOrganizationFlag":true,"userStatusList":["1","5"]},"domain":"admin_platform"}

request:
  headers:
    Content-Type: application/json;charset=UTF-8
    token: ${getManageToken()}
  json: $data
  method: post
  url: ${ENV(esign.projectHost)}/manage/orguser/user/getUserByOrganization
