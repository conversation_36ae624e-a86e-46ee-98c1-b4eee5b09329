name: 导入内部用户Excel
variables:
    fileType: multipart/form-data;
    language: zh-CN
    filename:
    filename1: $filename
    filePath:
    filePath1: $filePath

request:
    files:
        file:
        - $filename1
        - ${getFileForUpload($filePath1)}
        - $fileType
    headers:
        #Content-Type: multipart/form-data;boundary=----WebKitFormBoundarylPPUhq2ujXyjxAmo
        token: ${getManageToken()}
        language: $language
    json: {}
    method: post
    url: ${ENV(esign.projectHost)}/manage/orguser/user/importInsideUserExcel
