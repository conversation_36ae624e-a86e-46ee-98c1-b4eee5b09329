name: 获取外部用户分页列表
variables:
  searchType: 1
  userNameManager0:
  organizationName:
  userMobilePageOuter0:
  accountNumber:
  data:
    customerIP:
    deptId:
    domain: admin_platform
    params:
      currPage: 1
      organizationName: $organizationName
      pageSize: 10
      userName: $userNameManager0
      accountNumber: $accountNumber
      searchType: $searchType
      userMobile: $userMobilePageOuter0
    platform:
    tenantCode: 1000
    userCode:
request:
  headers:
    Content-Type: application/json;charset=UTF-8
    token: ${getManageToken()}
  json: $data
  method: post
  url: ${ENV(esign.projectHost)}/manage/orguser/user/pageOutsideUserList
