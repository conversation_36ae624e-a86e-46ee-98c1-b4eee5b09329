name: 保存用户
variables:
  userEmail:
  bankCardNo:
  ecUserParttimeIDList:
  data:
    customerIP:
    deptId:
    domain: admin_platform
    params:
      userName: $userName          #用户姓名不能为空   ^[a-zA-Z\u4e00-\u9fa5\s·]{226}$
      userType: $userType          #用户类型不能为空    [12]用户类型(1管理员、2普通用户)
      userWechat:         #微信支持输入0-50字
      userMobile: $userMobile        #用户手机不能为空 AES加密
      userEmail: $userEmail          #邮箱不能为空 AES加密
      licenseType: $licenseType          #$|[1][3789]|[2][3]$   证件类型(13护照 17港澳居民来往内地通行证 18台湾居民来往大陆通行证 19身份证 23其他)
      licenseNumber: $licenseNumber   #证件号 AES加密
      bankCardNo: $bankCardNo         #银行卡 AES加密
      userStatus: $userStatus        #状态不能为空  状态(1在职、2离职、3活跃、4注销、5未启用)
      userTerritory: $userTerritory  #用户组织类型不能为空   用户组织类型(1内部 2外部)
      userCode: $userCode            #用户编码支持输入2-30字 不能为空
      userWorkWechat:  #企业微信支持输入0-50字
      accountNumber: $accountNumber   #账号支持输入2-30字     不能为空
      userDingTalk:      #钉钉支持输入0-50字
      userFeiShu:          #飞书支持输入0-50字
      dimissionTime:   #离职/注销时间必须在1-19之间     离职/注销时间格式必须是yyyy-MM-dd HH:mm:ss
      organizationId: $organizationId   #所属组织支持输入1-36字
      organizationCode: $organizationCode  #所属组织编码支持输入1-36字 所属组织编码不能为空
      ecUserParttimeIDList: $ecUserParttimeIDList  #兼职用户ID
    platform:
    tenantCode: 1000
    userCode:
request:
  headers:
    Content-Type: application/json;charset=UTF-8
    token: ${getManageToken()}
  json: $data
  method: post
  url: ${ENV(esign.projectHost)}/manage/orguser/user/saveUser
