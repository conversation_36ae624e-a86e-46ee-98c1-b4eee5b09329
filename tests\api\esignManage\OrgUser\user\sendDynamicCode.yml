name: 用户信息-发送手机/邮箱验证码
variables:
    address: 地址
    addressType: 地址类型 比如4表示手机、3表示邮箱
    scenes: 使用场景比如登录、找回密码等(具体枚举参照AccountSceneEnum)
request:
    url: ${ENV(esign.projectHost)}/manage/orguser/user/sendDynamicCode
    method: POST
#    headers: ${gen_token_header_permissions($accountNumber,$password)}
    headers:
       Content-Type: application/json;charset=UTF-8
       token: ${getManageToken()}
    json:
        params:
            address: $address
            addressType: $addressType
            scenes: $scenes
        domain: "admin_platform"