name: 修改用户
variables:
    data:
        domain: admin_platform
        params:
            userName: $userName
            accountNumber: $accountNumber
            userType: $userType
            userTerritory: $userTerritory
            userStatus: $userStatus
            userEmail: $userEmail
            userMobile: $userMobile
            dimissionTime: $dimissionTime
            organizationId: $organizationId
            ecUserParttimeIDList: $ecUserParttimeIDList
            licenseType: $licenseType
            licenseNumber: $licenseNumber
            bankCardNo: $bankCardNo
            useLanguage: $useLanguage
            userUkey: $userUkey
            userFeiShu: $userFeiShu
            userDingTalk: $userDingTalk
            userWorkWechat: $userWorkWechat
            userWechat: $userWechat
            id: $id
            userCode: $userCode
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: ${getManageToken()}
    json: $data
    method: post
    url: ${ENV(esign.projectHost)}/manage/orguser/user/updateUser
