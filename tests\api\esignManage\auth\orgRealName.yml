name: 意愿认证-内部用户账密认证
variables:
  envName: "stable"
  legalUserLicNo: ""
  legalUserName: ""
  orgLicNo: ""
  orgName: ""
  saasUserOid: ""


request:
  url: ${ENV(esign.projectHost)}/df/org/realName
  method: POST
  headers:
    Content-Type: 'application/json'
#    token: ${getManageToken()}
#    X-timevale-project-id: ${ENV(esign.projectId)}
  json:
    envName: $envName
    legalUserLicNo: $legalUserLicNo
    legalUserName: $legalUserName
    orgLicNo: $orgLicNo
    orgName: $orgName
    saasUserOid: $saasUserOid