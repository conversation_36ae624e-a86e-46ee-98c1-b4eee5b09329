variables:
  - forceRealName: false
  - type: 2
  - orgCode:
#  - account: ${ENV(sign01.accountNo)}
#  - password: ${ENV(passwordEncrypt)}
  - userCode:
  - authorization0:  ${getPortalToken()}  #${get_outer_person_token(account)}
  - project0: ${ENV(esign.projectId)}
#  - header: ${gen_token_header_permissions($accountNumber,$password)}
  - orgRealNamed:
  - header:
      Content-Type: 'application/json'
      authorization: $authorization0
      X-timevale-project-id: $project0

request:
  url: ${ENV(esign.projectHost)}/esign-signs/auth/goAuthUrl
  method: POST
  headers: $header
  json:
    {
      params:
       {
        forceRealName: $forceRealName,
        processId: $processId,
        redirectUrl: $redirectUrl,
        type: $type,
        userCode: $userCode,
        orgRealNamed: $orgRealNamed,
        organizeCode: $orgCode
       }
    }