variables:
  - forceRealName:
  - type: 2
  - orgCode:
  - account: ${ENV(sign01.accountNo)}
  - password: ${ENV(passwordEncrypt)}
  - userCode:
request:
  url: ${ENV(esign.projectHost)}/esign-signs/auth/realName
  method: POST
  headers: ${gen_token_header_permissions($accountNumber,$password)}
  json:
    {
      params:
       {
        forceRealName: $forceRealName,
        processId: $processId,
        redirectUrl: $redirectUrl,
        type: $type,
        organizeCode: $orgCode,
        userCode: $userCode
       }
    }