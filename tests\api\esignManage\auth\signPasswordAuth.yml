name: 意愿认证-内部用户账密认证
variables:
  applyId:
  signPassword: ${ENV(ceswdzxzdhyhwgd1.password)}


request:
  url: ${ENV(esign.projectHost)}/manage/auth/signPasswordAuth
  method: POST
  headers:
    Content-Type: 'application/json'
    token: $manageToken
    X-timevale-project-id: ${ENV(esign.projectId)}
  json:
    domain: admin_platform
    authentication: true
    params:
      applyId: $applyId
      signPassword: $signPassword