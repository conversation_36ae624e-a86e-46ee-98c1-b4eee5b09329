name: ukey意愿
variables:
    algorithm: "sha256RSA"
    data:
        domain: admin_platform
        params:
            algorithm: $algorithm
            applyId: $applyId
            certBase64: $certBase64
            signSource: $signSource
            signValue: $signValue
            success: $success
            ukeyNumber: $ukeyNumber
request:
    headers:
        Content-Type: application/json;charset=UTF-8
        token: ${getManageToken()}
    json: $data
    method: post
    url: ${ENV(esign.projectHost)}/manage/auth/ukeyWillAuth
