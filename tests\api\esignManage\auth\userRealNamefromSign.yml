variables:
  - forceRealName: false
  - type: 1
  - organizeCode:
  - account: ${ENV(sign01.accountNo)}
  - password: ${ENV(passwordEncrypt)}


request:
  url: ${ENV(esign.projectHost)}/esign-signs/auth/realName
  method: POST
  headers: ${gen_token_header_permissions($accountNumber,$password)}
  json:
    {
      params:
       {
        forceRealName: $forceRealName,
        processId: $processId,
        redirectUrl: $redirectUrl,
        type: $type,
        organizeCode: $organizeCode
       }
    }