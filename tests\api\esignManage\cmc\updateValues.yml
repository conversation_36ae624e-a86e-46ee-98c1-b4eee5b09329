name: 修改配置值
variables:
    moduleId:
    thirdId:
    configValues: []
    token0: ${getManageToken()}
request:
#    url: ${ENV(esign.projectHost)}/manage/v1/cmc/values
    url: ${ENV(esign.projectHost)}/manage/v1/cmc/values/update
    method: POST
    headers:
        Content-Type: application/json;charset=UTF-8
        token: $token0
    json:
      moduleId: $moduleId
      configValues: $configValues
      thirdId: $thirdId
      domain: "admin_platform"
