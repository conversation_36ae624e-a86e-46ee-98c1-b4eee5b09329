name: 上传应用授权
variables:
    filePath:
    fileName:
    fileType: multipart/form-data
    uuidWrapper:
request:
    url: ${ENV(esign.projectHost)}/etl-integrate/v1/lc/manage/app/license/upload
    method: POST
    headers:
        Esa-Token: ${getManageToken()}
        Esa-Token-Source: MANAGE
    params:
        uuid: $uuidWrapper
    files:
        file:
            - $fileName
            - $filePath
            - $fileType
