
name: 根据环节分类获取处理人类型集合和按钮集合

request:
    url: ${ENV(esign.projectHost)}/manage/workflow/nodeclass/listHandlerTypeAndButtonByNodeClassType
    method: POST
    headers:
        Content-Type: "application/json;charset=UTF-8"
        token: ${getManageToken()}
    json:
        domain: ${ENV(manage.domain)}
        params:
            nodeClassType: $nodeClassType
            workflowType: $workflowType

