name: 新建外部组织

variables:
  data: {
    name: $name,
    customOrgNo: $customOrgNo,
    licenseType: $licenseType,
    licenseNo: $licenseNo,
    legalRepAccountNo: $legalRepAccountNo,
    legalRepUserCode: $legalRepUserCode
  }

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/outerOrganizations/create
  method: POST
  headers:
    Content-Type: 'application/json'
    x-timevale-project-id: ${ENV(esign.projectId)}
    x-timevale-signature: ${getSignature($data)}
  json: $data