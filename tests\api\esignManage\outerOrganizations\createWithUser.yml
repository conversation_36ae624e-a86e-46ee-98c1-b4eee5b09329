variables:
  - legalRepAccountNo: ""
  - legalRepUserCode: ""
  - licenseNo: ""
  - licenseType: ""
  - bankCardNo: ""
  - licenseNo1: ""
  - licenseType1: ""
  - licenseNo2: ""
  - licenseType2: "ID_CARD"
  - mainCustomOrgNo: ""
  - mainOrganizationCode: ""
  - otherCustomOrgName: ""
  - otherOrganizationCode: ""
  - customOrgNo: ""
  - name1: ""
  - customAccountNo: ""
  - email: ""
  - mobile: ""
  - name2: ""
  - data: {
    "customOrgNo": "$customOrgNo",
    "legalRepAccountNo": "$legalRepAccountNo",
    "legalRepUserCode": "$legalRepUserCode",
    "licenseNo": "$licenseNo1",
    "licenseType": "$licenseType1",
    "name": "$name1",
    "user": {
      "bankCardNo": "$bankCardNo",
      "customAccountNo": "$customAccountNo",
      "email": "$email",
      "licenseNo": $licenseNo2,
      "licenseType": "$licenseType2",
      "mainCustomOrgNo": "$mainCustomOrgNo",
      "mainOrganizationCode": "$mainOrganizationCode",
      "mobile": $mobile,
      "name": "$name2"}}

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/outerOrganizations/createWithUser
  method: POST
  headers: ${gen_headers_signature($data)}
  json: $data