name: 查询外部组织成员列表

variables:
  data: {
    organizationCode: $organizationCode,
    customOrgNo: $customOrgNo,
    pageNo: $pageNo,
    pageSize: $pageSize,
    projectId: $projectId
  }
  projectId:

request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/outerOrganizations/members/list
  method: POST
  headers:
    Content-Type: 'application/json'
    x-timevale-project-id: ${ENV(esign.projectId)}
    x-timevale-signature: ${getSignature($data)}
  json: $data