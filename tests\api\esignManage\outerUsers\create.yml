
name: 新建外部用户
variables:
    data: {
        customAccountNo: $customAccountNo,
        email: $email,
        mobile: $mobile,
        name: $name
    }

request:
    url: ${ENV(esign.gatewayHost)}/manage/v1/outerUsers/create
    method: POST
    headers:
        Content-Type: 'application/json'
        x-timevale-project-id: ${ENV(esign.projectId)}
        x-timevale-signature: ${getSignature($data)}
    json: $data