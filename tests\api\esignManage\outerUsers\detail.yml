name: 查询外部用户
variables:
  email: ""
  userCode: ""
  customAccountNo: ""
  name: ""
  mobile: ""
  licenseNo: ""
  outerUsersDetail: {
    userCode: $userCode,
    customAccountNo: $customAccountNo,
    name: $name,
    email: $email,
    mobile: $mobile,
    licenseNo: $licenseNo
  }
request:
  url: ${ENV(esign.gatewayHost)}/manage/v1/outerUsers/detail
  method: POST
  headers:
    Content-Type: 'application/json'
    x-timevale-project-id: ${ENV(esign.projectId)}
    x-timevale-signature: ${getSignature($outerUsersDetail)}
  json: $outerUsersDetail