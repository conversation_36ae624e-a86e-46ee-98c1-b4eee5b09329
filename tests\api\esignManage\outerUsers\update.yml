
name: 修改外部用户
variables:
    bankCardNo: ''
    customAccountNo: ''
    email: ''
    licenseNo: '220301193604270034'
    licenseType: 'ID_CARD'
    mainCustomOrgNo: ''
    mainOrganizationCode: '7411e3dfc40442f88bf432ae58c43759'
    mobile: '***********'
    name: '测试全链路外部一'
    otherOrganization:
      - otherCustomOrgName: '${ENV(wsignwb01.main.orgName)}'
        otherCustomOrgNo: '${ENV(wsignwb01.main.orgNo)}'
        otherOrganizationCode: '${ENV(wsignwb01.main.orgCode)}'
    userCode:

    updateData:
        bankCardNo: $bankCardNo
        customAccountNo: $customAccountNo
        email: $email
        licenseNo: $licenseNo
        licenseType: $licenseType
        mainCustomOrgNo: $mainCustomOrgNo
        mainOrganizationCode: $mainOrganizationCode
        mobile: $mobile
        name: $name
        otherOrganization: $otherOrganization
        userCode: $userCode
    data: $updateData

request:
    url: ${ENV(esign.gatewayHost)}/manage/v1/outerUsers/update
    method: POST
    headers:
        Content-Type: 'application/json'
        x-timevale-project-id: ${ENV(esign.projectId)}
        x-timevale-signature: ${getSignature($data)}
    json: $data