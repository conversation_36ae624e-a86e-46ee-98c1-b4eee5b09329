name: 业务平台-批量导入外部组织
variables:
        accountNumber: ${ENV(sign01.accountNo)}
        password: ${ENV(password01)}
        navId:
request:
    files:
        file:
        - $filename
        - ${getFileForUpload($filePath)}
        - $fileType
    headers: ${gen_upload_token_header($accountNumber,$password,$navId,$language)}
    json: {}
    method: post
    url: ${ENV(esign.projectHost)}/portal/opposite/org/batchImportOrganization
