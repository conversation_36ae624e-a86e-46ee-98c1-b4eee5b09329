name: 业务平台-批量导入外部用户
variables:
        accountNumber: ${ENV(sign01.accountNo)}
        password: ${ENV(password01)}
        navId:
request:
    files:
        file:
        - $filename1
        - ${getFileForUpload($filePath1)}
        - $fileType1
    headers: ${gen_upload_token_header($accountNumber,$password,$navId)}
    json: {}
    method: post
    url: ${ENV(esign.projectHost)}/portal/opposite/user/batchImportUser
