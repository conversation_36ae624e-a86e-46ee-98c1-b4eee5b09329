name: 导入内部组织Excel
variables:
        accountNumber: ${ENV(sign01.accountNo)}
        password: ${ENV(password01)}
        navId: "1764924302865543170"
request:
        files:
            file:
            - $filename
            - ${getFileForUpload($filePath)}
            - $fileType
        headers: ${gen_upload_token_header($accountNumber,$password,$navId,$language)}
        json: {}
        method: post
        url: ${ENV(esign.projectHost)}/portal/orguser/org/importOrganizationExcel
