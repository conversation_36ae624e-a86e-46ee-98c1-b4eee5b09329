name: 导入内部用户Excel
variables:
        accountNumber: ${ENV(sign01.accountNo)}
        password: ${ENV(password01)}
        navId: "1764924302865543170"
request:
        files:
            file:
            - $filename1
            - ${getFileForUpload($filePath1)}
            - $fileType1
        headers: ${gen_upload_token_header($accountNumber,$password,$navId)}
        json: {}
        method: post
        url: ${ENV(esign.projectHost)}/portal/orguser/user/importInsideUserExcel
