name: 保存用户
variables:
        accountNumber: ${ENV(sign01.accountNo)}
        password: ${ENV(password01)}
        navId: "1764924302865543170"
        data:
          {
                  "customerIP": "",
                  "deptId": "",
                  "domain": "admin_platform",
                  "params": $subParamsSaveUser,
                  "platform": "",
                  "tenantCode": "1000",
                  "userCode": ""
          }
request:
        headers: ${gen_token_header_permissions($accountNumber,$password,$navId)}
        json: $data
        method: post
        url: ${ENV(esign.projectHost)}/portal/orguser/user/saveUser
