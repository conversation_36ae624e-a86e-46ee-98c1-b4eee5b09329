import base64
import hashlib
import pandas as pd
import hmac
import json
import os
import re
import string
import random
import sys
import time
import urllib
import datetime
from functools import lru_cache

import pymysql
import requests
from requests_toolbelt import MultipartEncoder
from tools import readMail
from tools import cardGen
from tools import sign_create as sc
import message as message
import tools.unifiedAuth as unifiedAuth
from tools import autoSign
from tools import signFlowGen
from tools.signFlowGen import autoSignByFile, gen_signFlow_data6
from tools.DBConnet import connect, execute_sql_convert_array, execute_sql_convert_object
from tools.SigntureConfig import SigntureConfig
from tools.callback import callback_check
from tools.esignOrganizationUser import  detailInnerOrganizations, detailOuterUsers, \
    detailOuterOrganizations
from tools.esignSealAndCertGen import getSealTypeCode, getPersonSealId
from tools.esignSignFlowNodeGen import createSignFlow, signersDelete, addFiles, addSigners, startSignFlow, \
    finishSignFlow, addSignersAutoSign, deleteSigners
from utils.common import get_randomNo_16
from utils.common import get_randomNo_32
from urllib.parse import urlparse, parse_qs
from utils import ENV

project_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_dir)
# 自定义非tests/ 目录下的引用需要放在下面

sep = os.path.sep  # 路径分隔符
cur_dir = os.path.abspath('.') + sep
cur_dir_data = os.path.abspath('.') + sep + "tests" + sep

verify_code_image_path = 'sendVerifyCode.png'
DATAPATH = os.path.join(os.path.dirname(os.path.realpath(__file__)), 'data')
PROJECT_ID = ENV('esign.projectId')
PROJECT_SECRET = ENV('esign.projectSecret')
MAIN_HOST = ENV('esign.projectHost')
OPENAPI_HOST = ENV('esign.projectHost')

sign01_customAccountNo = ENV('sign01.accountNo')
sign01_userCode = ENV('sign01.userCode')
org01_orgCode = ENV('sign01.main.orgCode')
password_encrypt = ENV('passwordEncrypt')
pdf_filekey_crypto = ENV('cryptoFileKey')
pdf_filekey = ENV('fileKey')
ofd_filekey = ENV('ofdFileKey')
ofd_filekey_crypto = ENV('ofdCryptoFileKey')
csqs_userCode = ENV('csqs.userCode')
csqs_main_orgCode = ENV('csqs.orgCode')
ci_orgCode = ENV('csqs.orgCode')
businessTypeCode = ENV('businessTypeCode')
sign01_main_orgName = ENV('sign01.main.orgName')
sign01_userName = ENV('sign01.userName')
sign01_sealId = ENV('sign01.sealId')
org01_sealId = ENV('org01.sealId')
wsignwb01_accountNo = ENV('wsignwb01.accountNo')
wsignwb01_main_orgNo = ENV('wsignwb01.main.orgNo')
wsignwb01_userCode = ENV('wsignwb01.userCode')
wsignwb01_main_orgCode = ENV('worg01.orgCode')
wsignwb01_main_orgName = ENV('wsignwb01.main.orgName')
wsignwb01_sealId = ENV('wsignwb01.sealId')
wsignwb02_approve_accountNo = ENV('wsignwb02.approve.accountNo')
wsignwb02_approve_userCode = ENV('wsignwb02.approve.userCode')
wsignwb02_approve_saasId = ENV('wsignwb02.approve.saasId')
wsignwb02_account_encrypt = ENV('wsignwb02.account.encrypt')
worg01_orgNo = ENV('worg01.orgNo')
worg01_orgCode = ENV('worg01.orgCode')
worg01_orgId = ENV('worg01.orgId')
dbHost = ENV('esign.dbHost')
dbPort = ENV('esign.dbPort')
dbUser = ENV('esign.dbUser')
dbPassword = ENV('esign.dbPassword')
db_manage = ENV('esign.db.manage')
db_seals = ENV('esign.db.seals')
db_signs = ENV('esign.db.signs')
CLOUD_APPID = ENV('appId')
CLOUD_APPSECRET = ENV('appSecret')
cloud_url = ENV('cloudUrl')
cloud_login_url = ENV('cloud.login.Url')
cloud_h5_url = ENV('cloud.h5.Url')
cloud_saas_url = ENV('cloud.saas.Url')
fileKey_big_332MB = ENV('fileKey332MB')
cloudUrl = ENV('cloud.will.Url')
processDefinitionKey = ENV('processDefinitionKey')
gateway_on = ENV('gateway_on')
autotest_name_prefix = "自动化"

from utils.esignToken import createManageToken, openApiSignature
MANAGE_TOKEN = createManageToken(ENV('manage.account'), ENV('manage.password'))

@lru_cache(128)
def getManageToken():
    """
    管理后台token，默认admin登录
    :return:
    """
    return MANAGE_TOKEN

def decryptTokenKey(willingUrl):
    """
    解密willing接口返回的code，用于密码意愿认证签署
    签署页签署的时候的意愿token： 业务token置换管理平台token
    :param willingUrl: 意愿认证申请地址
    :return:
    """
    from utils.esignToken import parseToken
    token = parseToken(willingUrl)
    return token

def putTempEnv(key, value):
    """
    临时存入环境变量，取值方式${ENV(key)}
    :param key: 自定义key，避免与.env里的key重复
    :param value:
    :return:
    """
    os.environ[key] = value

from utils.esignToken import createPortalTokenObj
"""统一门户和管理平台token设为全局变量，避免反复登录"""
TOKEN_OBJ = createPortalTokenObj(sign01_customAccountNo, password_encrypt)
PORTAL_TOKEN = TOKEN_OBJ.token
PORTAL_CODE = TOKEN_OBJ.code

SEAL_TOKEN_OBJ = createPortalTokenObj(ENV('userCode'), password_encrypt)
SEAL_TOKEN = SEAL_TOKEN_OBJ.token
SEAL_CODE = SEAL_TOKEN_OBJ.code

DOC_TOKEN_OBJ = createPortalTokenObj(ENV('ceswdzxzdhyhwgd1.account'), password_encrypt)
DOC_TOKEN = DOC_TOKEN_OBJ.token
DOC_CODE = DOC_TOKEN_OBJ.code

@lru_cache(128)
def getPortalToken(account=None, password=None):
    """
    统一门户token，支持自定义账密
    :param account: ENV('sign01.accountNo')
    :param password: 密码默认设置(abc_123456)
    :return:
    """
    if account is None and password is None:
        return PORTAL_TOKEN
    else:
        if password is None:
            password = password_encrypt
        if account :
            if account == sign01_customAccountNo:
                return PORTAL_TOKEN
            if account == ENV('userCode'):
                return SEAL_TOKEN
            if account == ENV('ceswdzxzdhyhwgd1.account'):
                return DOC_TOKEN
            tokenObj = createPortalTokenObj(account, password)
            return tokenObj.token
    return PORTAL_TOKEN

@lru_cache(128)
def getPortalCode():
    """
    统一门户登录code，自定义账密时先调用getPortalToken
    :return:
    """
    return PORTAL_CODE


# 路径处理工具类
class pathutil(object):
    """路径处理工具类"""

    def __init__(self):
        # 判断调试模式
        debug_vars = dict((a, b) for a, b in os.environ.items()
                          if a.find('IPYTHONENABLE') >= 0)
        print(sys.path)
        # 根据不同场景获取根目录
        if len(debug_vars) > 0:
            """当前为debug运行时"""
            print("debug执行")
            self.rootPath = sys.path[0]
        elif getattr(sys, 'frozen', False):
            """当前为exe运行时"""
            print("exe执行")
            self.rootPath = os.getcwd()
        else:
            """正常执行"""
            print("正常执行")
            self.rootPath = sys.path[0]
        # 替换斜杠
        self.rootPath = self.rootPath.replace("\\", "/") + "/"


PathUtil = pathutil()

# 获取请求公有云的请求头
def getHeadersForRequestPublicCloud(data, url, appId, appSecret, requestType):
    accept = "*/*"
    contentType = "application/json; charset=UTF-8"
    headers = {
        "X-Tsign-Open-App-Id": appId,
        "X-Tsign-Open-Auth-Mode": "Signature",
        "Accept": accept,
        "Content-Type": contentType
    }
    if requestType == 'GET':
        headers['X-Tsign-Open-Auth-Mode'] = 'simple'
    else:
        contentMd5 = getContenMd5(data)
        timestamp = str(int(round(time.time() * 1000)))
        headers['X-Tsign-Open-Ca-Signature'] = getSignatureSaas(contentMd5, url, appSecret,
                                                            requestType)
        headers['Content-MD5'] = contentMd5
    return headers


# MD5计算
def getContenMd5(data):
    contentMd5Before = json.dumps(data)
    hl = hashlib.md5()
    hl.update(contentMd5Before.encode("UTF-8"))
    contentMd5After = str(base64.b64encode(hl.digest()), "UTF-8")
    return contentMd5After

def getSignatureSaas(contentMD5, url, appSecret, requestType):
    """
    计算签名
    :param contentMD5:
    :param url:
    :param appSecret:
    :param accept:
    :param contentType:
    :param requestType:
    :return: hash
    """
    date = ""
    headers = ""
    contentType = 'application/json'
    data = requestType \
           + "\n" + "*/*" \
           + "\n" + contentMD5 \
           + "\n" + contentType \
           + "\n" + date \
           + "\n" + headers + url
    res = get_sha256(data, appSecret)
    print(res)
    return res

def getSignature2(data,appSecret):
    """
    openapi 请求体加密
    :param data: 请求体
    :return:
    """
    from utils.esignToken import openApiSignature2
    return  openApiSignature2(data,appSecret)

# sha256加密
def get_sha256(data, key=None):
    if key == None:
        key = PROJECT_SECRET
    key = key.encode('utf-8')  # sha256加密的key
    message = data.encode('utf-8')  # 待sha256加密的内容
    sign = base64.b64encode(hmac.new(key, message, digestmod=sha256).digest()).decode()
    return sign


# 校验验证码认证
def verifyCodeAuth(bizId, willAuthId):
    url = "/v1/willingness/verifyCodeAuth"
    data = {
        "authCode": "123456",
        "bizId": bizId,
        "bizType": "SIGN",
        "willAuthId": willAuthId
    }
    res = requestPublicCloud(url, data, CLOUD_APPID, CLOUD_APPSECRET, "PUT")
    return res

# 请求公有云
def requestPublicCloud(url, data, appId, appSecret, requestType):
    headers = getHeadersForRequestPublicCloud(data, url, appId, appSecret, requestType)
    print('请求公有云入参', url, data, appId, appSecret, requestType)
    if requestType == "POST":
        res = requests.post(url=cloudUrl + url, headers=headers, json=data)
    elif requestType == "PUT":
        res = requests.put(url=cloudUrl + url, headers=headers, json=data)
    elif requestType == "GET":
        if  not url.startswith('http'):
            url = cloudUrl + url
        res = requests.get(url=url, headers=headers)
    jsonData = json.loads(res.text)
    print('请求公有云', jsonData)
    if jsonData["code"] != 0:
        print(res.text)
    return jsonData["data"]

# 查询个人的实名授权状态
def cloud_query_psnIdentity(mobile):
    try:
        url1 = f"{cloud_url}/v3/persons/identity-info?psnAccount={mobile}"
        response1 = requestPublicCloud(url1, '', CLOUD_APPID, CLOUD_APPSECRET, "GET")
        print("查询个人认证信息: ", response1)

        realnameStatus = response1.get('realnameStatus')  # 0 - 未实名，1 - 已实名
        authorizeUserInfo = response1.get('authorizeUserInfo')  # true - 已授权，false - 未授权
        oid = response1.get('psnId')
        return realnameStatus, authorizeUserInfo, oid
    except Exception as ex:
        print(ex)

@lru_cache(128)
def getSaasId(outerUserCode):
    '''
    获取相对方的saasId
    :param outerUserCode:
    :return:
    '''
    userInfo01 = detailOuterUsers(OPENAPI_HOST, PROJECT_ID, PROJECT_SECRET, outerUserCode)
    phone01 = userInfo01.get('mobile')
    tmp = cloud_query_psnIdentity(phone01)
    return tmp[2]

wsignwb01_saasId = getSaasId(wsignwb01_userCode)
wsignwb02_approve_saasId = getSaasId(wsignwb02_approve_userCode)

def get_userMobile():
    """
    :param successData: 随机生成手机号
    :return:
    """
    from faker import Faker
    faker = Faker('zh_CN')
    return faker.phone_number()

def get_name(num):
    global str
    str_list = []
    for i in range(num):
        i = 1
        head = random.randint(0xb0, 0xf7)
        body = random.randint(0xa1, 0xfe)
        val = f'{head:x} {body:x}'
        # 由于gb2312 范围太小，会出现错误，这里改成 GB18030
        # name1 = bytes.fromhex(val).decode('gb2312')
        name1 = bytes.fromhex(val).decode('GB18030')
        str_list.append(name1)
        a = ''
        name = a.join(str_list)
    return name

def sleep(n_secs):
    time.sleep(n_secs)


# 天印Post，Get请求签名验签
def gen_headers_signature(data=None):
    signature = hmac_sha256_encrypt(data, PROJECT_SECRET)
    headers = {"Content-Type": "application/json", "x-timevale-project-id": PROJECT_ID,
               "x-timevale-signature": signature}
    return headers


def gen_token_headers_method(PROJECT_ID, PROJECT_SECRET, token, method):
    content_type = "application/json"
    if method == "GET":
        content_type = "application/x-www-form-urlencoded"
    headers = {"Content-Type": content_type, "x-timevale-project-id": PROJECT_ID, "authorization": token}
    return headers


def get_headers_with_autho(projectId=None):
    """
    使用账号登录统一门户，登录账号默认sign01
    :param PROJECT_ID:
    :return:
    """
    if projectId == None:
        projectId = PROJECT_ID
    headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId,
               "authorization": PORTAL_TOKEN}
    return headers


def get_headers_with_accountCode(accountCode):
    """
    使用账号登录统一门户,可以指定登录账号
    :param accountCode: 指定是用户的账号信息，customAccountNo
    :return:
    """
    if accountCode == sign01_customAccountNo:
        headers =  get_headers_with_autho()
    else:
        headers = {"Content-Type": "application/json", "x-timevale-project-id": PROJECT_ID,
                   "authorization": getPortalToken(accountCode, password_encrypt)}
    return headers

def getSignature(requestJson: dict):
    """
    openapi 请求体加密
    :param requestJson: 请求体
    :return:
    """
    return openApiSignature(requestJson)

def get_outer_person_token(outerUserCode):
    """
    相对方账号手机号登录
    :param outerUserCode:
    :return:
    """
    userInfo01 = detailOuterUsers(OPENAPI_HOST, PROJECT_ID, PROJECT_SECRET, outerUserCode)
    print('XXXXX',userInfo01)
    phone01 = userInfo01.get('mobile')
    token = getVerCodeToken(phone01, 2)
    return token

# param 当获取wsignwb01的token则 param=wsignwb01.userCode;若是获取用印审批的wsignwb02的token则 param=签署流程Id
def get_headers_with_outer_person(param=None):
    """
    手机号登录系统
    :param param: 相对方用户的userCode信息
    :return:
    """
    try:
        # 获取指定用户的手机号,默认相对方外部用户:wsignwb01
        if param:
            userCode01 = param
        else:
            userCode01 =  wsignwb01_userCode
        token = get_outer_person_token(userCode01)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": PROJECT_ID,
                   "authorization": token}
        return headers
    except:
        print('获取相对方登录token失败')
        return None

def getVerCodeToken(phoneOrMail, userTerritory=None):
    """
    外部/内部签署人，通过验证码登录获取token
    :param phoneOrMail: 手机号或者邮箱
    :param userTerritory: '2'-外部，None-内部
    :return: token
    """
    # 自定义方法，调用时引入
    from utils.esignToken import createVerCodeToken
    return createVerCodeToken(phoneOrMail, userTerritory)


def get_headers_with_fileUpload(PROJECT_ID):
    # 获取内部签署人的token
    token1 = getPortalToken(sign01_customAccountNo, password_encrypt)
    headers = {"Content-Type": "multipart/form-data; boundary=----WebKitFormBoundaryMj8eVLxVsBeDDjs6",
               "x-timevale-project-id": PROJECT_ID, "authorization": token1}
    return headers


#文件上传openapi接口signature
def get_signature_with_fileUpload(request_data):
    # 初始化查询字符串
    query_string = ''

    # 遍历请求数据并构建查询字符串
    for key in request_data:
        if key == 'file':
            continue
        if key in request_data:
            query_string += f"{urllib.parse.quote(key)}={urllib.parse.quote(str(request_data[key]))}&"

    # 去掉最后一个&符号
    query_string = query_string.rstrip('&')

    # 生成 HMAC-SHA256 签名
    secret_key = ENV('esign.projectSecret')
    hash_object = hmac.new(
        secret_key.encode('utf-8'),
        query_string.encode('utf-8'),
        hashlib.sha256
    )
    signature = hash_object.hexdigest()
    # 返回签名结果（在实际应用中，你可能需要设置环境变量或返回结果）
    return signature



# 获取随机数字符串
def get_randomNo_str():
    return str(random.randint(1, 999999))


# 默认数据路径都放在data下
def open_file(local_path):
    fd = open(cur_dir + local_path, 'rb')
    return fd


# 获取文件的大小
def get_file_size(local_path):
    size = os.path.getsize(PathUtil.rootPath + local_path)
    return size


# 获取文件的后缀名/扩展名
def get_file_suffix(local_path):
    try:
        suffix = os.path.splitext(PathUtil.rootPath + local_path)[-1][1:]
        return suffix
    except:
        return None


# 获取文件的base64编码，并根据文件格式添加前缀，以便能被正确解析
def get_file_base64_add_suffix(local_path):
    try:
        suffix = get_file_suffix(local_path)
        f = open(PathUtil.rootPath + local_path, 'rb')
        ls_f = str(base64.b64encode(f.read()), 'utf-8')
        return "data:image/" + suffix + ";base64," + ls_f
    except:
        return None


# 根据文件名获取相对路径
def get_file_path_by_name(file_name):
    try:
        return "data/" + file_name
    except Exception as e:
        print(e.args)
        print("get_file_path_by_name 报错")
        return None


def get_file_base64(local_path):
    try:
        f = open(PathUtil.rootPath + local_path, 'rb')
        ls_f = str(base64.b64encode(f.read()), 'utf-8')
        return ls_f
    except Exception as e:
        print(e.args)
        print("get_file_base64 报错")
        return None


def get_file_base64_absolute_path(absolute_path):
    try:
        f = open(absolute_path, 'rb')
        ls_f = str(base64.b64encode(f.read()), 'utf-8')
        return ls_f
    except:
        return None


def get_file_base64_md5(local_path):
    try:
        fd = open(cur_dir + local_path, 'rb')
        m = hashlib.md5()
        while True:
            d = fd.read(8096)
            if not d:
                break
            m.update(d)
        byte = base64.b64encode(m.digest())
        return bytes.decode(byte)
    except:
        return None


def connectDB(db_host, db_port, db_user, db_passwd, dbname, db_charset):  # MYSQL数据库连接
    try:
        connect = pymysql.Connect(
            host=db_host,
            port=db_port,
            user=db_user,
            passwd=db_passwd,
            db=dbname,
            charset=db_charset)

        return connect
    except:
        return None


# 随机生成身份证
def regiun():
    '''生成身份证前六位'''
    # 列表里面的都是一些地区的前六位号码
    first_list = ['362402', '362421', '362422', '362423', '362424', '362425', '362426', '362427', '362428', '362429',
                  '362430', '362432', '110100', '110101', '110102', '110103', '110104', '110105', '110106', '110107',
                  '110108', '110109', '110111']
    first = random.choice(first_list)
    return first


def year():
    '''生成年份'''
    now = time.strftime('%Y')
    # 1948为第一代身份证执行年份,now-18直接过滤掉小于18岁出生的年份
    second = random.randint(1948, int(now) - 18)
    age = int(now) - second
    print('随机生成的身份证人员年龄为：' + str(age))
    return second


def month():
    '''生成月份'''
    three = random.randint(1, 12)
    # 月份小于10以下，前面加上0填充
    if three < 10:
        three = '0' + str(three)
        return three
    else:
        return three


def day():
    '''生成日期'''
    four = random.randint(1, 31)
    # 日期小于10以下，前面加上0填充
    if four < 10:
        four = '0' + str(four)
        return four
    else:
        return four


def randoms():
    '''生成身份证后四位'''
    # 后面序号低于相应位数，前面加上0填充
    five = random.randint(1, 9999)
    if five < 10:
        five = '000' + str(five)
        return five
    elif 10 < five < 100:
        five = '00' + str(five)
        return five
    elif 100 < five < 1000:
        five = '0' + str(five)
        return five
    else:
        return five


def get_IdCard():
    first = regiun()
    second = year()
    three = month()
    four = day()
    last = randoms()
    IDcard = str(first) + str(second) + str(three) + str(four) + str(last)

    return IDcard


def int_sum(num1, num2):
    sum = int(num1) + int(num2)
    return sum

def int_cal(num1,num2,num3,num4):
    sum = int(num1) + int(num2) -int(num3) -int(num4)
    return sum

def lists_len(lists):
    return len(lists)


# 验证列表里某个字段的值是否正确
def containItems(results, value, key):
    contain = False;
    for i in range(len(results)):
        if results[i][key] == value:
            contain = True
    return contain


# 如果 results[n]的字段category == a, 返回 list[n]的 name 字段的值
def return_item_value(results, condition_key, condition_value, retrun_key):
    for i in range(len(results)):
        if results[i][condition_key] == condition_value:
            return results[i][retrun_key]
    return False


def get_json_field(json, field):
    try:
        return json[field]
    except:
        return None


def hook_sleep_n_secs(n_secs):
    time.sleep(n_secs)


def not_eq_str(value1, value2):
    if value1 == value2:
        return False
    return True


def is_null(check_value):
    is_null = False
    if check_value is None:
        is_null = True
    return is_null


# 如果 results[n]的字段category == a, 返回 list[n]的 name 字段的值
def return_item_value(results, condition_key, condition_value, retrun_key):
    for i in range(len(results)):
        if results[i][condition_key] == condition_value:
            return results[i][retrun_key]
    return False


def get_str_base64(content):
    try:
        return base64.b64encode(str.encode(json.dumps(content), encoding="utf-8"))
    except:
        return None


def get_timestamp(days=None):
    if days is None:
        return str(int(time.time() * 1000))
    else:
        today = datetime.date.today()  # 获得今天的日期
        datetimeStr = today + datetime.timedelta(days)
        # 转换成时间戳
        timeArray = time.strptime(str(datetimeStr), "%Y-%m-%d")
        timestamp = time.mktime(timeArray)
        return int(timestamp) * 1000

# 获取当前时间的时间戳
def getTimeStamp():
    t = time.time()
    return int(t)


# 获取随机数
def get_randomNo():
    return random.randint(100000, 999999)

def getRandomNo(num=32):
    """
    获取一串随机数
    :param num 随机数的位数，默认32位随机数
    """
    if num == 16:
        return get_randomNo_16()
    else:
        return get_randomNo_32()


def generate_random_string(length):
    english_letters = string.ascii_letters
    chinese_characters = [chr(random.randint(0x4e00, 0x9fff)) for _ in range(length)]
    all_characters = english_letters + ''.join(chinese_characters)
    # 生成随机字符串
    random_string = ''.join(random.choice(all_characters) for _ in range(length))
    return random_string

# 统一社会信用代码中不使用I,O,Z,S,V
SOCIAL_CREDIT_CHECK_CODE_DICT = {
    '0': 0, '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16, 'H': 17, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22,
    'P': 23, 'Q': 24,
    'R': 25, 'T': 26, 'U': 27, 'W': 28, 'X': 29, 'Y': 30}
# GB11714-1997全国组织机构代码编制规则中代码字符集
ORGANIZATION_CHECK_CODE_DICT = {
    '0': 0, '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
    'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16, 'H': 17, 'I': 18, 'J': 19, 'K': 20, 'L': 21, 'M': 22,
    'N': 23, 'O': 24, 'P': 25, 'Q': 26,
    'R': 27, 'S': 28, 'T': 29, 'U': 30, 'V': 31, 'W': 32, 'X': 33, 'Y': 34, 'Z': 35}


# 返回windows不支持的特殊字符串
def get_not_support_str():
    return "\\/ : * ?< > |\""


# 返回其他的特殊字符串
def get_support_str(Strlen):
    str = ""
    baseStr = '~!@#$%^&()_+{}\'.,;=-`（）·！@#￥%……&——+、；‘，。、《》：“©™®℗'
    length = len(baseStr) - 1
    for i in range(Strlen):
        str += baseStr[random.randint(0, length)]
    return str


# 生成一个指定长度的随机字符串
def generate_random_str(Strlen):
    str = ""
    baseStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
    length = len(baseStr) - 1
    for i in range(Strlen):
        str += baseStr[random.randint(0, length)]
    return str

# 通过json文件存储数据，在debugtalk中添加以下函数，yml文件中调用  ${get_constant(name)} ，即可获取name对应的值
def get_constant(config_key):
    try:
        current_path = os.path.abspath(__file__)
        config_file_path = os.path.join(os.path.dirname(current_path), 'constant')
        with open(config_file_path, 'r', encoding="utf-8") as f:
            config_dict = json.load(f)
        return config_dict[config_key]
    except:
        return None


# 获取时间，如：2022-03-23 17:19:12
# days 正数：未来日期， 负数：以前日期
def get_dateTime(days):
    today = datetime.datetime.now() + datetime.timedelta(minutes=0.1)  # 获得今天的日期
    datetimeStr = today
    datetimeStr = datetimeStr + datetime.timedelta(days)
    # print(datetime.datetime.strftime(datetimeStr, '%Y-%m-%d %H:%M:%S'))
    return datetime.datetime.strftime(datetimeStr, '%Y-%m-%d %H:%M:%S')


def get_dateTime2(dateParam):
    today = datetime.datetime.now() + datetime.timedelta(minutes=0.1)  # 获得今天的日期
    datetimeStr = today
    if dateParam.get("days") is not None:
        datetimeStr = datetimeStr + datetime.timedelta(dateParam.get("days"))
    if dateParam.get("seconds") is not None:
        datetimeStr = datetimeStr + datetime.timedelta(seconds=dateParam.get("seconds"))
    # print(datetime.datetime.strftime(datetimeStr, '%Y-%m-%d %H:%M:%S'))
    return datetime.datetime.strftime(datetimeStr, '%Y-%m-%d %H:%M:%S')


def getBASE64String(content):
    try:
        bs = str(base64.b64encode(content.encode('utf-8')), "utf-8")
        return bs
    except:
        return None


from hashlib import sha256


def hmac_sha256_encrypt(data, secret):
    appsecret = secret.encode('utf-8')  # 秘钥
    if isinstance(data, (list, dict)):
        data = json.dumps(data)
    if data:
        str0 = data.encode('utf-8')  # 加密数据
        signature = hmac.new(appsecret, str0, digestmod=sha256).hexdigest()
        return signature
    else:
        data = ""
        str0 = data.encode('utf-8')  # 加密数据
        signature = hmac.new(appsecret, str0, digestmod=sha256).hexdigest()
        return signature
    return None


# 创建流程并获得流程ID
# params: isFiles-是否多文档签署， isAuto-是否静默签署, isOuter-是否含有相对方签署
def get_signFlow_signing(isFiles, isAuto, isOuter):
    try:
        return get_signFlow_signing3(isFiles, isAuto, isOuter, True)
    except:
        return None


# 创建流程并获得流程ID
# params: isFiles-是否多文档签署， isAuto-是否静默签署, isOuter-是否含有相对方签署,是否包含抄送人 containCC(True, False, False)
# 获取签署完成的流程 get_signFlow_signing(False, True, False)
# 获取纯内部的签署中流程 get_signFlow_signing(False, False,  False)
# 获取多文件的签署中流程 get_signFlow_signing(True, False, False)
# 获取多文件的签署中流程 containCC(True, False, False)
def get_signFlow_signing3(isFiles, isAuto, isOuter, containCC):
    try:
        return get_signFlow_signing4("", isFiles, isAuto, isOuter, containCC, 0)
    except:
        return None


def get_signFlow_signing4(businessNo, isFiles, isAuto, isOuter, containCC, ofdFlag):
    try:
        Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
        signFiles = []
        signerInfos = []
        sealInfo = []
        if ofdFlag == 1:
            fileKey = ofd_filekey
        else:
            fileKey = pdf_filekey
        signFile1 = signFlowGen.gen_signFile_data(fileKey)
        signFiles.append(signFile1)

        if isFiles:
            if ofdFlag == 1:
                fileKey2 = os.environ['3PageOFDFileKey']
            else:
                fileKey2 = os.environ['3PageFileKey']
            signFile2 = signFlowGen.gen_signFile_data(fileKey2)
            signFiles.append(signFile2)
        if isAuto:
            s1 = signFlowGen.gen_sealInfo_data(fileKey, "COMMON-SIGN", sign01_sealId, "", "")
            sealInfo.append(s1)
            if len(signFiles) > 1:
                sealInfo.append(
                    signFlowGen.gen_sealInfo_data(fileKey2, "COMMON-SIGN", sign01_sealId, "", ""))
            # 添加静默签署的签署内部个人信息
            signerInnerPersonAuto = signFlowGen.gen_signerInfo_autoSign_data(sealInfo, sign01_userCode, "",
                                                                             0, 1)
            signerInfos.append(signerInnerPersonAuto)
        else:
            signerInnerPerson = signFlowGen.gen_signerInfo_data(fileKey, sign01_userCode, "", 1, 1)
            signerInnerOrganize = signFlowGen.gen_signerInfo_data(fileKey, sign01_userCode,
                                                                  org01_orgCode, 1, 1)
            signerInfos.append(signerInnerPerson)
            signerInfos.append(signerInnerOrganize)

        if isOuter:
            signerOuterPerson = signFlowGen.gen_signerInfo_data(fileKey, wsignwb01_userCode, "", 2, 1)
            signerOuterOrganize = signFlowGen.gen_signerInfo_data(fileKey, wsignwb01_userCode,
                                                                  wsignwb01_main_orgCode, 2, 1)
            signerInfos.append(signerOuterPerson)
            signerInfos.append(signerOuterOrganize)

        if containCC:
            data = signFlowGen.gen_signFlow_data4(businessNo, signFiles, signerInfos, businessTypeCode,
                                                  getUserList(), getOrgList())
        else:
            data = signFlowGen.gen_signFlow_data_noCC2(businessNo, signFiles, signerInfos,
                                                       businessTypeCode)
        headers = gen_headers_signature(data)
        res = requests.post(url=Url, headers=headers, json=data)
        # 实地测试，有些静默签流程需要处理的时候 >= 3，故此处改为5
        time.sleep(5)
        jsonData = json.loads(res.text)["data"]
        if jsonData is None:
            print(res.text)
        try:
            signFlowId = jsonData["signFlowId"]
            return signFlowId
        except:
            print("方法get_signFlow_signing4()报错了")
            return None
    except:
        return None


# 创建部分签署成功的流程,第一个节点是顺序签节点静默签署
# isAuto-是否静默签署
# sSignMode 第二个节点模式
def get_part_success_signFlow(businessNo, sSignMode):
    reqUrl = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
    signFiles = []
    signers = []
    sealInfo = []
    # 获取fileKey
    fileKey = pdf_filekey
    # 组装签署文件
    signFile = signFlowGen.gen_signFile_data(fileKey)
    signFiles.append(signFile)

    # 节点一静默签添加
    s1 = signFlowGen.gen_sealInfo_data(fileKey, "COMMON-SIGN", sign01_sealId, "", "")
    sealInfo.append(s1)
    # 添加静默签署的签署内部个人信息
    signerInnerPersonAuto = signFlowGen.gen_signerInfo_autoSign_data(sealInfo, sign01_userCode, "", 0, 1)
    signers.append(signerInnerPersonAuto)

    # 节点二添加
    signerInnerPerson = signFlowGen.gen_signerInfo_data2(fileKey, 1, sign01_userCode,
                                                         org01_orgCode, sSignMode, 2)
    signers.append(signerInnerPerson)
    if sSignMode != 0:
        signerInnerPerson2 = signFlowGen.gen_signerInfo_data2(fileKey, 1, os.environ['signjz01.userCode'], "",
                                                              sSignMode,
                                                              2)
        signers.append(signerInnerPerson2)

    # build data and request
    data = signFlowGen.gen_signFlow_data3(signFiles, signers, businessNo)
    headers = gen_headers_signature(data)
    res = requests.post(url=reqUrl, headers=headers, json=data)
    # sleep and wait to auto sign success!
    time.sleep(6)
    # get response data info
    try:
        jsonData = json.loads(res.text)["data"]
        if jsonData is None:
            print(res.text)
        signFlowId = jsonData["signFlowId"]
        return signFlowId
    except:
        print("方法get_part_success_signFlow()报错了")


# 创建流程并获得流程ID
# params: isFiles-是否多文档签署， isAuto-是否静默签署, isOuter-是否含有相对方签署, type-签署主体类型 1个人 2机构
# 获取签署完成的流程 get_signFlow_signing(False, True, False)
# 获取纯内部的签署中流程 get_signFlow_signing(False, False, False)
# 获取多文件的签署中流程 get_signFlow_signing(True, False, False)
def get_sign_flow_signing_v3(isFiles, isAuto, isOuter, signType, signModel):
    Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
    signFiles = []
    signerInfos = []
    sealInfo = []
    signerInnerPersonAuto = ""
    fileKey = pdf_filekey

    signFile1 = signFlowGen.gen_signFile_data(fileKey)
    signFiles.append(signFile1)
    if isFiles:
        fileKey2 = ENV('1PageFileKey')
        signFile2 = signFlowGen.gen_signFile_data(fileKey2)
        signFiles.append(signFile2)

    if isAuto:
        s1 = signFlowGen.gen_sealInfo_data(fileKey, "COMMON-SIGN", sign01_sealId, "", "")
        sealInfo.append(s1)
        if len(signFiles) > 1:
            sealInfo.append(
                signFlowGen.gen_sealInfo_data(fileKey2, "COMMON-SIGN", sign01_sealId, "", ""))
        if signType == 1:
            # 添加静默签署的签署内部个人信息
            signerInnerPersonAuto = signFlowGen.gen_signerInfo_autoSign_data(sealInfo, sign01_userCode, "",
                                                                             0, 1)
        # if type == 2:
        # 添加静默签署的签署内部机构信息
        # signerInnerOrganizeAuto = signFlowGen.gen_signerInfo_autoSign_data(sealInfoOrg, sign01_userCode,
        #                                                                    org01_orgCode,1,1)
        signerInfos.append(signerInnerPersonAuto)
        # signerInfos.append(signerInnerOrganizeAuto)
    else:
        if signType == 1:
            signerInnerPerson = signFlowGen.gen_signerInfo_data(fileKey, sign01_userCode, "", 1, signModel)
            signerInfos.append(signerInnerPerson)
        if signType == 2:
            signerInnerOrganize = signFlowGen.gen_signerInfo_data(fileKey, sign01_userCode,
                                                                  org01_orgCode, 1, signModel)
            signerInfos.append(signerInnerOrganize)

    if isOuter:
        if signType == 1:
            signerOuterPerson = signFlowGen.gen_signerInfo_data(fileKey, wsignwb01_userCode, "", 2,
                                                                signModel)
            signerInfos.append(signerOuterPerson)
        if signType == 2:
            signerOuterOrganize = signFlowGen.gen_signerInfo_data(fileKey, wsignwb01_userCode,
                                                                  wsignwb01_main_orgCode, 2,
                                                                  signModel)
            signerInfos.append(signerOuterOrganize)

    data = signFlowGen.gen_signFlow_data(signFiles, signerInfos, businessTypeCode, getUserList(),
                                         getOrgList())

    headers = gen_headers_signature(data)
    res = requests.post(url=Url, headers=headers, json=data)
    try:
        signFlowId = json.loads(res.text)["data"]["signFlowId"]
        return signFlowId
    except:
        print("方法get_sign_flow_signing_v3()报错了")
        return None


# 发起简单的流程，支持自定义流程主题，流程编号，签署有效期
# 发起businessNo 唯一的流程
# 发起10秒后过期的流程 get_signFlow_signing2("需要过期的流程", "", get_dateTime2({"seconds": 10}))
def get_signFlow_signing2(subject, businessNo, signFlowExpireTime):
    try:
        Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"

        data = signFlowGen.gen_signFlow_data2(subject, businessNo, signFlowExpireTime, businessTypeCode,
                                              getUserList(), getOrgList(), pdf_filekey)

        headers = gen_headers_signature(data)
        res = requests.post(url=Url, headers=headers, json=data)
        time.sleep(2)
        if json.loads(res.text)["data"] is not None:
            try:
                signFlowId = json.loads(res.text)["data"]["signFlowId"]
            except:
                print("方法get_signFlow_signing2()报错了")
                return None
            if signFlowExpireTime:
                time.sleep(5)
                if get_dateTime(0.1) > signFlowExpireTime:
                    status = get_signFlowDetail_signFlowStatus(signFlowId)
                    count = 0
                    while status != 3:
                        status = get_signFlowDetail_signFlowStatus(signFlowId)
                        time.sleep(10)
                        count = count + 1
                        if count > 10:
                            break
            return signFlowId
    except:
        return None


# 为了兼容v6.0.4.0版本删除接口的，临时构造的方法
# {"subject":subject,"initiatorInfo":initiatorInfo,"signFiles":signFiles,"signerInfos":singers,
# "businessNo":"businessNo","businessTypeCode":businessTypeCode,"CCInfos":CCInfos,
# "signFlowExpireTime":signFlowExpireTime,"replaceObject":replaceObject}
def get_signFlow_signing_all(params):
    Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
    try:

        if params.__contains__("data") == False:
            if params.__contains__("subject") == False:
                subject = 'PY-测试流程'
            else:
                subject = params["subject"]
            if params.__contains__("businessNo") == False:
                businessNo = ''
            else:
                businessNo = params["businessNo"]
            if params.__contains__("initiatorInfo") == False:
                initiatorInfo = {"organizationCode": ci_orgCode, "userCode": csqs_userCode, "userType": 1}
            else:
                initiatorInfo = params["initiatorInfo"]
            if params.__contains__("signFiles") == False & params.__contains__("signerInfos") == False:
                if params.__contains__("ofdFlag") == False:
                    fileKey = pdf_filekey
                else:
                    fileKey = ofd_filekey
                signFiles = [{"fileKey": fileKey}]
                signerInfos = [
                    {"sealInfos": signFiles, "signNode": 1, "signMode": 0, "userType": 1, "userCode": csqs_userCode}]
            elif params.__contains__("signFiles") == False & params.__contains__("signerInfos") == True:
                fileKey = params["signerInfos"]["sealInfos"]["fileKey"]
                signFiles = [{"fileKey": fileKey}]
            elif params.__contains__("signFiles") == True & params.__contains__("signerInfos") == False:
                signFiles = params["signFiles"]
                # 默认添加内部个人签署
                signerInfos = [
                    {"sealInfos": signFiles, "signNode": 1, "signMode": 0, "userType": 1, "userCode": csqs_userCode}]
            else:
                signFiles = params["signFiles"]
                signerInfos = params["signerInfos"]
            if params.__contains__("CCInfos") == False:
                CCInfos = []
            else:
                CCInfos = params["CCInfos"]
            if params.__contains__("signFlowExpireTime") == False:
                signFlowExpireTime = ''
            else:
                signFlowExpireTime = params["signFlowExpireTime"]
            data = gen_signFlow_data6(subject, businessNo, businessTypeCode, initiatorInfo, signFiles, signerInfos,
                                      CCInfos, signFlowExpireTime)
        else:
            data = params["data"]
        if params.__contains__("replaceObject") & len(params["replaceObject"]) > 0:
            for key, value in params["replaceObject"].items():
                data[key] = value
        print('流程发起数据: ', data)
        headers = gen_headers_signature(data)
        res = requests.post(url=Url, headers=headers, json=data)
        print('流程发起结果: ', res.text)
        time.sleep(0.5)
        if json.loads(res.text)["data"] is not None:
            try:
                signFlowId = json.loads(res.text)["data"]["signFlowId"]
                print('流程发起signFlowId: ', signFlowId)
            except:
                print(sys._getframe().f_code.co_name + "创建流程异常")
                return None
            return signFlowId
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 发起一签署中的流程，按需修改
def get_signFlow_signing_config(signtureConfig: SigntureConfig):
    Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"

    data = signFlowGen.gen_signFlow_data5(signtureConfig, getUserList(), getOrgList())
    headers = gen_headers_signature(data)
    res = requests.post(url=Url, headers=headers, json=data)
    time.sleep(10)
    if json.loads(res.text)["data"] is not None:
        try:
            signFlowId = json.loads(res.text)["data"]["signFlowId"]
        except:
            print("方法get_signFlow_signing2()报错了")
            return None
        if get_dateTime(0.1) > signtureConfig.expireTime:
            status = get_signFlowDetail_signFlowStatus(signFlowId)
            count = 0
            while status != 3:
                status = get_signFlowDetail_signFlowStatus(signFlowId)
                time.sleep(10)
                count = count + 1
                if count > 10:
                    break
        return signFlowId


# 获取流程的签署地址
def get_signFlowUrls(signFlowId):
    sleep(0.5)
    try:
        Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/signUrls"
        data = "signFlowId=" + signFlowId
        Url = Url + "?" + data
        headers = gen_headers_signature(data)
        res = requests.get(url=Url, headers=headers)
        print('get_signFlowUrls: ', res.text)
        if json.loads(res.text)["data"] is not None:
            try:
                signFlowDetail = json.loads(res.text)["data"]["signUrlInfos"]
            except:
                print("方法get_signFlowDetail()报错了")
                return None
            return signFlowDetail
    except:
        return None


# 获取流程的详情json,能够知道流程的状态，签署人，发起人，签署文档
def get_signFlowDetail(signFlowId):
    try:
        Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/signDetail"

        data = "signFlowId=" + signFlowId
        Url = Url + "?" + data
        headers = gen_headers_signature(data)
        res = requests.get(url=Url, headers=headers)
        if json.loads(res.text)["data"] is not None:
            try:
                signFlowDetail = json.loads(res.text)["data"]
            except:
                print("方法get_signFlowDetail()报错了")
                return None
            return signFlowDetail
    except:
        return None


# signDetail获取流程的流程状态
def get_signFlowDetail_signFlowStatus(signFlowId):
    try:
        detail = get_signFlowDetail(signFlowId)
        if detail:
            return detail["signFlowStatus"]
        else:
            return None
    except:
        print("get_signFlowDetail_signFlowStatus 方法报错了")
        return None


def get_autoSign_fail():
    '''
    获取静默签署失败的流程Id
    :return: signFlowId
    '''
    Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"

    fileKey = ENV('editEncryptionFileKey')
    data = autoSignByFile(businessTypeCode, fileKey, sign01_userCode,
                          org01_orgCode)
    headers = gen_headers_signature(data)
    res = requests.post(url=Url, headers=headers, json=data)
    sleep(5)
    print(res.json())
    if json.loads(res.text)["data"] is not None:
        try:
            signFlowId = json.loads(res.text)["data"]["signFlowId"]
        except:
            print(sys._getframe().f_code.co_name + "-调用异常")
            return None
        return signFlowId


def get_business_no():
    return "biz-" + get_randomNo_str()


# 作废流程并返回新的作废流程id
def get_cancel_process_id():
    # 创建静默签流程
    try:
        signFlowId = get_signFlow_signing(False, True, False)
        # 返回新的作废流程id
        return get_signFlow_revoke(signFlowId)
    except:
        print("get_cancel_process_id()方法报错了")
        return None


# 创建流程并催办，返回被催办的流程id
def get_urge_process_id():
    try:
        # 创建流程
        signFlowId = get_signFlow_signing(False, False, False)

        # 催办
        Url = MAIN_HOST + "/esign-signs/v1/signFlow/urge"
        data = {"signFlowId": signFlowId}
        token = getPortalToken(sign01_customAccountNo, password_encrypt)
        headers = {"Content-Type": "application/json", "x-timevale-project-id": PROJECT_ID,
                   "x-timevale-signature": '', "authorization": token}
        res = requests.post(url=Url, headers=headers, json=data)
        try:
            if json.loads(res.text)["code"] == 200:
                return signFlowId
            else:
                print("errro")
                return None
        except:
            return None
    except:
        return None


# 创建两个人无序签流程并openApi催办其中一个人，返回被催办的流程id
def get_open_api_urge_process_id(business_no):
    try:
        # 创建流程
        signFlowId = get_signFlowId_status(1, business_no, True, True, 1, 1, 0)
        sleep(5)
        # openApi催办
        Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/urge"
        data = {
            "signFlowId": signFlowId
        }
        headers = {"Content-Type": "application/json", "x-timevale-project-id": PROJECT_ID}
        res = requests.post(url=Url, headers=headers, json=data)
        print(json.loads(res.text))
        if json.loads(res.text)["code"] == 200:
            return signFlowId
        else:
            return signFlowId
    except:
        print("get_open_api_urge_process_id method error")
        return None


# status = 10 发起一个相对方计费的流程,get_signFlowId_status(10, None, None, None, None, None,0)
# status = 9 获取过期时间为当前日期后一天时间
# status = 8 静默签 签署状态为已完成
# status = 7 签署失败，流程状态仍未签署中， 签署状态为失败
# status = 6 (status = 2签署完成被作废，status =6作废中)
# status = 5 (status = 1签署中被作废时，status =5已作废)
# status = 4 拒签 调用方式get_signFlowId_status(4, get_business_no(), False, True, 0,1,0)
# status = 3 过期
# status = 2 已完成
# status = 1 签署中
# status = 0 草稿态
# userType 1 内部  2 外部  3 内外混合
# organize= true 机构签
def get_signFlowId_status(status, businessNo, person, organize, signMode, userType, ofdFlag):
    print('发起流程的状态： ', status)
    if status == 0:
        try:
            return gen_startSignFlow_node(0)
        except:
            print("gen_startSignFlow_node method error")
            return None

    if status == 1:
        Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
        if ofdFlag == 1:
            fileList = getFileKeyForSignFlowGen(1)
        else:
            fileList = getFileKeyForSignFlowGen(0)
        data = signFlowGen.gen_sign_flow_data3(businessNo, person, organize, signMode, userType, status,
                                               businessTypeCode, getUserList(), getOrgList(),
                                               fileList)
        headers = gen_headers_signature(data)
        print('[request]： ', data)
        res = requests.post(url=Url, headers=headers, json=data)
        jsonData = json.loads(res.text)
        sleep(3)
        if jsonData["code"] != 200 or jsonData["data"] is None:
            print(res.text)
            return None
        try:
            signFlowId = jsonData["data"]["signFlowId"]
            return signFlowId
        except:
            print("status == 1,get_signFlowId_status()方法报错了")
            return None

    if status == 2:
        return get_signFlow_signing4(businessNo, False, True, False, False, ofdFlag)

    if status == 3:
        time = {"seconds": 5}
        return get_signFlow_signing2("自动化case-" + get_randomNo_str(), businessNo, get_dateTime2(time))

    if status == 4:
        try:
            data = get_refusing_data2(businessNo, person, organize, signMode, userType)
            rejectUrl = MAIN_HOST + "/esign-signs/process/refuseFlow"
            headers = get_headers_with_autho(PROJECT_ID)
            res = requests.post(rejectUrl, json=data, headers=headers)
            print(str(res.json()))
            return data.get("params").get("processId")
        except:
            print("status == 4,get_signFlowId_status()方法报错了")
            return None

    if status == 5:
        try:
            signFlowId = get_signFlowId_status(1, businessNo, person, organize, signMode, userType, ofdFlag)
            sleep(1)
            rejectUrl = OPENAPI_HOST + "/esign-docs/v1/signFlow/revoke"
            data = {"reason": "OPENAPI作废接口作废", "signFlowId": signFlowId}
            headers = gen_headers_signature(data)
            res = requests.post(rejectUrl, json=data, headers=headers)
            if res.json()['code'] == 200:
                sleep(0.2)
                return signFlowId
        except:
            print("status == 5,get_signFlowId_status()方法报错了")
            return None

    if status == 6:
        try:
            signFlowId = get_signFlowId_status(1, businessNo, person, organize, signMode, userType, ofdFlag)
            # 非纯内部签署时
            if userType != 2:
                innerHeader = get_headers_with_autho(PROJECT_ID)
                sign_wrap(signFlowId, organize, innerHeader, 1)
            # 非纯外部签署时
            if userType != 1:
                outUserCode = wsignwb01_userCode
                outerHeader = get_headers_with_outer_person(outUserCode)
                signtureParams = {
                    "sealTypeCode": None,
                    "organizeCode": None,
                    "sealId": os.environ['wsignwb01.sealId'],
                    "accountSaasId": wsignwb01_saasId,
                    "userMobile": get_outeruser_customAccountNo(outUserCode,"mobile"),
                    "userCode": outUserCode
                }
                sign_wrap_outer_account(signFlowId, signtureParams, outerHeader)
            rejectUrl = OPENAPI_HOST + "/esign-docs/v1/signFlow/revoke"
            data = {"reason": "OPENAPI作废接口作废", "signFlowId": signFlowId}
            headers = gen_headers_signature(data)
            requests.post(rejectUrl, json=data, headers=headers)
            return signFlowId
        except:
            print("status == 6,get_signFlowId_status()方法报错了")
            return None

    if status == 7:
        Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
        if ofdFlag == 1:
            fileList = getFileKeyForSignFlowGen(1)
        else:
            fileList = getFileKeyForSignFlowGen(0)
        data = signFlowGen.gen_sign_flow_data3(businessNo, person, organize, signMode, userType, status,
                                               businessTypeCode, getUserList(), getOrgList(),
                                               fileList)
        headers = gen_headers_signature(data)
        res = requests.post(url=Url, headers=headers, json=data)
        jsonData = json.loads(res.text)
        try:
            if jsonData["data"] is None:
                print(res.text)
            signFlowId = jsonData["data"]["signFlowId"]
            headers = get_headers_with_autho(PROJECT_ID)
            sign_wrap(signFlowId, False, headers, 1)
            return signFlowId
        except:
            print("status == 7,方法get_signFlowId_status报错了")
            return None

    if status == 8:
        return get_signFlow_signing4(businessNo, False, True, False, False, 0)
    if status == 9:
        return get_signFlow_signing2("自动化case-" + get_randomNo_str(), None, get_dateTime(1))

    if status == 10:
        Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
        data = signFlowGen.signCharging(businessTypeCode, pdf_filekey, ci_orgCode,
                                        csqs_userCode, wsignwb01_userCode,
                                        wsignwb01_main_orgCode)
        headers = gen_headers_signature(data)
        res = requests.post(url=Url, headers=headers, json=data)
        jsonData = json.loads(res.text)
        if jsonData["code"] != 200 or jsonData["data"] is None:
            print(res.text)
            return None
        try:
            signFlowId = jsonData["data"]["signFlowId"]
            return signFlowId
        except:
            print("status == 10,get_signFlowId_status()方法报错了")
            return None


# 拒签的前置数据准备
# preson 0 organize 1  = 组织
# signMode 0 随机生成
# userType 1 内部  2 外部  3 内外混合
def get_refusing_data(person, organize, signMode, userType):
    try:
        signFlowId = get_signFlowId_status(1, get_business_no(), person, organize, signMode, userType, 0)
        # 调用意愿的接口，保存applyId到actor中，并返回applyId和密码验证的重定向地址
        headers = get_headers_with_autho(PROJECT_ID)
        willReturn = willing(signFlowId, headers, organize, 1)
        # 验证密码的token解析和组装
        token = parse_tokenKey(willReturn['url'])
        applyId = willReturn["applyId"]
        password_auth(applyId, password_encrypt, token)
        if organize:
            organizationCode = org01_orgCode
        else:
            organizationCode = ""
        userCode = sign01_userCode
        data = {"params": {"processId": signFlowId,
                           "refuseReason": "test",
                           "userCode": userCode,
                           "organizeCode": organizationCode,
                           "applyId": applyId}
                }
        return data
    except:
        return None


# 拒签的前置数据准备
# preson 0 organize 1  = 组织
# signMode 0 随机生成
# userType 1 内部  2 外部  3 内外混合
def get_refusing_data2(businessNo, person, organize, signMode, userType):
    try:
        signFlowId = get_signFlowId_status(1, businessNo, person, organize, signMode, userType, 0)
        # 签署人状态
        status = 0
        while status != 1:
            time.sleep(2)
            headers = get_headers_with_autho(PROJECT_ID)
            detail = get_detail(signFlowId, organize, headers, 1)
            status = detail["signInfo"][0]["list"][0]["signStatus"]
        # 调用意愿的接口，保存applyId到actor中，并返回applyId和密码验证的重定向地址
        headers = get_headers_with_autho(PROJECT_ID)
        willReturn = willing(signFlowId, headers, organize, 1)
        # 验证密码的token解析和组装
        token = parse_tokenKey(willReturn['url'])
        applyId = willReturn["applyId"]
        password_auth(applyId, password_encrypt, token)
        if organize:
            organizationCode = org01_orgCode
        else:
            organizationCode = ""
        userCode = sign01_userCode
        data = {"params": {"processId": signFlowId,
                           "refuseReason": "test",
                           "userCode": userCode,
                           "organizeCode": organizationCode,
                           "applyId": applyId}
                }
        return data
    except:
        return None


# 带有内部个人签署的流程的拒签数据准备
def get_refusing_data3(signFlowId):
    try:
        # 签署人状态
        status = 0
        organize = False
        while status != 1:
            time.sleep(2)
            headers = get_headers_with_autho(PROJECT_ID)
            detail = get_detail(signFlowId, organize, headers, 1)
            status = detail["signInfo"][0]["list"][0]["signStatus"]
        # 调用意愿的接口，保存applyId到actor中，并返回applyId和密码验证的重定向地址
        headers = get_headers_with_autho(PROJECT_ID)
        willReturn = willing(signFlowId, headers, organize, 1)
        # 验证密码的token解析和组装
        token = parse_tokenKey(willReturn['url'])
        applyId = willReturn["applyId"]
        password_auth(applyId, password_encrypt, token)
        if organize:
            organizationCode = org01_orgCode
        else:
            organizationCode = ""
        userCode = sign01_userCode
        data = {"params": {"processId": signFlowId,
                           "refuseReason": "test",
                           "userCode": userCode,
                           "organizeCode": organizationCode,
                           "applyId": applyId}
                }
        return data
    except:
        return None


# node 节点
# nodeIndex 节点的第几个签署人
def assemble_sealInfos(docReturn, sealParams):
    try:
        headers = get_headers_with_autho(PROJECT_ID)
        signFlowId = docReturn["signFlowId"]
        sealInfos = []
        for i in range(len(sealParams)):
            sealParam = sealParams[i]
            node = sealParam['node']
            nodeIndex = sealParam['nodeIndex']
            organize = sealParam['organize']
            # 签署方身份 0个人 1机构 ,示例值(1)
            signIdentity = 1
            # 签署方身份 1个人 2机构 3法人章 ,示例值(1)
            sealIdentityType = 1
            if organize:
                signIdentity = 2
                sealIdentityType = 2
            actorId = get_proccess_actor_uuid(signFlowId, node, nodeIndex)
            fileKey = docReturn["fileKeys"][i]
            cryptoFileKey = docReturn["cryptoFileKeys"][i]
            pageNo = sealParam['pageNo']
            isTime = sealParam['isTime']
            sealSignTimeFormat = 1
            isAddSealSignTime = 0
            if isTime > 0:
                isAddSealSignTime = 1
                sealSignTimeFormat = isTime
            edgeScope = sealParam['edgeScope']
            # signType 签署类型 1普通 2骑缝,示例值(1)
            signType = 1
            if edgeScope is not None:
                signType = 2
            if sealParam['keyList'] is not None:
                keyList = sealParam['keyList']
                pageRange = sealParam['pageRange']
                keyPositions = keyWordParsing(fileKey, cryptoFileKey, 1, keyList, pageRange, headers)
                keySealInfos = gen_sealInfo_with_keyWord([], keyPositions, fileKey, actorId, pageRange,
                                                         sealIdentityType,
                                                         isAddSealSignTime, sealSignTimeFormat, signIdentity, signType,
                                                         None, keyList)
                keyIdx = sealParam['keyIndex']
                if keyIdx is not None and (keyIdx == 'first' or keyIdx == 'last'):
                    idx = 0
                    if keyIdx == 'last':
                        idx = len(keyPositions) - 1
                    defSealInfo = keySealInfos[idx]
                    sealInfos.append(defSealInfo)
                else:
                    sealInfos = keySealInfos
            else:
                sealInfo = gen_sealInfo(fileKey, actorId, pageNo, 150, 150, None, 'group1', None, sealIdentityType,
                                        signIdentity, signType, sealSignTimeFormat, None, isAddSealSignTime, edgeScope)
                sealInfos.append(sealInfo)
        return sealInfos
    except:
        return None


# readComplete
def readComplete(processId):
    headers = get_headers_with_autho(PROJECT_ID)
    actorId = get_proccess_actor_uuid(processId, 0, 0)
    data = {
        "params": {
            "actorId": actorId,
            "processId": processId
        }
    }
    saveUrl = MAIN_HOST + "/esign-signs/process/readComplete"
    res = requests.post(url=saveUrl, headers=headers, json=data)
    try:
        if json.loads(res.text)["status"] != 200:
            print(res.text)
    except:
        return None


# signModel 0 顺序签 1 无序签 2 或签 仅支持不同账号
def get_signers(singerParamList, singers):
    orSingers = []
    for i in range(len(singerParamList)):
        singerParam = singerParamList[i]
        lists = singerParam['list']
        signModel = singerParam['signModel']
        for i2 in range(len(lists)):
            list = lists[i2]
            info = {'signer': list['signer'], 'organize': list['organize'], 'useType': list['useType']}
            if signModel < 2:
                singers.append(info)
            if signModel == 2:
                orSingers.append(info)
    if len(orSingers) > 0:
        singers.append(orSingers[0])


def get_sort(singerParamList):
    for i in range(len(singerParamList)):
        singerParam = singerParamList[i]
        signModel = singerParam['signModel']
        if signModel != 0:
            return 0
    return 1


def get_singerParamList(organize, sealParams, singers):
    try:
        singerParamList = []
        for i in range(len(sealParams)):
            sealParam = sealParams[i]
            singerIndex = sealParam['signer']
            if singerIndex not in singers:
                singers.append(singerIndex)
                singerParam = {
                    'signModel': 0,
                    'list': [{'organize': organize, 'useType': 1, 'signer': singerIndex}]
                }
                singerParamList.append(singerParam)

        return singerParamList
    except:
        return None


def get_sign_headers(useType, singerIndex):
    try:
        if useType == 1:
            accountCode = sign01_customAccountNo
            if singerIndex == 2:
                accountCode = csqs_userCode
            return get_headers_with_accountCode(accountCode)
        if useType == 2:
            if singerIndex == 1:
                return get_headers_with_outer_person(wsignwb01_userCode)
            else:
                return get_headers_with_outer_person(wsignwb01_userCode)
    except:
        return None


# 支持签署的印章id
def get_sign_sealId(useType, singerIndex):
    try:
        sealId = None
        if useType == 1:
            sealId = sign01_sealId
            if singerIndex == 2:
                sealId = os.environ['userCodeNoCertSealId']
            return sealId
        if useType == 2:
            if singerIndex == 1:
                return sealId
            if useType == 2:
                if singerIndex == 1:
                    return sealId
    except:
        return None


# 外部印章id

def get_sign_seal_Id(headers, organizeCode, processId):
    data = {"params": {
        "organizeCode": organizeCode,
        "processId": processId}
    }
    url = MAIN_HOST + "/esign-signs/seals/list"
    res = requests.post(url=url, headers=headers, json=data)
    try:
        data = {"params": {
            "organizeCode": organizeCode,
            "processId": processId}
        }
        url = MAIN_HOST + "/esign-signs/seals/list"
        res = requests.post(url=url, headers=headers, json=data)
        try:
            jsonData = json.loads(res.text)
            if jsonData["status"] != 200:
                raise Exception(res.text)
        except:
            print("get_out_seal_Id()报错啦：" + res.text)
        if organizeCode is None:
            personalSeal = res.json()['data']['personalSeal']
            seals = personalSeal['seals']
            seal0 = seals[0]
            sealId = seal0['sealId']
        else:
            officialSeal = res.json()['data']['officialSeal']
            seals = officialSeal['seals']
            seal0 = seals[0]
            sealId = seal0['sealId']
        return sealId
    except:
        return None


def get_sign_sealTypeCode(useType):
    if useType == 1:
        sealId = org01_sealId
        return gen_sealTypeCode(sealId)
    if useType == 2:
        return "PUBLIC"


# 支持签署的userCode
def get_sign_UserCode(useType, singerIndex):
    if useType == 1:
        userCode = sign01_userCode
        if singerIndex == 2:
            userCode = csqs_userCode
        return userCode
    if useType == 2:
        if singerIndex == 1:
            userCode = wsignwb01_userCode
            return userCode


# 支持签署的机构信息
def get_sign_organizeInfo(useType, singerIndex):
    try:
        name = ""
        organizationCode = ""
        if useType == 1:
            if singerIndex == 1:
                organizationCode = org01_orgCode
            if singerIndex == 2:
                organizationCode = ci_orgCode
            name = os.environ['sign01.main.orgName']
        if useType == 2:
            if singerIndex == 1:
                projectId = PROJECT_ID
                projectSecrect = PROJECT_SECRET
                outerUserCode = wsignwb01_userCode
                esignManageOpenApiUrl = OPENAPI_HOST
                userInfo = detailOuterUsers(esignManageOpenApiUrl, projectId, projectSecrect, outerUserCode)
                organizationCode = userInfo['mainOrganizationCode']
                organizationInfo1 = detailOuterOrganizations(OPENAPI_HOST,
                                                             PROJECT_ID,
                                                             PROJECT_SECRET, organizationCode)
                name = organizationInfo1['name']
        organizationInfo = {
            'organizationCode': organizationCode,
            'organizationName': name
        }
        return organizationInfo
    except:
        return None


# 签署的前置流程数据准备
# 签署区配置
# condition 0 默认 1 是否带附件 2 抄送内部个人 3抄送相对方个人 4 强制完成阅读 5 签署需知 6 旋转角度
# conditon为负数-杨一， -1 短信意愿
# signModel 0 顺序签 1 无序签 2 或签
# isTime 0 不带时间 1 yyyy-MM-dd 2yyyy年MM月dd日 3yyyy/MM/dd
# signer  1 内部账号userAccountNumber 2 内部账号
# signModel 0 顺序签 1 无序签 2 或签
# 签署人配置 singerParamList [{
#                 'signModel': 0,
#                 'list': [{'organize': organize, 'useType': 1, 'signer': 1}]
#             }]
# free 是否自由签 False True
# V6.0.4.0改造
def get_standardSign_data(free, docReturn, sealParams, singerParamList, condition, subject, isUsed=None):
    print('get_standardSign_data 调用入参: ', free, docReturn, sealParams, singerParamList, condition, subject)
    try:
        userCode0 = sign01_userCode
        fileKey0 = pdf_filekey
        signConfigs = []
        singers = []
        signMode = 0
        node = 1
        if singerParamList:
            node = len(singerParamList)
            for index in range(0, node):
                signConfigs = []
                if free:
                    if sealParams:
                        for seal in sealParams:
                            if index != seal['node']:
                                break
                            signConfig = {"posX": "200.01", "posY": "200", "pageNo": "1", "node": seal['node'],
                                          "nodeIndex": seal['nodeIndex']}
                            signConfig['signatureType'] = "PERSON-SEAL"
                            signConfig['signType'] = "COMMON-SIGN"
                            signConfig['pageNo'] = seal['pageNo']
                            if seal['isTime'] == 1:
                                signConfig['addSignDate'] = True
                                signConfig['sealSignDatePositionInfo'] = {
                                    "sealSignDatePositionInfo": {"fontSize": 28, "posX": 0.5, "posY": 0.5,
                                                                 "sealSignDateFormat": 3}}
                            if seal['keyList']:
                                signConfig['keywordInfo'] = {"keywordIndex": -1, "offsetPosX": 5, "offsetPosY": 10,
                                                             "keyword": seal['keyList'][0]}
                                signConfig['pageNo'] = seal['pageRange']
                                signConfig['signType'] = "KEYWORD-SIGN"
                            if seal['edgeScope']:
                                signConfig['signType'] = "EDGE-SIGN"
                                signConfig['edgeScope'] = seal['edgeScope']
                                if seal['edgeScope'] != 0:
                                    signConfig['pageNo'] = ''
                            if seal['organize']:
                                signConfig['signatureType'] = "COMMON-SEAL"
                            signConfigs.append(signConfig)
                    else:
                        signConfigs = [{"pageNo": "1", "posX": 233.1, "posY": 203.1, "signType": "COMMON-SIGN",
                                        "signatureType": "PERSON-SEAL"}]

                item = singerParamList[index]
                signMode = item['signModel']
                for item2 in item['list']:
                    organizationCode0 = ''
                    useType = item2['useType']
                    organize = item2['organize']
                    signer = item2['signer']
                    if useType == 2:
                        userCode0 = wsignwb01_userCode
                    if organize:
                        if useType == 2:
                            organizationCode0 = worg01_orgCode
                        else:
                            if signer == 2:
                                userCode0 = csqs_userCode
                                organizationCode0 = csqs_main_orgCode
                            else:
                                organizationCode0 = org01_orgCode
                    else:
                        if useType == 2:
                            userCode0 = wsignwb01_userCode
                        else:
                            if signer == 2:
                                userCode0 = csqs_userCode
                    singer = {"sealInfos": [{"fileKey": fileKey0, "signConfigs": signConfigs}], "signMode": signMode,
                              "signNode": index,
                              "userCode": userCode0, "organizationCode": organizationCode0, "userType": useType,
                              'organize': organize, 'signer': signer}
                    singers.append(singer)
        else:
            singers = [{"sealInfos": [{"fileKey": fileKey0, "signConfigs": signConfigs}], "signMode": 0, "signNode": 1,
                        "userCode": userCode0, "userType": 1, 'organize': False, 'signer': 1}]

        if signMode != 0 and len(singers) > 0 and node == 1 and len(singerParamList[0]['list']) == 1:
            singer = {"sealInfos": [{"fileKey": fileKey0, "signConfigs": signConfigs}],
                      "signMode": signMode, "signNode": index,
                      "userCode": sign01_userCode, "organizationCode": csqs_main_orgCode, "userType": 1,
                      'organize': organize, 'signer': signer}
            singers.append(singer)

        # 发起人
        initiatorInfo = {"userCode": csqs_userCode, "userType": 1}
        signFiles = [{"fileKey": fileKey0}]
        replaceObject = {}
        if condition == 2:
            replaceObject = {"replaceObject": {"CCInfos": [initiatorInfo]}}
        if condition == 3:
            replaceObject = {"replaceObject": {
                "CCInfos": [{"organizationCode": worg01_orgCode, "userCode": wsignwb01_userCode, "userType": 2}]}}
        if condition == 4:
            replaceObject = {"replaceObject": {"readComplete": True}}
        if condition == 5:
            replaceObject = {"replaceObject": {"advertisement": True}}
        param = {"subject": subject, "initiatorInfo": initiatorInfo, "signFiles": signFiles, "signerInfos": singers,
                 "replaceObject": replaceObject}

        #####加一段逻辑===查询当天sign01签署方的相同流程主题的签署中流程，重复使用 ,isUsed是否允许使用历史数据#######
        if isUsed == 1:
            url0 = MAIN_HOST + "/portal/task/queryUndoTask"
            time = {"seconds": "-600"}
            startTime = get_dateTime(-0.1)
            data = {"domain": "unified_portal_service",
                    "params": {"pageSize": 10, "currPage": 1, "businessId": "", "workflowConfigName": subject,
                               "startUserName": "", "workflowCategory": "", "timeType": 1, "startTime": startTime,
                               "endTime": "2029-07-13 23:59:59"}}
            header = gen_unified_portal_header()
            res = requests.post(url=url0, json=data, headers=header)
            res0 = json.loads(res.text)
            if res0.get("status") == 200 and res0.get("data").get('totalCount') > 0:
                list01 = res0.get("data").get('list')
                index = len(list01)
                signFlowId = list01[index-1].get('signFlowId')
            else:
                isUsed = 0
        if isUsed == None or isUsed == 0:
            signFlowId = get_signFlow_signing_all(param)

        # 签署
        for i in range(len(singers)):
            signInfo = singers[i]
            singerIndex = signInfo['signer']
            useType = signInfo['userType']
            organize = signInfo['organize']
            headers3 = get_sign_headers(useType, singerIndex)
            # 强制完成阅读
            if condition == 4:
                readComplete(signFlowId)
            if useType == 2:
                organizeCode = None
                if useType == 2:
                    if organize:
                        organizationInfo = get_sign_organizeInfo(useType, singerIndex)
                        organizeCode = organizationInfo['organizationCode']
                # 请求公有云并选择验证码校验
                sassId = wsignwb01_saasId
                willReturn = willing_out(signFlowId, headers3, organizeCode)
                applyId = willReturn["applyId"]
                # 请求公有云并选择验证码校验
                createCodeAuthRes = createCodeAuth(sassId, applyId)
                # 请求公有云校验短信验证码
                verifyCodeAuth(createCodeAuthRes['bizId'], createCodeAuthRes['willAuthId'])
            else:
                # 调用意愿的接口，保存applyId到actor中，并返回applyId和密码验证的重定向地址
                willReturn = willing_in(signFlowId, organize, singerIndex)
                applyId = willReturn["applyId"]
                # 验证密码的token解析和组装
                token = parse_tokenKey(willReturn['url'])
                if condition == -1:
                    message_willing(applyId, '123456', token)
                else:
                    password_auth(applyId, password_encrypt, token)
            data = gen_standardSign_data(organize, applyId, signFlowId, condition, singerIndex, useType)
            print('过程数据222===', data)
            if len(singers) == 1:
                return data
            if len(singers) > 1:
                if i < len(singers) - 1:
                    # 签署
                    obj1 = standard_flow_sign1(data, useType, singerIndex)
                    print('get_standardSign_data-签署结果信息===',obj1)
                    if get_signFlowDetail_signFlowStatus(signFlowId) == 2:
                        return data
                else:
                    return data
    except Exception as e:
        print('签署失败：', e.args)
        return None


# specifySealType 是否指定印章类型
# readComplete 是否强制阅读完成
# readTime  是否需要阅读时长
# removeSealInfo 移除签署区信息
##以下参数只有一个能为true，多个为true不保证方法的正确性
def customize_sign_data(specifySealType, readComplete, readTime, removeSealInfo, largeFile, ofdFlag):
    Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
    if ofdFlag == 1:
        fileList = getFileKeyForSignFlowGen(1)
    else:
        fileList = getFileKeyForSignFlowGen(0)
    data = signFlowGen.gen_sign_flow_data3(get_business_no(), False, True, 0, 1, 1,
                                           businessTypeCode, getUserList(), getOrgList(),
                                           fileList)
    if specifySealType:
        data["signerInfos"][0]["sealTypeCode"] = "COMMON-SEAL"
    if readComplete:
        data["readComplete"] = 1
    if readTime:
        data["businessTypeCode"] = businessTypeCode
    if removeSealInfo:
        data["signerInfos"][0]["sealInfos"] = None
    if largeFile:
        largeFileKey = fileKey_big_332MB
        data["signFiles"][0]["fileKey"] = largeFileKey
        data["signerInfos"][0]["sealInfos"][0]["fileKey"] = largeFileKey
    headers = gen_headers_signature(data)
    res = requests.post(url=Url, headers=headers, json=data)
    jsonData = json.loads(res.text)
    if jsonData["code"] != 200 or jsonData["data"] is None:
        print(res.text)
        return None
    try:
        signFlowId = jsonData["data"]["signFlowId"]
        return signFlowId
    except:
        print("status == 1,get_signFlowId_status()方法报错了")
        return None


def customize_sign_data2(handEnabled, ofdFlag):
    Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
    if ofdFlag == 1:
        fileList = getFileKeyForSignFlowGen(1)
    else:
        fileList = getFileKeyForSignFlowGen(0)
    data = signFlowGen.gen_sign_flow_data3(get_business_no(), True, False, 0, 1, 1,
                                           businessTypeCode, getUserList(), getOrgList(),
                                           fileList)
    if handEnabled:
        data["signerInfos"][0]["sealInfos"][0]["signConfigs"][0]["handEnable"] = True
    headers = gen_headers_signature(data)
    res = requests.post(url=Url, headers=headers, json=data)
    jsonData = json.loads(res.text)
    if jsonData["code"] != 200 or jsonData["data"] is None:
        print(res.text)
        return None
    try:
        signFlowId = jsonData["data"]["signFlowId"]
        return signFlowId
    except:
        print("status == 1,get_signFlowId_status()方法报错了")
        return None


def gen_standardSign_data(organize, applyId, signFlowId, condition, singerIndex, useType):
    try:
        detail = get_detail1(signFlowId, organize, singerIndex, useType)
        sealTypeCode = None
        organizeCode = None
        if organize:
            sealTypeCode = get_sign_sealTypeCode(useType)
            organizationInfo = get_sign_organizeInfo(useType, singerIndex)
            organizeCode = organizationInfo['organizationCode']
        accountCode = get_sign_UserCode(useType, singerIndex)
        # 传入参数
        headers = get_sign_headers(useType, singerIndex)
        sealId = get_sign_seal_Id(headers, organizeCode, signFlowId)
        # sealId = get_sign_sealId(useType, singerIndex)
        # if useType == 2:
        #     headers = get_sign_headers(useType, singerIndex)
        configParam = {
            'accountCode': accountCode,
            'organizeCode': organizeCode,
            'sealId': sealId,
            'sealTypeCode': sealTypeCode,
            'condition': condition
        }
        print('configParam' + str(configParam))
        return signFlowGen.gen_standardSign_data(organize, applyId, detail, configParam)
    except:
        return None


# 发起签署时的添加签署文件
# fileCount 签署文件数量 默认 1
# fileFormat 签署文件格式 默认 pdf
def common_upload(fileName=None):
    if fileName is None:
        return pdf_filekey
    try:
        basedir = os.path.dirname(__file__)
        file_path1 = os.path.join(basedir, 'data')
        file_path2 = os.path.join(file_path1, fileName)
        if fileName.endswith('.zip'):
            file_path2 = os.path.join(file_path1, 'epaasTemplate', fileName)
        myfile = open(file_path2, 'rb')
        fileUploadUrl = MAIN_HOST + "/esign-docs/fileSystem/attachmentUpload"
        fileHeaders = get_headers_with_fileUpload(PROJECT_ID)
        multipart_encoder = MultipartEncoder(
            fields={
                'uploadFile': (fileName, myfile, "application/pdf"),
            },
            boundary='----WebKitFormBoundaryMj8eVLxVsBeDDjs6'
        )
        res = requests.post(url=fileUploadUrl, headers=fileHeaders, data=multipart_encoder)
        # time.sleep(2)
        if json.loads(res.text)["status"] != 200:
            print(res.text)
        fileKey = json.loads(res.text)["data"]["fileKey"]
        cryptoFileKey1 = json.loads(res.text)["data"]["cryptoFileKey"]
        m = re.search(r'sign=(\w+)', cryptoFileKey1)
        cryptoFileKey = m.group(0)[5:]
        fileKeyInfo = dict()
        fileKeyInfo["fileKey"] = fileKey
        fileKeyInfo["cryptoFileKey"] = cryptoFileKey
        print('xxxx===',fileKeyInfo)
        return fileKeyInfo
    except:
        print("common_upload method error")
        return None

def common_upload_fileKey(fileName=None):
    fileKeyInfo=common_upload(fileName)
    return fileKeyInfo["fileKey"]

def get_save_doc(fileCount, fileFormat):
    try:
        if fileFormat == 'pdf':
            signFlowId = get_sign_scene(1, 0)
            docReturn = {
                "fileKeys": pdf_filekey,
                "cryptoFileKeys": pdf_filekey_crypto,
                "fileName": 'test.pdf',
                "signFlowId": signFlowId,
                "docFormat": "pdf"
            }
        else:
            signFlowId = get_sign_scene(1, 1)
            docReturn = {
                "fileKeys": ofd_filekey,
                "cryptoFileKeys": ofd_filekey_crypto,
                "fileName": 'test.ofd',
                "signFlowId": signFlowId,
                "docFormat": "ofd"
            }
        return docReturn
    except Exception as e:
        print("get_save_doc method error")
        return None


# signModel 0 顺序签 1 无序签 2 或签
# isStart 1 发起签署 0 保存草稿  1,False,1,0,1
# signerType 签署方类型 0 内部个人 1 内部机构 2相对方个人 3 相对方机构
# userIndex取签署人列表的下标
# 举例子,singerParamList: {'nodeList':  [
#               {'signModel': 0, 'list': [{'signerType': 2,'userIndex': 0}]},
#               {'signModel': 0, 'list': [{'signerType': 3,'userIndex': 1}]}
#               ] }
def get_saveProcess_data(isStart, isSort, singerParamList, docReturn):
    try:
        # 签署方
        signInfo = get_save_web_signInfo(singerParamList, isStart)
        # 组装发起签署的前置流程
        data = signFlowGen.gen_save_data(isStart, isSort, signInfo, docReturn, 0, None)
        return data
    except Exception as e:
        print(e.args)
        print("get_saveProcess_data 报错")
        return None


def get_saveProcess_data1(isStart, isSort, singerParamList, docReturn):
    try:
        # 签署方
        signInfo = get_save_web_signInfo(singerParamList, isStart)
        # 组装发起签署的前置流程
        data = signFlowGen.gen_save_data(isStart, isSort, signInfo, docReturn, 1, None)
        return data
    except Exception as e:
        print(e.args)
        print("get_saveProcess_data1 报错")
        return None


## 用于签署，使用写死的签署方信息
# signModel 0 顺序签 1 无序签 2 或签
# signerType 签署方类型 0 内部个人 1 内部机构 2相对方个人 3 相对方机构
## 用于签署，使用写死的签署方信息,目前只支持内部
# 举例子,singerParamList1: [
#               {'signModel': 0, 'list': [{'organize': False,'useType':1,'signer':1}]},
#               {'signModel': 0, 'list': [{'organize': True,'useType':1,'signer':1}]}
#               ]
def get_saveProcess_data_condition(isStart, isSort, singerParamList1, docReturn, condition, subject):
    # 签署方
    signInfo = get_save_web_signInfo1(singerParamList1)
    # 组装发起签署的前置流程
    data = signFlowGen.gen_save_data(isStart, isSort, signInfo, docReturn, condition, subject)
    return data


def get_save_web_signInfo(singerParamList, isStart):
    try:
        return signFlowGen.gen_save_web_signInfo(singerParamList, isStart)
    except:
        return None


## 用于签署，使用写死的签署方信息,目前只支持内部
# 举例子,singerParamList1: [
#               {'signModel': 0, 'list': [{'organize': False,'useType':1,'signer':1}]},
#               {'signModel': 0, 'list': [{'organize': True,'useType':1,'signer':1}]}
#               ]
def get_save_web_signInfo1(singerParamList1):
    try:
        signInfos = []
        for i in range(len(singerParamList1)):
            node = singerParamList1[i]
            signModel = node['signModel']
            singerList = node['list']

            # 签署方类型
            list = []
            for i in range(len(singerList)):
                singerParam = singerList[i]
                organize = singerParam['organize']
                useType = singerParam['useType']
                singerIndex = singerParam['signer']

                userCode = get_sign_UserCode(useType, singerIndex)
                organizationInfo = get_sign_organizeInfo(useType, singerIndex)
                departmentCode = organizationInfo['organizationCode']
                departmentName = organizationInfo['organizationName']

                legalSealAuthFlag = None
                organizeCode = None
                signerType = 1
                uuid = userCode + "-" + departmentCode
                if organize:
                    organizeCode = departmentCode
                    legalSealAuthFlag = 1
                    signerType = 2
                    uuid = departmentCode + "-" + departmentCode

                signer = {
                    'signerType': signerType,
                    'isUkeySign': 0,
                    'userCode': userCode,
                    'userType': useType,
                    'organizeCode': organizeCode,
                    'legalSignFlag': 0,
                    'sealTypeId': "",
                    'departmentCode': departmentCode,
                    'departmentName': departmentName,
                    # 'legalSealAuthFlag': legalSealAuthFlag,
                    'uuid': uuid,
                    'id': "add-" + str(i),
                    'draggable': True
                }
                list.append(signer)

            if signModel == 0:
                signModel = 1
            singerBean = {
                'signModel': signModel,
                'list': list,
                'id': "node-" + str(i)
            }
            signInfos.append(singerBean)
        return signInfos
    except:
        return None


# 抄送人列表
# noticeParamList [{'useType':1,'index':0}]
def get_save_web_noticeInfo(noticeParamList):
    try:
        return signFlowGen.gen_save_web_noticeInfo(noticeParamList)
    except:
        return None


# 取内部个人签署方
# sealType 主体类型 1个人 2机构,示例值(1)
# useType  1 内部 2 相对方
def getUserInfo(useType, sealType, index):
    try:
        url = MAIN_HOST + "/esign-signs/user/getUserInfo"
        headers = get_headers_with_autho(PROJECT_ID)
        signerName = "测试"
        if useType == 2 and index == 0:
            signerName = "测试签署"
        data = {
            "params":
                {"sealType": sealType,
                 "signerName": signerName,
                 "userType": useType
                 }
        }
        businessRes = requests.post(url, json=data, headers=headers)
        dataList = businessRes.json()['data']
        userInfo = dataList[index]
        user = {
            'departmentCode': userInfo['departmentCode'],
            'departmentName': userInfo['departmentName'],
            'userCode': userInfo['id'],
            'name': userInfo['name'],
            'telOrEmail': userInfo['telOrEmail']
        }
        return user
    except:
        return None


def get_data_param(data, param):
    try:
        return data.get("params").get(param)
    except:
        return None


def get_data_index_param(data, listParam, index, param):
    try:
        return data.get("params").get(listParam)[index].get(param)
    except:
        return None


def get_data_0_listparam(data, listParam, param):
    try:
        return data[0].get(listParam)[0].get(param)
    except:
        return None


def get_data_0_param(data, param):
    try:
        return data[0].get(param)
    except:
        return None


def get_docReturn_cryptoFileKey(docReturn):
    print(docReturn["cryptoFileKeys"][0])
    return docReturn["cryptoFileKeys"][0]


def get_docReturn_fileKey(docReturn):
    print(docReturn["fileKeys"][0])
    return docReturn["fileKeys"][0]


def get_conditon(fileFormat):
    fileKeyInfo = {}
    if fileFormat == 'pdf':
        fileKeyInfo['fileKey'] = pdf_filekey
        fileKeyInfo['cryptoFileKey'] = pdf_filekey_crypto
    if fileFormat == 'ofd':
        fileKeyInfo['fileKey'] = ofd_filekey
        fileKeyInfo['cryptoFileKey'] = ofd_filekey_crypto
    if fileFormat != 'pdf' and fileFormat != 'ofd':
        fileName = signFlowGen.gen_fieName(fileFormat)
        fileKeyInfo = common_upload(fileName)
    return "fileKey=" + fileKeyInfo['fileKey'] + "&cryptoFileKey=" + fileKeyInfo['cryptoFileKey']


def get_conditon_cryptoFileKey(fileFormat):
    fileKeyInfo = {}
    if fileFormat == 'pdf':
        fileKeyInfo['cryptoFileKey'] = pdf_filekey_crypto
    if fileFormat == 'ofd':
        fileKeyInfo['cryptoFileKey'] = ofd_filekey_crypto
    if fileFormat != 'pdf' and fileFormat != 'ofd':
        fileName = signFlowGen.gen_fieName(fileFormat)
        fileKeyInfo = common_upload(fileName)
    try:
        return "cryptoFileKey=" + fileKeyInfo['cryptoFileKey']
    except:
        print("get_conditon_cryptoFileKey()报错了")
        return None


def get_largeFile_process():
    Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
    signFiles = []
    signerInfos = []
    sealInfo = []

    fileKey = fileKey_big_332MB

    signFile1 = signFlowGen.gen_signFile_data(fileKey)
    signFiles.append(signFile1)

    s1 = signFlowGen.gen_sealInfo_data(fileKey, "COMMON-SIGN", sign01_sealId, "", "")
    sealInfo.append(s1)
    # 添加静默签署的签署内部个人信息
    signerInnerPersonAuto = signFlowGen.gen_signerInfo_autoSign_data(sealInfo, sign01_userCode, "", 0,
                                                                     1)
    signerInfos.append(signerInnerPersonAuto)
    data = signFlowGen.gen_signFlow_data_noCC2(get_business_no(), signFiles, signerInfos,
                                               businessTypeCode)

    headers = gen_headers_signature(data)
    res = requests.post(url=Url, headers=headers, json=data)
    # 实地测试，有些静默签流程需要处理的时候 >= 3，故此处改为5
    jsonData = json.loads(res.text)["data"]
    if jsonData is None:
        print(res.text)
    try:
        signFlowId = jsonData["signFlowId"]
        return signFlowId
    except:
        print("get_largeFile_process()报错了")
        return None


# 保存签署区
def save_seal1(signFlowId, sealInfo):
    headers = get_headers_with_autho(PROJECT_ID)
    data = {
        "params": {
            "isStart": 0,
            "processId": signFlowId,
            "sealInfo": sealInfo
        }
    }
    saveUrl = MAIN_HOST + "/esign-signs/process/saveSeal"
    res = requests.post(url=saveUrl, headers=headers, json=data)
    try:
        if json.loads(res.text)["status"] != 200:
            print(res.text)
    except:
        return None


# 保存签署区
def save_seal(signFlowId, sealInfo):
    headers = get_headers_with_autho(PROJECT_ID)
    data = {
        "params": {
            "isStart": 0,
            "processId": signFlowId,
            "sealInfo": sealInfo
        }
    }
    saveUrl = MAIN_HOST + "/esign-signs/process/saveSeal"
    requests.post(url=saveUrl, headers=headers, json=data)
    # print(res.text)
    try:
        detail = get_save_detail(signFlowId)
        sealInfo = detail['signConfigInfo']
        return sealInfo
    except:
        return None


# 签署区-关键字
def get_signConfigInfo_with_keyWord(docReturn):
    try:
        # print(docReturn)
        signFlowId = docReturn["signFlowId"]
        fileKey = docReturn["fileKeys"][0]
        cryptoFileKey = docReturn["cryptoFileKeys"][0]
        keyList = ["甲方"]
        # 关键字解析,可以不做
        headers = get_headers_with_autho(PROJECT_ID)
        keyPositions1 = keyWordParsing(fileKey, cryptoFileKey, 1, keyList, '1-3', headers)
        keyPositions2 = keyWordParsing(fileKey, cryptoFileKey, 1, keyList, '3-5', headers)

        actorId = get_proccess_actor_uuid(signFlowId, 0, 0)
        sealInfo = gen_sealInfo_with_keyWord([], keyPositions1, fileKey, actorId, '1-3', 1, 1, 1, 1, 1, None, keyList)
        sealInfo2 = gen_sealInfo_with_keyWord(sealInfo, keyPositions2, fileKey, actorId, '3-5', 1, 0, 1, 1, 1, None,
                                              keyList)
        return save_seal(signFlowId, sealInfo2)
    except:
        return None


# 签署区-多签名域指定不同印章
def get_signConfigInfo2(docReturn):
    try:
        # print(docReturn)
        signFlowId = docReturn["signFlowId"]
        fileKey = docReturn["fileKeys"][0]
        actorId = get_proccess_actor_uuid(signFlowId, 0, 0)
        sealInfo1 = gen_sealInfo(fileKey, actorId, '1-10', 80, 100, None, 'group1', None, 2, 2, 1, None, None, None,
                                 None)
        sealInfo2 = gen_sealInfo(fileKey, actorId, '3-5', 200, 100, None, 'group2', None, 1, 2, 1, None, None, None,
                                 None)
        sealInfo3 = gen_sealInfo(fileKey, actorId, 7, 80, 200, None, 'group3', None, 1, 2, 1, None, None, None, None)
        # 偶数页骑缝签
        sealInfo4 = gen_sealInfo(fileKey, actorId, None, 0.0, 500, None, 'group4', None, 2, 2, 2, None, None, None, 2)
        sealInfos = [sealInfo1, sealInfo2, sealInfo3, sealInfo4]
        return save_seal(signFlowId, sealInfos)
    except:
        return None


# 签署区-指定奇数页骑缝+偶数页骑缝
def get_signConfigInfo3(docReturn):
    try:
        # print(docReturn)
        signFlowId = docReturn["signFlowId"]
        fileKey = docReturn["fileKeys"][0]
        actorId = get_proccess_actor_uuid(signFlowId, 0, 0)
        detail = get_save_detail(signFlowId)
        pageNo = get_pageNo(detail)
        # 全部骑缝章
        sealInfo1 = gen_sealInfo(fileKey, actorId, '1-' + str(pageNo), 0.0, 100, None, 'group1', None, 2, 2, 2, None,
                                 None,
                                 None, None)
        # 奇数骑缝章
        sealInfo2 = gen_sealInfo(fileKey, actorId, None, 0.0, 300, None, 'group2', None, 2, 2, 2, None, None, None, 1)
        # 偶数骑缝章
        sealInfo3 = gen_sealInfo(fileKey, actorId, None, 0.0, 500, None, 'group3', None, 2, 2, 2, None, None, None, 2)

        # 个人章
        sealInfo4 = gen_sealInfo(fileKey, actorId, 1, 200, 100, None, 'group4', None, 1, 2, 1, None, None, None, None)
        sealInfos = [sealInfo1, sealInfo2, sealInfo3, sealInfo4]
        return save_seal(signFlowId, sealInfos)
    except:
        return None


# 签署区-多文档单签署人
def get_signConfigInfo4(docReturn):
    try:
        # print(docReturn)
        signFlowId = docReturn["signFlowId"]
        fileKey = docReturn["fileKeys"][1]
        # 关键字解析,可以不做
        detail = get_save_detail(signFlowId)
        pageNo = get_pageNo(detail)
        actorId = get_proccess_actor_uuid(signFlowId, 0, 0)
        # 全部骑缝章
        sealInfo1 = gen_sealInfo(fileKey, actorId, "1-" + str(pageNo), 0.0, 100, None, 'group1', None, 2, 2, 2, None,
                                 None,
                                 None, None)
        sealInfos = [sealInfo1]
        return save_seal(signFlowId, sealInfos)
    except:
        return None


# 签署区-多文档多签署人，不指定
def get_signConfigInfo6(signFlowId):
    try:
        return save_seal(signFlowId, [])
    except:
        return None


# 签署区-多文档多签署人，指定自由签后其他签署人不指定
def get_signConfigInfo7(docReturn):
    try:
        # print(docReturn)
        signFlowId = docReturn["signFlowId"]
        fileKey2 = docReturn["fileKeys"][1]
        actorId = get_proccess_actor_uuid(signFlowId, 0, 0)
        sealInfo1 = gen_sealInfo(fileKey2, actorId, 1, 80, 100, None, 'group1', None, 1, 1, 1, None, None, None, None)
        sealInfos = [sealInfo1]
        return save_seal(signFlowId, sealInfos)
    except:
        return None


# 签署区-多文档多签署人，指定位置后不可添加自由签签名域
def get_signConfigInfo5(docReturn):
    try:
        # print(docReturn)
        signFlowId = docReturn["signFlowId"]
        fileKey2 = docReturn["fileKeys"][1]
        actorId = get_proccess_actor_uuid(signFlowId, 0, 0)
        actorId2 = get_proccess_actor_uuid(signFlowId, 1, 0)
        sealInfo1 = gen_sealInfo(fileKey2, actorId, 1, 80, 100, None, 'group1', None, 1, 1, 1, None, None, None, None)
        sealInfo2 = gen_sealInfo(fileKey2, actorId2, 1, 80, 100, None, 'group1', None, 1, 1, 1, None, None, None, None)
        sealInfos = [sealInfo1, sealInfo2]
        return save_seal(signFlowId, sealInfos)
    except:
        return None


# 签署区-多文档多签署人，文档指定位置后其他签署人不指定
def get_signConfigInfo8(docReturn):
    try:
        print(docReturn)
        signFlowId = docReturn["signFlowId"]
        fileKey2 = docReturn["fileKeys"][1]
        fileKey3 = docReturn["fileKeys"][2]
        actorId = get_proccess_actor_uuid(signFlowId, 0, 0)
        actorId2 = get_proccess_actor_uuid(signFlowId, 1, 0)
        sealInfo1 = gen_sealInfo(fileKey2, actorId, 1, 80, 100, None, 'group1', None, 1, 1, 1, None, None, None, None)
        sealInfo2 = gen_sealInfo(fileKey3, actorId2, 1, 80, 100, None, 'group1', None, 1, 1, 1, None, None, None, None)
        sealInfos = [sealInfo1, sealInfo2]
        return save_seal(signFlowId, sealInfos)
    except:
        return None


def get_detail_signInfo(processId):
    try:
        print(processId)
        signInfo = get_save_detail(processId)['signInfo']
        return signInfo
    except:
        return None


# 构建需要用印审批的流程
def get_approve_sign():
    try:
        data = signFlowGen.getOrSignOrNoSequeueRequst(businessTypeCode, pdf_filekey,
                                                      org01_orgCode,
                                                      sign01_userCode, wsignwb02_approve_userCode,
                                                      wsignwb01_main_orgCode, 2, 1)
        Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
        headers = gen_headers_signature(data)
        res = requests.post(url=Url, headers=headers, json=data)
        jsonData = json.loads(res.text)
        if jsonData["code"] != 200 or jsonData["data"] is None:
            print(res.text)
            return None
        signFlowId = jsonData.get("data").get("signFlowId")
        return signFlowId
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 获取不用场景的签署流程
# type： 0-获取无序签署的流程
# 1-获取内部或签
# 2-获取相对方或签
# 3-获取带有用印审批的无序签署
# 4-获取带有用印审批的或签
# 5-内部或签并签署完成
# 6-相对方企业签署并签署完成
# 7-相对方个人签署并签署完成
# 8-或签的拒签
# 9-用印审批的或签审批中（场景4的提交了审批用印）
# 10-内部仅UKey签署

def get_sign_scene(type, ofdFlag):
    Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
    try:
        if type == 1 or type == 8 or type == 10:
            data = signFlowGen.getOrSignOrNoSequeueRequst(businessTypeCode, pdf_filekey,
                                                          ci_orgCode,
                                                          csqs_userCode,
                                                          sign01_userCode,
                                                          org01_orgCode, 1, 2)
        if type == 10:
            data['signerInfos'][0]['UKeyOnly'] = 1
        if type == 2:
            data = signFlowGen.getOrSignOrNoSequeueRequst(businessTypeCode, pdf_filekey,
                                                          ci_orgCode,
                                                          csqs_userCode,
                                                          wsignwb01_userCode,
                                                          wsignwb01_main_orgCode, 2, 2)
        if type == 3:
            data = signFlowGen.getOrSignOrNoSequeueRequst(businessTypeCode, pdf_filekey,
                                                          org01_orgCode,
                                                          sign01_userCode,
                                                          wsignwb02_approve_userCode,
                                                          wsignwb01_main_orgCode, 2, 1)
        if type == 4 or type == 9:
            data = signFlowGen.getOrSignOrNoSequeueRequst(businessTypeCode, pdf_filekey,
                                                          org01_orgCode,
                                                          sign01_userCode,
                                                          wsignwb02_approve_userCode,
                                                          wsignwb01_main_orgCode, 2, 2)
        if type == 9:
            signer01 = {"userType": 2, "customAccountNo": wsignwb01_accountNo, "signMode": 2, "signNode": 1,
                        "sealInfos": [{"fileKey": pdf_filekey, "signConfigs": [
                            {"posY": "250", "posX": "250", "pageNo": "1", "signType": "COMMON-SIGN",
                             "signatureType": "PERSON-SEAL"}]}]}
            data['signerInfos'].append(signer01)
        if type == 5:
            data = signFlowGen.getOrSignOrNoSequeueRequst(businessTypeCode, pdf_filekey,
                                                          ci_orgCode,
                                                          csqs_userCode,
                                                          sign01_userCode,
                                                          org01_orgCode, 1, 3)
        if type == 6:
            data = signFlowGen.getOrSignOrNoSequeueRequst(businessTypeCode, pdf_filekey,
                                                          ci_orgCode,
                                                          csqs_userCode,
                                                          wsignwb01_userCode,
                                                          wsignwb01_main_orgCode, 2, 4)
        if type == 7:
            data = signFlowGen.getOrSignOrNoSequeueRequst(businessTypeCode, pdf_filekey,
                                                          ci_orgCode,
                                                          csqs_userCode,
                                                          wsignwb01_userCode,
                                                          '', 2, 4)
        if ofdFlag == 1:
            ofdFileKey = ofd_filekey
            pdfFileKey = pdf_filekey
            data = str(data).replace(pdfFileKey, ofdFileKey)
            data = eval(data)
        headers = gen_headers_signature(data)
        print('发起数据：', data)
        res = requests.post(url=Url, headers=headers, json=data)
        print('发起流程：', res.text)
        jsonData = json.loads(res.text)
        if jsonData["code"] != 200 or jsonData["data"] is None:
            print(res.text)
            return None
        signFlowId = jsonData["data"]["signFlowId"]

        if type == 5:  # 内部签署
            projectId = PROJECT_ID
            headers0 = get_headers_with_autho(projectId)
            sign_wrap(signFlowId, False, headers0, 1)
            time.sleep(3)  # 签署等待3秒
        if type == 6:  # 相对方企业签署
            headers2 = get_headers_with_outer_person(wsignwb01_userCode)
            sealId2 = get_sign_seal_Id(headers2, wsignwb01_main_orgCode, signFlowId)
            signtureParams = {
                "sealId": sealId2,
                "sealTypeCode": "PUBLIC",
                "organizeCode": wsignwb01_main_orgCode,
                "accountSaasId": os.environ['wsignwb01.saasId'],
                "userMobile": get_outeruser_customAccountNo(wsignwb01_userCode, "mobile"),
                "userCode": wsignwb01_userCode
            }
            sign_wrap_outer_account(signFlowId, signtureParams, headers2)
            time.sleep(3)  # 签署等待3秒
        if type == 7:  # 相对方个人签署
            outUserCode = wsignwb01_userCode
            headers3 = get_headers_with_outer_person(outUserCode)
            sealId = get_sign_seal_Id(headers3, None, signFlowId)
            signtureParams = {
                "sealId": sealId,
                "sealTypeCode": None,
                "organizeCode": None,
                "accountSaasId": wsignwb01_saasId,
                "userMobile": get_outeruser_customAccountNo(outUserCode, "mobile"),
                "userCode": outUserCode
            }
            sign_wrap_outer_account(signFlowId, signtureParams, headers3)
        if type == 8:  # 内部用户拒签
            try:
                data = get_refusing_data3(signFlowId)
                rejectUrl = MAIN_HOST + "/esign-signs/process/refuseFlow"
                headers = get_headers_with_autho(PROJECT_ID)
                res = requests.post(rejectUrl, json=data, headers=headers)
                sleep(3)
            except:
                print(sys._getframe().f_code.co_name + type + "拒签方法报错了")
                return None

        if type == 3:  # 相对方企业签署
            outUserCode = wsignwb02_approve_userCode
            signtureParams = {
                "sealId": None,
                "sealTypeCode": "PUBLIC",
                "organizeCode": wsignwb01_main_orgCode,
                "accountSaasId": wsignwb02_approve_saasId,
                "userMobile": get_outeruser_customAccountNo(outUserCode, "mobile"),
                "userCode": outUserCode
            }
            headers2 = get_headers_with_outer_person(outUserCode)
            sign_wrap_outer_account(signFlowId, signtureParams, headers2)
            time.sleep(3)  # 签署等待3秒
        if type == 9:  # 相对方企业用印审批提交签署
            headers2 = get_headers_with_outer_person(wsignwb02_approve_userCode)
            sealId2 = get_sign_seal_Id(headers2, wsignwb01_main_orgCode, signFlowId)
            signtureParams = {
                "sealId": sealId2,
                "sealTypeCode": "PUBLIC",
                "organizeCode": wsignwb01_main_orgCode,
                "accountSaasId": os.environ['wsignwb01.saasId'],
                "userMobile": get_outeruser_customAccountNo(wsignwb02_approve_userCode, "mobile"),
                "userCode": wsignwb02_approve_userCode
            }
            sign_wrap_outer_account(signFlowId, signtureParams, headers2)
            time.sleep(3)  # 签署等待3秒
        return signFlowId
    except:
        print(sys._getframe().f_code.co_name + type + " -type-调用异常")
        return None

# 获取指定节点中的指定顺序人actorId
# signNode 签署节点,通常是0
# personNo 签署人再当前节点中的位子，通常是0
def get_proccess_actor_uuid(processId, signNode, personNo):
    if get_signFlowDetail_signFlowStatus(processId) == 0:
        sleep(3)
    if get_signFlowDetail_signFlowStatus(processId) == 1:
        url = MAIN_HOST + "/esign-signs/process/detail"
        data = {
            "params": {
                "processId": processId,
                "organizeCode": "",
                "approvalProcessId": "",
                "requestSource": 2
            }
        }
        headers = get_headers_with_autho(PROJECT_ID)
        res = requests.post(url, json=data, headers=headers)
        print()
        try:
            jsonData = json.loads(res.text)
            print('签署详情：',jsonData)
            if jsonData["status"] == 200:
                print(res.text)
                actorId = jsonData.get("data").get("signInfo")[signNode].get("list")[personNo].get("actorId")
                print('签署方actorId：', actorId)
                return actorId
        except:
            return None




def get_userCode(signInfo, signNode, personNode):
    try:
        return signInfo[signNode]["list"][personNode]["userCode"]
    except:
        return None


# 根据usercode获取accountcode
def get_inneruser_customAccountNo(userCode):
    url = OPENAPI_HOST + "/manage/v1/innerUsers/detail"
    data = {"userCode": userCode}
    headers = gen_headers_signature(data)
    res = requests.post(url, json=data, headers=headers)
    try:
        return res.json()['data'][0]['customAccountNo']
    except:
        return None


def get_inneruser_userCode(customAccountNo, key):
    '''
    # 根据accountNumber获取内部用户的对应值
    :param customAccountNo:
    :param key: key的值有 userCode,name,email,mobile,licenseNo,mainCustomOrgNo,mainOrganizationCode,otherOrganization
    :return:
    '''
    url = OPENAPI_HOST + "/manage/v1/innerUsers/detail"
    data = {"customAccountNo": customAccountNo}
    headers = gen_headers_signature(data)
    res = requests.post(url, json=data, headers=headers)
    # print(res.json())
    try:
        if key == None:
            key = 'userCode'
        return res.json()['data'][0][key]
    except:
        return None


def get_outeruser_customAccountNo(userCode,keyVale=None):
    url = OPENAPI_HOST + "/manage/v1/outerUsers/detail"
    data = {"userCode": userCode}
    headers = gen_headers_signature(data)
    res = requests.post(url, json=data, headers=headers)
    try:
        if keyVale == None:
            return res.json()['data'][0]['customAccountNo']
        else:
            return res.json()['data'][0][keyVale]
    except:
        return None


def get_outeruser_otherInfo(customAccountNo, key):
    url = OPENAPI_HOST + "/manage/v1/outerUsers/detail"
    data = {"customAccountNo": customAccountNo}
    headers = gen_headers_signature(data)
    res = requests.post(url, json=data, headers=headers)
    try:
        if key == None:
            key = 'userCode'
        return res.json()['data'][0][key]
    except:
        return None


# 流程作废
# 签署作废获取原流程signFlowId
def get_revokedSignFlowId_revoke(signFlowId):
    Url = OPENAPI_HOST + "/esign-docs/v1/signFlow/revoke"

    data = {"businessNo": "", "reason": "自动化脚本过期", "signFlowId": signFlowId}
    headers = gen_headers_signature(data)
    res = requests.post(url=Url, headers=headers, json=data)
    try:
        signFlowId = json.loads(res.text)["data"]["revokedSignFlowId"]
        return signFlowId
    except:
        return None


# 流程作废
# 签署完成作废获取新流程的signFlowId
def get_signFlow_revoke(signFlowId):
    Url = OPENAPI_HOST + "/esign-docs/v1/signFlow/revoke"

    data = {"businessNo": "", "reason": "自动化脚本过期", "signFlowId": signFlowId}
    headers = gen_headers_signature(data)
    res = requests.post(url=Url, headers=headers, json=data)
    try:
        signFlowId = json.loads(res.text)["data"]["signFlowId"]
        sleep(1)
        return signFlowId
    except:
        return None


# mock流程缓存数据，模拟签署页面提交缓存
def build_process_cache():
    value = {
        "params": {
            "cacheValue": "[{\"fileKey\":\"1650091597291_qUCD9C8J.pdf\",\"fileName\":\"测试1(2).pdf\","
                          "\"fileStatus\":null,\"fileType\":1,\"fileEncrypt\":null,\"pageNumber\":5,"
                          "\"operationType\":2,\"openFile\":null,\"taskId\":\"14ab59af21f9ae19122546c42212bf21\","
                          "\"id\":29795,\"isSplit\":1,\"pageSize\":10,"
                          "\"docUrl\":\"/esign-signs/signFile/pageDownload?fileKey=1650091597291_qUCD9C8J.pdf&pageNo"
                          "=1\",\"docId\":\"1650091597291_qUCD9C8J.pdf\",\"docPwd\":null,\"type\":2,"
                          "\"docFormat\":\"pdf\",\"docCount\":5,\"pageCount\":5,\"seals\":[{\"readOnly\":true,"
                          "\"signIdentity\":1,\"sealId\":\"2eefe993abb8fb9d7985cf886a4361a1\",\"fieldType\":\"free\","
                          "\"sealIdentityType\":1,\"sealTypeCode\":null,\"draggable\":true,\"rotatable\":true,"
                          "\"checked\":false,\"paging\":false,\"id\":\"YAJJRH41\",\"groupId\":\"XK23J5YC\","
                          "\"edgeOption\":\"pageRange\",\"useDate\":true,\"displayData\":{\"width\":124,"
                          "\"height\":124,\"imgUrl\":\"\",\"editable\":true,\"deleteable\":true,\"highlight\":false},"
                          "\"displayDate\":{\"draggable\":true,\"fontSize\":12,\"key\":\"CC8A2DOH\","
                          "\"width\":63.38750076293945,\"height\":14,\"posY\":549.7199951171875,"
                          "\"posX\":320.1399780273438,\"offsetX\":0,\"offsetY\":132,\"dateFormat\":\"yyyy-MM-dd\"},"
                          "\"displayPos\":{\"posX\":320.1399780273438,\"posY\":681.7199951171875,\"posPage\":1,"
                          "\"allPage\":\"1\",\"rotate\":0}}],\"canFreeSign\":true,\"sealTypes\":\"personSeal\","
                          "\"sealConfig\":{\"allAssignedSeal\":false,\"allHandWriteSeal\":false,"
                          "\"personalSealVisible\":true,\"officialSealVisible\":true,\"legalSealVisible\":false,"
                          "\"requiredOrgType\":\"\",\"assignedSealIdMap\":null}}]",
            "processId": "38b02dfa9d815f69b6435a4ce5175cd6",
            "processActorId": "256f19c94114dcd980045ae0008af4aa"
        }
    }
    return json.dumps(value)


# mock流程缓存数据，模拟签署页面提交缓存
def get_process_cache_key():
    Url = MAIN_HOST + "/esign-signs/cache/addSigningCache"
    data = {
        "params": {
            "cacheValue": build_process_cache(),
            "processId": get_constant("cacheProcessId"),
            "processActorId": get_constant("cacheProcessActorId")
        }
    }
    token = getPortalToken(sign01_customAccountNo, password_encrypt)
    headers = {"Content-Type": "application/json", "x-timevale-project-id": PROJECT_ID,
               "x-timevale-signature": '', "authorization": token}
    res = requests.post(url=Url, headers=headers, json=data)
    try:
        key = json.loads(res.text)["data"]
        return key
    except:
        return None


# 获取意愿认证applyId
def get_applyId(processId, orgFlag):
    headers = get_headers_with_autho(PROJECT_ID)
    try:
        willReturn = willing(processId, headers, orgFlag, 0)
        applyId = willReturn["applyId"]
        return applyId
    except:
        print("get_applyId()方法报错了")
        return None


def willing(processId, headers, organizeSign, organizeIndex):
    url = MAIN_HOST + "/esign-signs/auth/willing"
    redirectUrl = MAIN_HOST + "/sign-manage-web/sign-page?mcok"
    if organizeSign:
        organizeCode = get_organizeCode(organizeSign, organizeIndex)
    else:
        organizeCode = ""
    print('willing------->意愿使用的用户', headers)
    data = {
        "params": {
            "processId": processId,
            "redirectUrl": redirectUrl,
            "clientType": 0,
            "organizeCode": organizeCode
        }
    }
    res = requests.post(url=url, headers=headers, data=json.dumps(data))
    try:
        jsonData = json.loads(res.text)
        if jsonData["data"] is None:
            print(res.text)
        return jsonData["data"]
    except:
        return None


def willing_in(processId, organizeSign, organizeIndex):
    headers = get_sign_headers(1, organizeIndex)
    url = MAIN_HOST + "/esign-signs/auth/willing"
    data = {
        "params": {
            "processId": processId,
            "redirectUrl": get_willingRedirectUrl(),
            "clientType": 0,
            "organizeCode": get_organizeCode(organizeSign, organizeIndex)
        }
    }
    print('内部意愿认证数据：', data)
    res = requests.post(url=url, headers=headers, data=json.dumps(data))
    try:
        if json.loads(res.text)["status"] != 200:
            print("willing_in fail:" + str(res.text))
        else:
            print("willing_in success")
            return json.loads(res.text)["data"]
    except:
        return None


def get_outer_apply(processId, headers, organizeCode):
    willReturn = willing_out(processId, headers, organizeCode)
    return willReturn["applyId"]


def willing_out(processId, headers, organizeCode):
    url = MAIN_HOST + "/esign-signs/auth/willing"
    redirectUrl = MAIN_HOST + "/sign-manage-web/sign-page?mcok"
    data = {
        "params": {
            "processId": processId,
            "redirectUrl": redirectUrl,
            "clientType": 0,
            "organizeCode": organizeCode
        }
    }
    res = requests.post(url=url, headers=headers, data=json.dumps(data))
    try:
        if json.loads(res.text)["status"] != 200:
            print("willing_out fail:" + str(res.text))
        else:
            print("willing_out success")
            return json.loads(res.text)["data"]
    except:
        return None


# 签署
def standard_flow_sign1(signData, useType, singerIndex):
    headers = get_sign_headers(useType, singerIndex)
    url = MAIN_HOST + "/esign-signs/process/standardFlowSign"
    res = requests.post(url=url, headers=headers, data=json.dumps(signData))
    jsonData = json.loads(res.text)
    print(res.text)
    try:
        if jsonData["status"] != 200:
            print("standard_flow_sign fail" + str(res.text))
        else:
            print("standard_flow_sign success")
            return jsonData["data"]["executeStatus"]
    except:
        return None


# 签署
def standard_flow_sign(signData, headers):
    url = MAIN_HOST + "/esign-signs/process/standardFlowSign"
    res = requests.post(url=url, headers=headers, data=json.dumps(signData))
    jsonData = json.loads(res.text)
    print(res.text)
    try:
        if jsonData["status"] != 200:
            print("standard_flow_sign fail" + str(res.text))
        else:
            print("standard_flow_sign success")
            return jsonData["data"]["executeStatus"]
    except:
        return None


def get_draft_detail(processId, organizeSign):
    headers = get_headers_with_autho(PROJECT_ID)
    data = {
        "params": {
            "processId": processId,
            "organizeCode": get_organizeCode(organizeSign, 1),
            "requestSource": 1
        }
    }
    url = MAIN_HOST + "/esign-signs/process/detail"
    res = requests.post(url=url, headers=headers, data=json.dumps(data))
    try:
        if json.loads(res.text)["status"] != 200:
            print("get_draft_detail fail " + str(res.text))
        else:
            print("get_draft_detail success")
            return json.loads(res.text)["data"]
    except:
        return None


def get_save_detail(processId):
    headers = get_headers_with_autho(PROJECT_ID)
    data = {
        "params": {
            "processId": processId,
            "organizeCode": "",
            "requestSource": 2
        }
    }
    url = MAIN_HOST + "/esign-signs/process/detail"
    res = requests.post(url=url, headers=headers, data=json.dumps(data))
    try:
        if json.loads(res.text)["status"] != 200:
            print("get_save_detail fail " + str(res.text))
        else:
            print("get_save_detail success")
            return json.loads(res.text)["data"]
    except:
        return None


def get_detail1(processId, organizeSign, signerIndex, useType):
    headers = get_sign_headers(useType, signerIndex)
    organizeCode = None
    if organizeSign:
        organizationInfo = get_sign_organizeInfo(useType, signerIndex)
        organizeCode = organizationInfo['organizationCode']
    data = {
        "params": {
            "processId": processId,
            "organizeCode": organizeCode,
            "requestSource": 2
        }
    }
    url = MAIN_HOST + "/esign-signs/process/detail"
    sleep(3)
    res = requests.post(url=url, headers=headers, data=json.dumps(data))
    try:
        if json.loads(res.text)["status"] != 200:
            print("get_detail1 fail " + str(res.text))
        else:
            print("get_detail1 success"+str(res.text)  )
            return json.loads(res.text)["data"]
    except:
        return None


def get_detail(processId, organizeSign, headers, requestSource):
    data = {
        "params": {
            "processId": processId,
            "organizeCode": get_organizeCode(organizeSign, 1),
            "requestSource": requestSource
        }
    }
    url = MAIN_HOST + "/esign-signs/process/detail"
    res = requests.post(url=url, headers=headers, data=json.dumps(data))
    try:
        if json.loads(res.text)["status"] != 200:
            print(res.text)
        return json.loads(res.text)["data"]
    except:
        print("get_detail()方法报错了")
        return None


# index 1 账号1 esigntest天印PO测试企业 ， 2 账号2
def get_organizeCode(organizeSign, organizeIndex):
    if organizeSign:
        if organizeIndex == 1:
            return org01_orgCode
        if organizeIndex == 2:
            return ci_orgCode
    else:
        return None


def get_willResult(processId, applyId, headers):
    url = MAIN_HOST + "/esign-signs/auth/willing/result?processId=" + processId + "&applyId=" + applyId
    res = requests.get(url=url, headers=headers)
    return res


def password_auth(applyId, password, token):
    url = MAIN_HOST + '/manage/auth/signPasswordAuth'
    headers = {"Content-Type": "application/json",
               "token": token
               }
    data = {
        "params": {
            "applyId": applyId,
            "signPassword": password
        },
        "authentication": True,
        "domain": "admin_platform"
    }
    res = requests.post(url=url, headers=headers, json=data)
    return res.text


def parse_tokenKey(willingUrl):
    """
    解密willing接口返回的code，用于密码意愿认证签署
    签署页签署的时候的意愿token： 业务token置换管理平台token
    :param willingUrl: 意愿认证申请地址
    :return:
    """
    from utils.esignToken import parseToken
    token = parseToken(willingUrl)
    return token


def get_decrypt(params):
    """
    aes解密
    """
    from utils.esign6Manage import get_aes_decrypt
    return get_aes_decrypt(params)

# 封装的签署公共方法
def sign_wrap(processId, organize, headers, organizeIndex):
    try:
        willReturn = willing(processId, headers, organize, organizeIndex)
        token = parse_tokenKey(willReturn['url'])
        applyId = willReturn["applyId"]
        password_auth(applyId, password_encrypt, token)
        detail = get_detail(processId, organize, headers, 1)
        # 传入参数 目前仅内部用
        if organize:
            sealId = org01_sealId
            sealTypeCode = gen_sealTypeCode(sealId)
        else:
            sealId = sign01_sealId
            sealTypeCode = None
        configParam = {
            'accountCode': sign01_userCode,
            'organizeCode': get_organizeCode(organize, organizeIndex),
            'sealId': sealId,
            'sealTypeCode': sealTypeCode,
            'condition': 0
        }
        data = signFlowGen.gen_standardSign_data(organize, applyId, detail, configParam)
        headers = get_headers_with_autho(PROJECT_ID)
        standard_flow_sign(data, headers)
    except:
        print("sign_wrap()方法报错了 ")

def getCloudWillScene(type, param):
    """
    SAAS 个人和企业实名的各种场景 --- 要求对应的手机号和企业名称在公有云上是已经有oid的账号
    :param type: 自定义的场景值
    :param param: {"mobile": "","orgName": ""}
    :return: []
    """
    # 自定义方法，调用时引入
    from utils.cloudScene import cloudWillScene
    return cloudWillScene(type, param)

def getSignDocInfoRequestsDataByDetail(detailData, sealParams):
    """
    处理签署数据
    :param detailData:
    :param isOrganize:
    :param sealParams: 要用到的签署印章 企业格式{"personSealId":xxx,"orgSealId":xxx,"legalSealId":xxxx}
    :return:
    """
    # 自定义方法，调用时引入
    from utils.esign6Signs import get_signDocInfoRequestsData
    return get_signDocInfoRequestsData(detailData, sealParams)

# 封装的外部签署公共方法
# processId: 流程id
# signtureParams: {
#             "sealId" : 印章id,
#             "sealTypeCode" : 印章类型，比如公章PUBLIC, 个人签为None,
#             "organizeCode" : 机构id， 个人签为None,
#             "accountSaasId": 签署人saasId
#         }
# headers: 签署人的登录header
def sign_wrap_outer_account(processId, signtureParams, headers):
    try:
        # 请求意愿地址并获取applyId
        willReturn = willing_out(processId, headers, signtureParams["organizeCode"])
        applyId = willReturn["applyId"]

        tmp001 = { "mobile": signtureParams["userMobile"],"applyId": applyId}
        getCloudWillScene(0, tmp001)

        # 封装签署请求参数
        detail = get_detail(processId, signtureParams["organizeCode"], headers, 1)

        tmpSealId = {"orgSealId": signtureParams["sealId"],"orgSealCode":"PUBLIC"}
        data = {
            'accountCode': signtureParams["userCode"],
            'organizeCode': signtureParams["organizeCode"],
            "applyId": applyId,
            'signDocInfoRequests' : getSignDocInfoRequestsDataByDetail(detail, tmpSealId),
            'processUUId':processId
        }
        # 签署
        standard_flow_sign(data, headers)
    except:
        print("sign_wrap_outer_account()方法报错了")


# 上传一个文件到指定的流程
def upload_file(fileName=None, signFlowId=None):
    if fileName == None:
        fileName = 'key30page.pdf'
    file_path = PathUtil.rootPath + "data/" + fileName
    fileHeaders = get_headers_with_fileUpload(PROJECT_ID)
    fileUploadUrl = MAIN_HOST + "/esign-signs/signFile/commonUpload"
    print("fileUploadUrl:" + fileUploadUrl)
    # './data/key30page.pdf'
    myfile = open(file_path, 'rb')
    multipart_encoder = MultipartEncoder(
        fields={
            'file': (fileName, myfile, "application/pdf"),
        },
        boundary='----WebKitFormBoundaryMj8eVLxVsBeDDjs6'
    )
    res = requests.post(url=fileUploadUrl, headers=fileHeaders, data=multipart_encoder)
    try:
        fileKey = json.loads(res.text)["data"]["fileKey"]
        cryptoFileKey = json.loads(res.text)["data"]["cryptoFileKey"]
        if signFlowId:
            # add_file(headers, fileKey, cryptoFileKey, fileName, signFlowId)
            return fileKey
        else:
            fileKey,cryptoFileKey
    except:
        return None


def save_draft(detail, headers):
    headers2 = get_headers_with_autho(PROJECT_ID)
    data = wrap(detail)


def set_SignInfo(detail, signInfo):
    try:
        detail['signInfo'] = signInfo
    except:
        return None


def saveAndStart(detail, headers):
    try:
        detail["isStart"] = 1
        save_draft(detail, headers)
    except:
        return None


def get_inner_person_signer(userCode):
    try:
        signner = signFlowGen.get_inner_person_signer(userCode)
        return signner
    except:
        return None


def get_inner_org_signer(sealTypeId, userCode):
    try:
        signner = signFlowGen.get_inner_org_signer(sealTypeId, userCode)
        return signner
    except:
        return None


# 获取一个相对方个人，会用来做意愿校验的，所以固定是这个人。勿改
def get_opposite_person_signer():
    try:
        projectId = PROJECT_ID
        projectSecrect = PROJECT_SECRET
        outerUserCode = wsignwb02_approve_userCode
        esignManageOpenApiUrl = OPENAPI_HOST
        userInfo = detailOuterUsers(esignManageOpenApiUrl, projectId, projectSecrect, outerUserCode)
        mainOrgCode = userInfo['mainOrganizationCode']
        organizationInfo = detailOuterOrganizations(OPENAPI_HOST, PROJECT_ID,
                                                    PROJECT_SECRET, mainOrgCode)
        orgName = organizationInfo['name']
        signner = signFlowGen.get_opposite_person_signer(outerUserCode, mainOrgCode, orgName)
        return signner
    except:
        return None


# 获取一个相对方机构，会用来做意愿校验的，所以固定是这个人。勿改
# 会触发用印审批的流程
def get_opposite_org_signer():
    try:
        organizationName = getOuterOrganizationName(wsignwb01_main_orgCode)
        signner = signFlowGen.get_opposite_org_signer(wsignwb02_approve_userCode,
                                                      wsignwb01_main_orgCode,
                                                      wsignwb01_main_orgCode,
                                                      organizationName)
        return signner
    except:
        print("get_opposite_org_signer()报错了")


# 获取一个相对方机构，会用来做意愿校验的，所以固定是这个人。勿改
# 会触发签署完成的流程
def get_opposite_org_signer2():
    organizationName = getOuterOrganizationName(wsignwb01_main_orgCode)
    signner = signFlowGen.get_opposite_org_signer(wsignwb01_userCode,
                                                  wsignwb01_main_orgCode,
                                                  wsignwb01_main_orgCode,
                                                  organizationName)
    return signner


def keyWordParsing(fileKey, cryptoFileKey, fileType, keyList, pageRange, headers):
    try:
        data = {
            "cryptoFileKey": cryptoFileKey,
            "fileKey": fileKey,
            "fileType": fileType,
            "keyList": keyList,
            "pageNo": pageRange
        }
        data = wrap(data)
        url = MAIN_HOST + "/esign-signs/process/keywordParsing"
        res = requests.post(url=url, headers=headers, json=data)

        jsonData = json.loads(res.text)["data"]
        if jsonData is None:
            print(res.text)
        return jsonData["keyPositions"]
    except:
        return None


def wrap(originalData):
    data = {
        "params": originalData
    }
    return data


def gen_sealInfo_with_keyWord(sealInfos, keyPositions, fileKey, actorId, pageRange, sealIdentityType, isAddSealSignTime,
                              sealSignTimeFormat, signIdentity, signType, edgeScope, keyList):
    try:
        keyGroup = "group" + get_randomNo_str()
        for i in range(len(keyPositions)):
            keyPosition = keyPositions[i]

            key = keyPosition["key"]
            for position in keyPosition["positions"]:
                sealInfo = gen_sealInfo(fileKey, actorId, position["posPage"], position["posX"], position["posY"],
                                        key, keyGroup, pageRange, sealIdentityType, signIdentity, signType,
                                        sealSignTimeFormat, keyList, isAddSealSignTime, edgeScope)
                sealInfos.append(sealInfo)
        return sealInfos
    except:
        return None


def gen_sealInfos(fileKey, actorId, pageNo, posX, posY, curKey, keyGroup, pageRange, sealIdentityType, signIdentity,
                  signType, sealSignTimeFormat, keyWord, isAddSealSignTime, edgeScope):
    sealInfos = []
    sealInfo = gen_sealInfo(fileKey, actorId, pageNo, posX, posY, curKey, keyGroup, pageRange, sealIdentityType,
                            signIdentity, signType, sealSignTimeFormat, keyWord, isAddSealSignTime, edgeScope)
    sealInfos.append(sealInfo)
    return sealInfos


# sealIdentityType 1手绘 2个人 3机构 4法人
# edgeScope 骑缝签范围 0全部 1奇数 2偶数，默认0
# signIdentity 签署身份1个人 2机构
# signType 签署类型,1单页签,2多页签,3骑缝签,4关键字签
def gen_sealInfo(fileKey, actorId, pageNo, posX, posY, curKey, keyGroup, pageRange, sealIdentityType, signIdentity,
                 signType, sealSignTimeFormat, keyWord, isAddSealSignTime, edgeScope):
    sealInfo = {
        "actorId": actorId,
        "fileKey": fileKey,
        "pageNo": pageNo,
        "posX": posX,
        "posY": posY,
        "curKey": curKey,
        "keyGroup": keyGroup,
        "keyWord": keyWord,
        "pageRange": pageRange,
        "sealIdentityType": sealIdentityType,
        "sealSignTimeFormat": sealSignTimeFormat,
        "signIdentity": signIdentity,
        "signType": signType,
        "isAddSealSignTime": isAddSealSignTime,
        "timePosX": get_time_PosX(isAddSealSignTime, posX),
        "timePosY": get_time_PosY(isAddSealSignTime, posY),
        "edgeScope": edgeScope
    }
    return sealInfo


def get_time_PosX(isAddSealsignTime, posx):
    if (isAddSealsignTime == 1):
        return posx
    else:
        return None


def get_time_PosY(isAddSealsignTime, posY):
    if (isAddSealsignTime == 1):
        return posY - 132
    else:
        return None


# 把签署人添加到指定签署节点下
# signNode: 签署节点，None则新建一个节点
def addSignInfo(signNode, detail, signner):
    try:
        if (signNode == None):
            signInfo = {
                'signModel': 1,
                'list': []
            }
            signInfo['list'].append(signner)
            detail['signInfo'].append(signInfo)
            return signInfo
        else:
            detail['signInfo'][signNode]['list'].append(signner)
            return detail['signInfo'][signNode]
    except:
        print("addSignInfo()方法失败")
        return None


# 修改这个签署节点的模式，1会签，2或签
def update_signModel_for_signInfo(signInfo, signModel):
    try:
        signInfo['signModel'] = signModel
    except:
        return None


def get_signFlow_with_unrealName_user():
    Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"

    signFiles = []
    signerInfos = []

    fileKey = pdf_filekey

    signFile1 = signFlowGen.gen_signFile_data(fileKey)
    signFiles.append(signFile1)
    # userCode = csqs_userCode
    userCode = os.environ['sign07.userCode']
    # orgCode = getInnerOrganizationCode(userCode)
    orgCode = org01_orgCode
    signerInnerPerson = signFlowGen.gen_signerInfo_data2(fileKey, 1, userCode, "", 1, 0)
    signerInfos.append(signerInnerPerson)
    signerInnerOrganize = signFlowGen.gen_signerInfo_data2(fileKey, 1, userCode, orgCode, 1, 0)
    signerInfos.append(signerInnerOrganize)

    data = signFlowGen.gen_signFlow_data(signFiles, signerInfos, businessTypeCode, getUserList(),
                                         getOrgList())
    headers = gen_headers_signature(data)

    res = requests.post(url=Url, headers=headers, json=data)
    print('发起未实名的流程-结果：', res.text)
    jsonData = json.loads(res.text)["data"]
    if jsonData:
        try:
            signFlowId = jsonData["signFlowId"]
            return signFlowId
        except:
            print("get_signFlow_with_unrealName_user()方法报错了")
            return None
    return None


def get_hand_url():
    return "https://test-esign6.tsign.cn/sign-manage-h5/draw-seal?" + "code=*********&projectId=1000000&lang=zh-CN"


# 获取手绘uuid
def get_hand_seal_uuid(process_id, hand_draw_name, hand_url):
    url = MAIN_HOST + "/esign-signs/handPaintedSeal/base64QrCode"
    token = getPortalToken(sign01_customAccountNo, password_encrypt)
    headers = gen_token_headers_method(PROJECT_ID, PROJECT_SECRET, token, '')
    data = {
        "params":
            {
                "processId": process_id,
                "handDrawName": hand_draw_name,
                "url": hand_url
            },
        "tenantCode": 0
    }
    res = requests.post(url, json=data, headers=headers)
    if json.loads(res.text)["data"] is not None:
        try:
            return json.loads(res.text)["data"]["uuid"]
        except:
            return None
    else:
        return None


# 获取上传手绘的filekey
def get_hand_seal_file_key(uuid, fileBase64):
    # 上传
    url = MAIN_HOST + "/esign-signs/handPaintedSeal/save"
    token = getPortalToken(sign01_customAccountNo, password_encrypt)
    headers = gen_token_headers_method(PROJECT_ID, PROJECT_SECRET, token, '')
    data = {
        "params":
            {
                "uuid": uuid,
                "fileBase64": fileBase64
            },
        "tenantCode": 0
    }
    res = requests.post(url, json=data, headers=headers)
    if json.loads(res.text)["data"] is not None:
        try:
            return json.loads(res.text)["data"]["fileKey"]
        except:
            return None
    else:
        return None


# 通过请求头token获取用户姓名
def getUserName():
    try:
        Url = MAIN_HOST + "/esign-signs/user/ownerInfo"
        headers = get_headers_with_autho(PROJECT_ID)
        res = requests.get(url=Url, headers=headers, )
        return json.loads(res.text)["data"][0]["userName"]
    except:
        return None


# 通过请求头token获取用户所属机构名称
def getOrganizeName():
    try:
        Url = MAIN_HOST + "/esign-signs/user/ownerInfo"
        headers = get_headers_with_autho(PROJECT_ID)
        res = requests.get(url=Url, headers=headers, )
        return json.loads(res.text)["data"][0]["organizeName"]
    except:
        return None


def get_pageNo(detail):
    try:
        return detail["docInfo"][0]["pageNumber"]
    except:
        return None


# 指定文件类型获取filekey
# fileType: ofd, png, jpg, xls, doc, docx, rar cc.
def getFilekeyByType(fileType):
    try:
        if fileType == 'ofd':
            return get_filekey_by_filename("key30page.ofd")
        if fileType == 'png':
            return get_filekey_by_filename("1.png")
        if fileType == 'jpg':
            return get_filekey_by_filename("psb.jpg")
        if fileType == 'docx':
            return get_filekey_by_filename("关键字.docx")
        if fileType == 'xlsx':
            return get_filekey_by_filename("111.xlsx")
        if fileType == 'rar':
            return get_filekey_by_filename("111.rar")
        if fileType == 'tar':
            return get_filekey_by_filename("test.tar")
        if fileType == 'zip':
            return get_filekey_by_filename("111.zip")
        if fileType == 'doc':
            return get_filekey_by_filename("普通doc文件.doc")
    except:
        return None


# 获取web端请求头
def get_web_headers(account, password):
    token = getPortalToken(account, password)
    headers = gen_token_headers_method(PROJECT_ID, PROJECT_SECRET, token, '')
    return headers


# 获取web端请求头
def get_web_headers_token(token):
    headers = gen_token_headers_method(PROJECT_ID, PROJECT_SECRET, token, '')
    return headers


# 获取签署完成的流程的签署文件文件
def get_process_filekey(process_id, loginToken):
    try:
        headers = get_web_headers_token(loginToken)
        detail = get_detail(process_id, False, headers, 2)
        fileKeyInfo = dict()
        if detail:
            fileKeyInfo["fileKey"] = detail["docInfo"][0]["fileKey"]
            fileKeyInfo["cryptoFileKey"] = detail["docInfo"][0]["cryptoFileKey"]
        return fileKeyInfo
    except:
        return None


def get_file_key_by_info(fileKeyInfo: object, keyType: int):
    """
    根据流程详情获取文件id和加密id
    :param fileKeyInfo: 文件信息
    :type fileKeyInfo: object
    :param keyType: 文件id类型 1：普通 2：加密
    :type keyType: int
    :return:
    :rtype:
    """
    if not fileKeyInfo:
        return None
    if keyType == 1:
        print(fileKeyInfo)
        print(fileKeyInfo["fileKey"])
        return fileKeyInfo["fileKey"]
    if keyType == 2:
        return fileKeyInfo["cryptoFileKey"]
    return None


# 获取签署完成的流程的签署文件文件
def get_process_crypto_file_key(process_id, account, password):
    headers = get_web_headers(account, password)
    detail = get_detail(process_id, False, headers, 2)
    if detail is not None:
        try:
            return detail["docInfo"][0]["cryptoFileKey"]
        except:
            print("get_process_crypto_file_key()方法报错了")
            return None
    else:
        print("get_process_crypto_file_key()方法获取文件为null")
        return None


def get_fileKey_from_detail(detail, fileIndex):
    try:
        return detail["docInfo"][fileIndex]["fileKey"]
    except:
        print("get_fileKey_from_detail()方法报错了")
        return None


def download_by_file_key(file_key, cryptoFileKey, loginToken):
    try:
        print("开始下载文件")
        srvUrl = MAIN_HOST + "/esign-signs/signFile/commonDownload"
        headers = get_web_headers_token(loginToken)
        params = {"fileKey": file_key, "cryptoFileKey": cryptoFileKey}
        resp = requests.get(url=srvUrl, params=params, headers=headers)
        if resp.status_code != requests.codes.ok:
            return  # error!
        response_header = resp.headers
        # 头中携带文件信息，可获取用于写文件
        name = response_header.get("x-timevale-fn")
        if name is not None:
            file_name = "case_down1_" + name.split(";")[1].strip().split("\"")[1]
            with open(PathUtil.rootPath + "data/" + file_name, "wb") as conf:
                conf.write(resp.content)
            print("文件下载完成，文件名：" + file_name)
            return file_name
    except Exception as ex:
        print(ex)
        return None


# 根据文件名删除文件 file_name:文件名
def remove_file_by_name(file_name):
    try:
        print("开始删除文件")
        if file_name is not None:
            file_path = PathUtil.rootPath + "data/" + file_name
            if os.path.exists(file_path):
                os.remove(file_path)
                print("文件删除成功，文件名")
            else:
                print("文件不存在删除失败")
    except:
        return None


# 手绘入参url
def get_draw_seal_url():
    return MAIN_HOST + "/sign-manage-h5/draw-seal?projectId=" + PROJECT_ID


# 意愿重定向url
def get_willingRedirectUrl():
    return MAIN_HOST + "/sign-manage-web/sign-page?mcok"


# 连接数据库进行查询,查询sql=param
def connectDB(param):
    print(param)
    try:
        host = dbHost
        user = dbUser
        password = dbPassword
        db = db_signs
        port = dbPort
        # 参数分别为：ip，用户名，密码，库名，端口
        con = pymysql.connect(host=host, user=user, password=password, db=db, port=int(port))
        cursor = con.cursor()
        if param != None:
            cursor.execute(param)
            result = cursor.fetchall()
            return result
    except Exception:
        print('数据库连接异常！')


# 连接数据库进行查询,查询sql=param,获取字段名和字段值
def connectDBForGetNameAndValue(param):
    # print(param)
    try:
        host = dbHost
        user = dbUser
        password = dbPassword
        db = db_signs
        port = dbPort
        # 参数分别为：ip，用户名，密码，库名，端口
        con = pymysql.connect(host=host, user=user, password=password, db=db, port=int(port))
        cursor = con.cursor()
        if param != None:
            cursor.execute(param)
            result = cursor.fetchall()
            resultName = cursor.description
            resultReturn = {}
            for i in range(len(resultName)):
                resultReturn[resultName[i][0]] = result[0][i]
            return resultReturn
    except Exception:
        print('数据库连接异常！')


def connectByDBName(dbname):
    try:
        host = dbHost
        user = dbUser
        passwd = dbPassword
        port = dbPort
        con = connect(host, port, user, passwd, dbname)
        return con
    except Exception:
        print(sys._getframe().f_code.co_name + "-数据库连接异常! ")


from assertDBSql import DBAssertSql


# 查询任务中心的待办任务
def get_taskCenter_detail(type, param):
    sql = ''
    # sleep(5)
    try:
        if param:
            processParam = {"signFlowId": param}
        if type == 1:
            sql = DBAssertSql.GET_TODO_TASK_BY_UUID.format(**processParam)
        if type == 2:
            sql = DBAssertSql.GET_CARBON_COPY_BY_UUID.format(**processParam)
        if type == 3:
            sql = DBAssertSql.GET_MY_START_BY_UUID.format(**processParam)
        if sql:
            # 查询任务中心的数据
            dbname = db_manage
            connect = connectByDBName(dbname)
            res = execute_sql_convert_array(connect, sql)
            return res
    except Exception:
        print(sys._getframe().f_code.co_name + "-调用异常")


def deal_list_data(list, index, key):
    '''
    处理数据
    :param list: [{'test':1,'test2':2},{'test':11,'test2':12}]
    :param index: 获取第几个对象从0开始
    :param key: 获取对象中的键值对
    :return:
    '''
    try:
        if list:
            res = list[index][key]
            print('处理结果：', res)
            return res
    except Exception:
        print(sys._getframe().f_code.co_name + "-调用异常")


def deal_type_data(data):
    '''
    将传入的数据强制转换为string
    :param data:
    :return:
    '''
    try:
        return str(data)
    except Exception:
        print(sys._getframe().f_code.co_name + "-调用异常")


# 获取符合自定义场景的业务类型。如：不支持手绘，只支持内部个人签署，只支持相对方机构签署等场景
# enableStr=None 则支持全部勾选的业务类型
# 固定数据-业务类型名称是：自动化测试业务类型（勿动）
# 适配只支持内部机构gen_businessTypeId_Scene({"sealType":"2","signEndTimeEnable":0,"signerRange":"1"})
def gen_businessTypeId_Scene(enableStr):
    return businessTypeCode


# 新建不同的业务类型，且适配各种场景
def gen_businessTypeId_Scene2(enableStr):
    return businessTypeCode

# 更新业务类型
# enableStr 需要传入dict格式，如：{"businessTypeName":"测试业务类型","downloadEnable":1}
def gen_businessType_update(templataId, enableStr):
    try:
        webHost = MAIN_HOST
        headers = get_headers_with_autho(PROJECT_ID)
        Url02 = webHost + "/esign-docs/businessPreset/blockUp/" + templataId
        data02 = {"params": {}}
        requests.post(Url02, json=data02, headers=headers)
        # 修改业务模板配置（消息配置）
        Url = webHost + "/esign-docs/businessPreset/addBusinessType"
        data = {}
        if templataId:
            data['params'] = gen_businessType_detail(templataId)
            data['params']['presetId'] = templataId
            businessTypeName = data['params']['businessTypeName']
            businessTypeId = data['params']['businessTypeId']
            if enableStr:
                if isinstance(enableStr, dict):
                    if enableStr.__contains__('templateCode'):
                        detail = data['params']
                        templateId01 = enableStr['templateCode']
                        messageConfigList = detail["messageConfigList"]
                        messageConfigListv1 = []
                        for config in messageConfigList:
                            configv1 = dict()
                            if config["templateCode"] != templateId01:
                                configv1 = config
                            else:
                                configv1["ccFlag"] = enableStr["ccFlag"]
                                configv1["ownerFlag"] = enableStr["ownerFlag"]
                                configv1["singerFlag"] = enableStr["singerFlag"]
                                configv1["templateCode"] = config["templateCode"]
                                configv1["templateName"] = config["templateName"]
                            messageConfigListv1.append(configv1)
                        detail["messageConfigList"] = messageConfigListv1
                        data = {
                            "params": detail
                        }
                    else:
                        for item in enableStr:
                            data['params'][item] = enableStr[item]
                        print(data['params'][item], enableStr[item])
            sleep(0.5)
            print('业务类型修改: ' + str(templataId) + ": " + str(data))
            businessRes = requests.post(Url, json=data, headers=headers)
            print('businessRes--------->' + str(businessRes.json()))

            # 启动业务模板
            Url04 = webHost + "/esign-docs/businessPreset/addSigners"
            data04 = {"params": {"presetId": templataId, "presetSnapshotId": None, "presetName": businessTypeName,
                                 "status": 1, "initiatorAll": 1, "initiatorEdit": 0, "allowAddFile": 1,
                                 "allowAddSigner": 1, "forceReadingTime": None, "signEndTimeEnable": None, "sort": 0,
                                 "workFlowModelKey": "", "workFlowModelName": "", "workFlowModelStatus": 0,
                                 "signatoryCount": 0, "changeReason": "", "fileFormat": 1}}
            req04 = requests.post(Url04, json=data04, headers=headers)

            if req04.json()['status'] == 200:
                # 判定接口调用成功
                return businessTypeId
            else:
                print(sys._getframe().f_code.co_name + "-templataId=" + str(templataId) + "-businessTypeId=" + str(
                    businessTypeId) + ": " + str(enableStr) + ' 修改失败!')
                return None
    except:
        print("gen_businessType_update method error")
        return None


# 查询业务类型
def gen_businessType_detail(businessTemplateId):
    Url = MAIN_HOST + "/esign-docs/businessPreset/detail/" + os.environ['businessTemplateId']
    headers = get_headers_with_autho(PROJECT_ID)
    params = {}
    businessRes = requests.get(url=Url, params=params, headers=headers)
    try:
        if businessRes.json()['data']:
            # 判定接口调用成功
            return businessRes.json()['data']['signBusinessType']
    except:
        print("gen_businessType_detail()方法报错了")
        return None


# 返回用户组用于signFlowGen文件
def getUserList():
    userList = {}
    userList['userCodeInitiator'] = csqs_userCode
    userList['userCodeSigner'] = sign01_userCode
    userList['userCodeSigner2'] = os.environ['signjz01.userCode']
    userList['userCodeOuterSigner'] = wsignwb01_userCode

    return userList


def get_oppositePerson(param):
    try:
        if (param == "organizationname"):
            sqlStr = "select * from " + db_manage + ".ec_organization where organization_territory=2 and organization_type=1 and deleted=1 order by modify_time"
            print("sqlStr:" + sqlStr)
            sqlQuery = connectDB(sqlStr)
            name = sqlQuery[0][3]
        else:
            sqlStr = "select * from " + db_manage + ".ec_user where user_type=2 and deleted=1 order by `CREATE_TIME` desc"
            sqlQuery = connectDB(sqlStr)
            name = sqlQuery[0][2]
        return name
    except:
        return None


# 返回用户组用于signFlowGen文件
def getUserList():
    userList = {}
    userList['userCodeInitiator'] = csqs_userCode
    userList['userCodeSigner'] = sign01_userCode
    userList['userCodeSigner2'] = os.environ['signjz01.userCode']
    userList['userCodeOuterSigner'] = wsignwb01_userCode

    return userList


# 返回机构组用于signFlowGen文件
def getOrgList():
    orgList = {}
    orgList['organizationCodeInitiator'] = ci_orgCode
    orgList['organizationCodeSigner'] = org01_orgCode
    orgList['oppositeOrganizationCode'] = wsignwb01_main_orgCode

    return orgList


# 返回file用于signFlowGen文件
def getFileKeyForSignFlowGen(ofdFlag):
    try:
        fileKeyList = {}
        if ofdFlag == 1:
            fileKeyList['fileKey'] = ofd_filekey
            fileKeyList['fileKeyFailed'] = os.environ['3PageOFDFileKey']
        else:
            fileKeyList['fileKey'] = pdf_filekey
            fileKeyList['fileKeyFailed'] = os.environ['editEncryptionFileKey']

        return fileKeyList
    except:
        return None


# 根据文件名上传文件获取fileKey
def get_filekey_by_filename(file_name):
    from tools.uploadFile import get_filekey_by_filename
    return get_filekey_by_filename(file_name)


# 根据文件名上传文件获取fileKey
def get_filekey_by_filenamev1(file_name, loginToken):
    try:
        req = common_upload(file_name)
        return req
    except:
        return None

def getFilekeyByType2(param):
    from tools.uploadFile import getFilekeyByType2
    return getFilekeyByType2(param)

# 通过sealId获取印章类型
def gen_sealTypeCode(sealId):
    try:
        headers = get_headers_with_autho(PROJECT_ID)
        result = getSealTypeCode(MAIN_HOST, headers, sealId)
        if result:
            # 判定接口调用成功
            return result
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 通过查询流程详情，获取流程的等待时间
def gen_signFlow_status_sleep(signFlowId):
    if signFlowId:
        try:
            result = get_signFlowDetail(signFlowId)
            signFlowExpireTime0 = result['signFlowExpireTime']
            signFlowStatus0 = result['signFlowStatus']
            print("signFlowExpireTime0:  " + str(signFlowExpireTime0))
            count = 0
            if signFlowStatus0 != 3:
                while (count < 50):
                    count = count + 1
                    print(count)
                    if get_dateTime(0) > str(signFlowExpireTime0):
                        result2 = get_signFlowDetail(signFlowId)
                        signFlowStatus2 = result2['signFlowStatus']
                        if signFlowStatus2 == 3:
                            break
                        else:
                            sleep(5)
                    else:
                        sleep(10)
        except:
            print("gen_signFlow_status_sleep()报错了")


# 发起验证码认证
def createCodeAuth(accountId, bizId):
    url = "/v1/willingness/createCodeAuth"
    data = {
        "accountId": accountId,
        "appId": CLOUD_APPID,
        "bizId": bizId,
        "bizType": "SIGN",
        "sendType": "SMS"
    }
    res = requestPublicCloud(url, data, CLOUD_APPID, CLOUD_APPSECRET, "POST")
    sleep(1)
    return res

# <AUTHOR> wenmin
# @Time : 2022/5/18 14:17
# @Description : 签署中心-无节点签署-获取一个无节点流程
# @param: status=0 获取一个未开启的无节点流程，
# status=1 获取一个开启了的无节点流程，
# status=2 获取一个结束了的无节点流程,
# status=3 无节点流程未添加签署人的开启,
# status=4 只创建流程不添加签署文件
# status=5 获取一个已经签署完成的还未结束的无节点流程
# status=6 获取一个无节点签署流程，无签署方，只添加了签署文件和附件，流程状态是开启
# status=7 获取一个无节点签署流程，无签署方,只添加了签署文件和附件,流程状态是未开启
def gen_startSignFlow_node(status):
    esignSignsOpenApiUrl = OPENAPI_HOST

    projectId = PROJECT_ID
    projectSecrect = PROJECT_SECRET

    userCode = sign01_userCode
    esignManageOpenApiUrl = OPENAPI_HOST
    userInfo = detailInnerUsers(esignManageOpenApiUrl, projectId, projectSecrect, userCode)
    if userInfo:
        userName = userInfo['name']
        organizationCode = userInfo['mainOrganizationCode']
        organizationInfo = detailInnerOrganizations(esignManageOpenApiUrl, projectId, projectSecrect, organizationCode)
        if organizationInfo:
            organizationName = organizationInfo['name']
        else:
            organizationName = ''
    else:
        userName = sign01_userName
        organizationCode = org01_orgCode
        organizationName = sign01_main_orgName

    fileKey = pdf_filekey
    # sealId = sign01_sealId

    try:
        signFlowId = createSignFlow(esignSignsOpenApiUrl, projectId, projectSecrect, userCode, organizationCode,
                                    businessTypeCode)
        if status != 4:
            addFiles(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId, fileKey)
        if status == 1:
            addSigners(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId, fileKey, userCode, organizationCode)
            startSignFlow(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId)
        if status == 3:
            startSignFlow(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId)
        if status == 2:
            # 获取静默签署的印章
            esignSignsWebUrl = MAIN_HOST
            headers = get_headers_with_autho(projectId)
            sealId = getPersonSealId(esignSignsWebUrl, headers, userCode, userName, organizationCode, organizationName)

            addSignersAutoSign(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId, fileKey, userCode, "",
                               sealId)
            startSignFlow(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId)
            sleep(3)
            finishSignFlow(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId)
        if status == 5:
            # 获取静默签署的印章
            esignSignsWebUrl = MAIN_HOST
            headers = get_headers_with_autho(projectId)
            sealId = getPersonSealId(esignSignsWebUrl, headers, userCode, userName, organizationCode, organizationName)

            addSignersAutoSign(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId, fileKey, userCode, "",
                               sealId)
            startSignFlow(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId)
            sleep(3)
        if status == 6:
            startSignFlow(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId)
        if status == 7:
            return signFlowId
        return signFlowId
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


def gen_finishSignFlow_node(signFlowId):
    '''
    结束无节点流程
    :param signFlowId:  无节点流程Id
    :return:
    '''
    esignSignsOpenApiUrl = OPENAPI_HOST

    projectId = PROJECT_ID
    projectSecrect = PROJECT_SECRET
    finishSignFlow(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId)
    return signFlowId


# 获取无节点流程-自定义无节点签署的文件
def gen_signFlowNode_fileKey(fileKey):
    '''
    获取无节点流程-自定义无节点签署的文件
    :param fileKey:
    :return: signFlowIdId
    '''
    esignSignsOpenApiUrl = OPENAPI_HOST

    projectId = PROJECT_ID
    projectSecrect = PROJECT_SECRET

    userCode = sign01_userCode
    esignManageOpenApiUrl = OPENAPI_HOST
    userInfo = detailInnerUsers(esignManageOpenApiUrl, projectId, projectSecrect, userCode)
    organizationCode = userInfo['mainOrganizationCode']
    try:
        signFlowId = createSignFlow(esignSignsOpenApiUrl, projectId, projectSecrect, userCode, organizationCode,
                                    businessTypeCode)
        if fileKey:
            addFiles(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId, fileKey)
        else:
            addFiles(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId, pdf_filekey)
        return signFlowId
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 删除无节点的签署人
def gen_delete_signer(signFlowId, signerInfos):
    print(type(signerInfos))
    '''
    删除无节点的签署人
    :param signFlowId:
    :param signerInfos: [{"organizationCode": "${ENV(sign01.main_orgCode)}","userCode": "${ENV(sign01.userCode)}"}]
    :return: signFlowIdId
    '''
    esignSignsOpenApiUrl = OPENAPI_HOST

    projectId = PROJECT_ID
    projectSecrect = PROJECT_SECRET
    try:
        signFlowId = deleteSigners(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId, signerInfos)
        return signFlowId
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 根据userCode更新内部用户及对应机构信息，重置内部用户和机构实名状态（通过openapi）
def updateUser(userCode):
    url = OPENAPI_HOST + "/manage/v1/innerUsers/update"
    data = {
        "userCode": userCode,
        "customAccountNo": "",
        "name": "测试芷萱自动化修改",
        "licenseNo": "",
        "bankCardNo": ""
    }
    headers = gen_headers_signature(data)
    res = requests.post(url, json=data, headers=headers)
    return res.json()


# 更新机构信息，重置实名
def updateOrg(organizationCode):
    url = OPENAPI_HOST + "/manage/v1/innerOrganizations/update"
    data = {
        "organizationCode": organizationCode,
        "name": "esigntest芷萱0304对公打款修改",
        "licenseNo": ""
    }
    headers = gen_headers_signature(data)
    res = requests.post(url, json=data, headers=headers)
    return res.json()


# 根据外部用户code获取外部用户编码
def getOuterUsersAccountNo(outerUserCode):
    try:
        userInfo = detailOuterUsers(OPENAPI_HOST, PROJECT_ID,
                                    PROJECT_SECRET, outerUserCode)
        customAccountNo = userInfo['customAccountNo']
        return customAccountNo
    except:
        return None


# 内部用户签署实名回调地址
def gen_realName_notifyUrl(userCode, orgCode, processId):
    try:
        notifyUrl = unifiedAuth.gen_inner_notifyUrl(os.environ['notifyUrl'], processId, userCode, orgCode,
                                                    get_constant("applyIdForRealName"))
        return notifyUrl
    except:
        return None


# 由于实名case是独立账号，设置单独生成processId方法
# userCode有值 orgCode无值 创建个人签ID
# userCode有值且orgCode有值 创建机构签ID
def gen_inner_realname_processId(userCode, orgCode):
    Url = OPENAPI_HOST + "/esign-signs/v1/signFlow/createAndStart"
    signFiles = []
    signerInfos = []
    fileKey = pdf_filekey
    signFiles.append(signFlowGen.gen_signFile_data(fileKey))
    personSigner = signFlowGen.gen_signerInfo_data(fileKey, userCode, "", 1, 0)
    signerInfos.append(personSigner)
    if orgCode is not None and len(orgCode) > 0:
        orgSigner = signFlowGen.gen_signerInfo_data(fileKey, userCode, orgCode, 1, 0)
        signerInfos.append(orgSigner)
    data = signFlowGen.gen_signFlow_data(signFiles, signerInfos, businessTypeCode, getUserList(),
                                         getOrgList())
    headers = gen_headers_signature(data)
    res = requests.post(url=Url, headers=headers, json=data)
    jsonData = json.loads(res.text)["data"]
    if jsonData is None:
        print(res.text)
    try:
        signFlowId = jsonData["signFlowId"]
        return signFlowId
    except:
        print("gen_inner_realname_processId()方法报错了: userCode=", userCode, "; orgCode=", orgCode)
        return None


# 静默签方法适配
def autoAdapter(*param):
    type = 1
    arry = []
    num = 0
    for item in param:
        print(str(item))
        if num == 0:
            type = item
            num = 1
        else:
            arry.append(item)

    return autoSign.autoAdapter(type, arry)


# 校验签署回调通知
def check_callBack(status, processId):
    re = callback_check(status, processId)
    if re:
        assert re
        print("回调校验成功")
    else:
        assert False
        print("回调校验失败")


# 根据外部用户code获取外部用户姓名
def getOuterUsersName(outerUserCode):
    try:
        userInfo = detailOuterUsers(OPENAPI_HOST, PROJECT_ID,
                                    PROJECT_SECRET, outerUserCode)
        name = userInfo['name']
        return name
    except:
        return None


# 根据外部机构code获取外部机构姓名
def getOuterOrganizationName(outerOrganizationCode):
    try:
        organizationInfo = detailOuterOrganizations(OPENAPI_HOST, PROJECT_ID,
                                                    PROJECT_SECRET, outerOrganizationCode)
        name = organizationInfo['name']
        return name
    except:
        return None


# 根据外部机构code获取外部机构编码
def getOuterOrganizationNo(outerOrganizationCode):
    try:
        organizationInfo = detailOuterOrganizations(OPENAPI_HOST, PROJECT_ID,
                                                    PROJECT_SECRET, outerOrganizationCode)
        return organizationInfo['customOrgNo']
    except:
        return None


# 根据内部用户code获取对应机构code
def getInnerOrganizationCode(userCode):
    try:
        userInfo = detailInnerUsers(OPENAPI_HOST, PROJECT_ID,
                                    PROJECT_SECRET, userCode)
        return userInfo['mainOrganizationCode']
    except:
        return None


# 根据内部用户code获取内部用户姓名
def getInnerUserName(userCode):
    try:
        userInfo = detailInnerUsers(OPENAPI_HOST, PROJECT_ID,
                                    PROJECT_SECRET, userCode)
        return userInfo['name']
    except:
        return None


# 根据内部用户code获取内部用户证件号
def getInnerUserIdNo(userCode):
    try:
        userInfo = detailInnerUsers(OPENAPI_HOST, PROJECT_ID,
                                    PROJECT_SECRET, userCode)
        return userInfo['licenseNo']
    except:
        return None


# 根据内部用户code获取内部用户银行卡号
def getInnerUserBankNo(userCode):
    try:
        userInfo = detailInnerUsers(OPENAPI_HOST, PROJECT_ID,
                                    PROJECT_SECRET, userCode)
        return userInfo['bankCardNo']
    except:
        return None


# 根据内部机构code获取内部机构名称
def getInnerOrgName(organizationCode):
    try:
        organizationInfo = detailInnerOrganizations(OPENAPI_HOST, PROJECT_ID,
                                                    PROJECT_SECRET, organizationCode)
        customOrgName = organizationInfo["name"]
        return customOrgName
    except:
        return None


# 根据内部机构code获取内部机构证件号
def getInnerOrgIdNo(organizationCode):
    try:
        organizationInfo = detailInnerOrganizations(OPENAPI_HOST, PROJECT_ID,
                                                    PROJECT_SECRET, organizationCode)
        licenseNo = organizationInfo['licenseNo']
        return licenseNo
    except:
        return None


# 根据内部用户code获取内部机构账号
def getInnerOrgAccoutNo(organizationCode):
    try:
        userInfo = detailInnerUsers(OPENAPI_HOST, PROJECT_ID,
                                    PROJECT_SECRET, organizationCode)
        return userInfo["mainCustomOrgNo"]
    except:
        return None


# 根据外部机构code获取外部机构organizationId
def getOuterOrganizationId(outerOrganizationCode):
    try:
        Url = MAIN_HOST + "/esign-signs/opposite/organization/info"
        token = getPortalToken(sign01_customAccountNo, password_encrypt)
        header = {"x-timevale-project-id": PROJECT_ID,
                  "authorization": token}
        params = {"organizationCode": outerOrganizationCode}
        res = requests.get(url=Url, params=params, headers=header)
        print(json.loads(res.text)["data"])
        return json.loads(res.text)["data"]["organizationId"]
    except:
        print("getOuterOrganizationId error")
        return None


def message_willing(applyId, verifyCode, token):
    userInfo = getUserByLoginCode(token)
    mobile = userInfo["data"]["userMobile"]
    sendMessage(applyId, mobile, token)
    sleep(0.3)
    message_auth(applyId, verifyCode, mobile, token)


def message_auth(applyId, verifyCode, phone, token):
    url = MAIN_HOST + "/manage/auth/messageAuth"
    headers = {"Content-Type": "application/json",
               "token": token
               }
    data = {
        "authentication": True,
        "domain": "admin_platform",
        "params": {
            "applyId": applyId,
            "phone": phone,
            "verifyCode": verifyCode
        }
    }
    res = requests.post(url=url, headers=headers, json=data)
    print('意愿结果：', res)


def sendMessage(applyId, mobile, token):
    url = MAIN_HOST + "/manage/auth/sendMessage"
    headers = {"Content-Type": "application/json",
               "token": token
               }
    data = {
        "authentication": True,
        "domain": "admin_platform",
        "params": {
            "applyId": applyId,
            "phone": mobile
        }
    }
    res = requests.post(url=url, headers=headers, json=data)
    print('短信发送结果：', res)


def getUserByLoginCode(token):
    url = MAIN_HOST + "/manage/orguser/user/getUserByLoginCode"
    headers = {"Content-Type": "application/json",
               "token": token
               }
    data = {
        "authentication": True,
        "domain": "admin_platform"
    }
    res = requests.post(url=url, headers=headers, json=data)
    userInfo = json.loads(res.text)
    return userInfo


# 获取签署意愿认证applyId
# param   processId流程id
def get_sign_applyId(processId, orgCode):
    headers = get_headers_with_autho(PROJECT_ID)
    url = MAIN_HOST + "/esign-signs/auth/willing"
    data = {
        "params": {
            "processId": processId,
            "redirectUrl": MAIN_HOST + "/sign-manage-web/sign-page?mcok",
            "clientType": 0,
            "organizeCode": orgCode
        }
    }
    res = requests.post(url=url, headers=headers, data=json.dumps(data))
    try:
        if res:
            jsonData = json.loads(res.text)
            if jsonData["data"]['needRealNameFlag'] == True:
                print('签署账号未实名，需要手动维护实名状态,signFlowId=', processId)
                return None
            token = parse_tokenKey(jsonData["data"]['url'])
            applyId = jsonData["data"]["applyId"]
            password_auth(applyId, password_encrypt, token)
            return applyId
    except:
        print("get_sign_applyId()方法报错了")
        return None


# <AUTHOR> hechu
# @Time : 2022/5/24 17:21
# @Description : 删除签署方（添加的不删除，不然其他用例没办法再添加）
# @param: signFlowId, organizeCode,userCode
def hook_signersDelete(signFlowId, organizationCode, userCode):
    print("调用hook_signersDelete:" + signFlowId + ":" + organizationCode + ":" + userCode)
    esignSignsOpenApiUrl = OPENAPI_HOST
    projectId = PROJECT_ID
    projectSecrect = PROJECT_SECRET

    try:
        print("调用hook_signersDelete:" + esignSignsOpenApiUrl + ":" + projectId + ":" + projectSecrect)
        signersDelete(esignSignsOpenApiUrl, projectId, projectSecrect, signFlowId, organizationCode, userCode)
    except:
        print(sys._getframe().f_code.co_name + "-调用hook_signersDelete异常")
        return None


# 根据外部用户code获取外部用户的saasId
def get_out_user_sassId(userCode):
    try:
        sql = "select esign_saas_id from " + db_manage + ".ec_user where user_code = '%s'" % userCode
        sqlQuery = connectDB(sql)
        if sqlQuery:
            return sqlQuery[0][0]
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 根据外部机构code获取外部机构的saasId
def get_out_org_sassId(orgCode):
    try:
        sql = "select esign_saas_id from " + db_manage + ".ec_organization where organization_code = '%s'" % orgCode
        sqlQuery = connectDB(sql)
        if sqlQuery:
            return sqlQuery[0][0]
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


def get_timestamp1():
    return message.get_timestamp1()


def check_message1(processUuid, messageType, signNode, signOrder, timestamp):
    print('check_message1[request]: ', processUuid, messageType, signNode, signOrder, timestamp)
    """
    消息校验
    """
    print("开始执行校验: " + processUuid)
    sleep(5)
    flag = message.check_message(processUuid, messageType, signNode, signOrder, timestamp)
    sleep(5)
    if not flag:
        raise Exception("消息比对失败，请检查, processId:" + processUuid)
    return flag


def check_message_not_send(response, messageType, signNode, signOrder, timestamp):
    """
    消息校验
    """
    sleep(3)
    processId = response.json["data"]["processId"]
    flag = message.check_message_not_send_invite(processId, messageType, signNode, signOrder, timestamp, False)
    sleep(2)
    if flag:
        raise Exception("消息比对失败，请检查")
    return flag


def check_message_not_sendv1(processId, messageType, signNode, signOrder, timestamp):
    """
    消息校验
    """
    sleep(3)
    flag = message.check_message_not_send_invite(processId, messageType, signNode, signOrder, timestamp, False)
    sleep(2)
    if flag:
        raise Exception("消息比对失败，请检查， processId:" + processId)
    return flag


def check_signed_cancel_message(response, messageType):
    processId = response.json["data"]["signFlowId"]
    subject = response.json["data"]["subject"]
    time.sleep(1)
    # return check_message1(processId, messageType, None, None, processId)
    # print("开始执行校验" + processUuid)
    sleep(3)
    flag = message.check_message(processId, messageType, None, None, subject)
    sleep(2)
    if not flag:
        raise Exception("消息比对失败，请检查, processId:" + processId + ", messageType:" + messageType)
    return flag


def check_message_response(response, messageType, signNode, signOrder, timestamp):
    """
    消息校验
    """
    sleep(3)
    processId = response.json["data"]["signFlowId"]
    flag = message.check_message(processId, messageType, signNode, signOrder, timestamp)
    sleep(2)
    if not flag:
        raise Exception("消息比对失败，请检查, processId:" + processId)


def check_message_response1(response, messageType, signNode, signOrder):
    """
    消息校验
    """
    sleep(3)
    processId = response.json["data"]["processId"]
    subject = response.json["data"]["subject"]
    flag = message.check_message(processId, messageType, signNode, signOrder, subject)
    sleep(2)
    if not flag:
        raise Exception("消息比对失败，请检查, processId:" + processId)


def check_message_response2(response, messageType, signNode, signOrder, subject):
    """
    消息校验
    """
    sleep(5)
    processId = response.json["data"]["processId"]
    flag = message.check_message(processId, messageType, signNode, signOrder, subject)
    sleep(5)
    if not flag:
        raise Exception("消息比对失败，请检查")


def check_message_defaultData(defaultData, messageType, signNode, signOrder, timestamp):
    sleep(3)
    processId = defaultData["params"]["processUUId"]
    flag = message.check_message(processId, messageType, signNode, signOrder, timestamp)
    sleep(2)
    if not flag:
        raise Exception("消息比对失败，请检查, processId:" + processId)


def check_message_is_auto_signV1(response, messageType, signNode, signOrder, timestamp):
    sleep(3)
    processId = response.json["data"]["signFlowId"]
    return check_message_is_auto_sign(processId, messageType, signNode, signOrder, timestamp)


def check_message_is_auto_signV2(response, messageType, signNode, signOrder):
    sleep(3)
    processId = response.json["data"]["processId"]
    subject = response.json["data"]["subject"]
    flag = message.check_message_is_auto_sign(processId, messageType, signNode, signOrder, subject)
    sleep(2)
    if not flag:
        raise Exception("消息比对失败，请检查, processId:" + processId)


def check_message_is_auto_sign(processUuid, messageType, signNode, signOrder, timestamp):
    """
    消息校验-静默签
    """
    sleep(3)
    print("静默签开始执行校验" + processUuid)
    flag = message.check_message_is_auto_sign(processUuid, messageType, signNode, signOrder, timestamp)
    sleep(2)
    if not flag:
        raise Exception("消息比对失败，请检查, processId:" + processUuid)
    return flag


def check_sign_cancel_message(response, messageType):
    sleep(3)
    processId = response.json["data"]["revokedSignFlowId"]
    # return check_message1(processId, messageType, None, None, processId)
    # print("开始执行校验" + processUuid)
    flag = message.check_message(processId, messageType, None, None, processId)
    sleep(2)
    if not flag:
        raise Exception("消息比对失败，请检查, processId:" + processId + ", messageType: " + messageType)
    return flag


def get_message_subject(timestamp: str):
    """
    获取流程主体名称
    :param timestamp: 时间戳字符串
    :type timestamp: str
    :return: subject
    :rtype: str
    """
    return str(timestamp) + "-case-subject-" + get_randomNo_str()


def set_signtureConfig(json):
    date = get_dateTime2(json)
    signtureConfig = SigntureConfig()
    signtureConfig.expireTime = date
    signtureConfig.subject = json.get("subject")
    signtureConfig.signModel = json.get("signModel")
    signtureConfig.businessTypeCode = json.get("businessTypeCode")
    signtureConfig.businessNo = get_business_no()
    signtureConfig.userCode = json.get("userCode")
    signtureConfig.organizeCode = json.get("organizeCode")
    signtureConfig.processId = json.get("processId")
    return signtureConfig


# bizTypeCode 代表的是业务模板的ID
def reset_business_type_v1(bizTypeCode: str):
    """
    重置业务类型消息配置
    :param bizTypeCode:
    :type bizTypeCode:
    :return:
    :rtype:
    """
    bizTypeConfig = {
        "messageConfigList": [
            {"ccFlag": "2", "ownerFlag": "2", "singerFlag": "1", "templateCode": "SIGN_INVITE",
             "templateName": "邀请签署通知"},
            {"ccFlag": "2", "ownerFlag": "1", "singerFlag": "1", "templateCode": "SIGN_SIGNER_SIGNED",
             "templateName": "签署人签署完成通知"},
            {"ccFlag": "1", "ownerFlag": "1", "singerFlag": "1", "templateCode": "SIGN_FLOW_FINISH",
             "templateName": "签署流程完结通知"},
            {"ccFlag": "2", "ownerFlag": "1", "singerFlag": "1", "templateCode": "SIGN_FLOW_EXPIRE_REMIND",
             "templateName": "签署截止前通知"},
            {"ccFlag": "2", "ownerFlag": "1", "singerFlag": "1", "templateCode": "SIGN_FLOW_OVERTIME",
             "templateName": "签署流程过期通知"},
            {"ccFlag": "2", "ownerFlag": "1", "singerFlag": "1", "templateCode": "SIGN_FLOW_REFUSE",
             "templateName": "签署人拒签通知"},
            {"ccFlag": "1", "ownerFlag": "1", "singerFlag": "1", "templateCode": "SIGNED_FLOW_CANCEL",
             "templateName": "签署完成作废通知"},
            {"ccFlag": "2", "ownerFlag": "1", "singerFlag": "1", "templateCode": "SIGN_FLOW_CANCEL",
             "templateName": "签署中作废通知"}]
    }
    gen_businessType_update(bizTypeCode, bizTypeConfig)


def reset_businessType():
    bizTypeCode = businessTypeCode
    reset_business_type_v1(bizTypeCode)


def make_message_config(businessTemplateId: str, templateId: str, ccFlag: str, ownerFlag: str, signerFlag: str):
    """
    更新业务类型的消息配置
    :param businessTypeCode: 业务类型编码
    :type businessTypeCode: str
    :param templateId: 消息模板id
    :type templateId: str
    :param ccFlag: 抄送人标记
    :type ccFlag: str
    :param ownerFlag: 发起人标记
    :type ownerFlag: str
    :param signerFlag: 签署人标记
    :type signerFlag: str
    :return:
    :rtype:
    """
    reqObject = {'ccFlag': ccFlag, 'ownerFlag': ownerFlag, 'singerFlag': signerFlag, 'templateCode': templateId}
    return gen_businessType_update(businessTemplateId, reqObject)


# 执行sql
def executeSql(sql):
    try:
        con = base_execute_db()
        cursor = con.cursor()
        if cursor == None:
            print('cursor 数据库连接异常！')
        elif sql != None:
            cursor.execute(sql)
            con.commit()
            cursor.close()
            con.close()
    except Exception:
        print('execute 数据库连接异常！')


# 查询sql
def get_execute_sql(sql):
    con = base_execute_db()
    cursor = con.cursor()
    if cursor == None:
        print('cursor 数据库连接异常！')
    elif sql != None:
        cursor.execute(sql)
        sqlQuery = cursor.fetchall()
        if sqlQuery:
            return sqlQuery[0][0]


# 数据库统一封装
def base_execute_db():
    try:
        host = dbHost
        user = dbUser
        password = dbPassword
        db = db_signs
        port = dbPort
        # 参数分别为：ip，用户名，密码，库名，端口
        return pymysql.connect(host=host, user=user, password=password, db=db, port=int(port), connect_timeout=2)
    except Exception:
        print('execute 数据库连接异常！')
        return None


# <AUTHOR> qianfan
# @Time : 2022/5/24 17:21
# @Description : 比较数据库字段值
# @param: tableName表名，compareDict正确的字段名和值，fieldName查询的字段名，fieldValue查询的字段值
def compare_db_data(tableName, compareDict, fieldName, fieldValue):
    sqlStr = "select * from " + db_signs + "." + tableName + " where " + fieldName + "='" + str(
        fieldValue) + "'"
    sqlQuery = connectDBForGetNameAndValue(sqlStr)
    if sqlQuery:
        for key in compareDict:
            keyStr = str(key)
            valueDb = sqlQuery[keyStr]
            valueDict = compareDict[keyStr]
            if type(valueDb) == datetime.datetime:
                milliseconds = int(round(valueDb.timestamp() * 1000))
                true_time = datetime.datetime.strptime(valueDict, "%Y-%m-%d %H:%M:%S")
                yes_time = true_time + datetime.timedelta(days=-1)
                tom_time = true_time + datetime.timedelta(days=+1)
                millisecondsYes = int(round(yes_time.timestamp() * 1000))
                millisecondsTom = int(round(tom_time.timestamp() * 1000))
                if millisecondsYes > milliseconds or millisecondsTom < milliseconds:
                    print("表" + tableName + ",字段" + keyStr + "数据有误，数据库是" + str(valueDb) + ",实际应该是" + str(valueDict))
                    return 0
            elif valueDb != valueDict:
                print("表" + tableName + ",字段" + keyStr + "数据有误，数据库是" + str(valueDb) + ",实际应该是" + str(valueDict))
                return 0
    else:
        print("没有该数据" + fieldValue)
        return 0
    return 1


# <AUTHOR> qianfan
# @Time : 2022/5/24 17:21
# @Description : 根据流程id获取流程配置id
# @param: processId流程id
def get_processConfigId(processId):
    sqlStr0 = "select * from " + db_signs + ".process" + " where uuid='" + processId + "'"
    sqlQuery0 = connectDB(sqlStr0)
    if sqlQuery0:
        sqlStr1 = "select * from " + db_signs + ".process_config" + " where process_id='" + str(
            sqlQuery0[0][0]) + "'"
        sqlQuery1 = connectDB(sqlStr1)
        if sqlQuery0:
            return sqlQuery1[0][0]
        else:
            print("没有process_config数据" + sqlQuery0[0][0])
    else:
        print("没有process数据" + processId)


def get_processuuid_by_sign_data(signData):
    """
    获取流程id
    :param signData: 签署结果
    :type signData:
    :return:
    :rtype:
    """
    return signData["params"]["processUUId"]

# <AUTHOR> wenmin
# @Time : 2022/7/06 15:47
# @Description : 新建不同状态的印章（organizeCodeSigner这个机构），返回印章ID
# @param: status=1 获取一个新的机构印章，不授权给任何机构
def getNewOrgSealId(status):
    try:
        if status == 1:
            print('todo')
        return None
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# <AUTHOR> wenmin
# @Time : 2022/7/27 15:36
# @Description : 查询特定状态的个人用户的某项数据
# 如查询内部离职用户的customAccountNo: getUserInfoByDB(2,'account_number', 1)
# 查询内部删除用户的customAccountNo:  getUserInfoByDB(6,'account_number', 1)
# 查询已删除内部用户的所属机构的customAccountNo:  getUserInfoByDB(9,'sign01', 1)
def getUserInfoByDB(status, key, territory):
    '''
    查询特定状态的个人用户的某项数据
    :param status: 用户状态(1在职、2离职、3活跃、4注销、5未启用),6-代表删除,
    7-代表主责机构类型是部门的用户,
    8-查询一个有效的带有特殊字符的用户信息,
    9-查询用户number的所属主责机构的number
    :param key: ec_user表中的表字段信息
    :param territory: 1-内部用户，2-外部用户
    :return:
    '''
    try:
        dbname = db_manage
        connect = connectByDBName(dbname)

        t03 = ''
        t04 = ''
        if status == 6:
            t03 = " deleted = 1 "
            t04 = "group by %s having(count(*)=1)" % key
        else:
            t03 = " user_status = '%s'  and deleted = 0 " % status
        if status == 7:
            t03 = " deleted =0 and organization_id in (select id from ec_organization where organization_type=2)"
        if status == 8:
            t03 = " deleted =0 and " + key + " like '%·%'"
        if status == 9:
            t03 = "account_number='%s' " % key
            key = 'organization_id'

        t01 = "select %s from  ec_user where " % key
        t02 = " and user_territory = %s " % territory
        sql = t01 + t03 + t02
        if t04:
            sql = sql + t04
        res = execute_sql_convert_object(connect, sql)
        if res:
            if status == 9:
                orgId = res['organization_id']
                sql2 = "select * from ec_organization where id='%s'; " % orgId
                res = execute_sql_convert_object(connect, sql2)
                return res['account_number']
            return res[key]
        else:
            print(sys._getframe().f_code.co_name + 'sql查询数据异常')
        return "not_exist"
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# <AUTHOR> wenmin
# @Time : 2022/7/28 11:36
# @Description : 查询特定状态的个人用户的某项数据
# 如查询内部机构的no getOrgInfoByDB(1,'account_number', 1, 1)
# 如通过orgId查询内部机构的no getOrgInfoByDB(10,'1552253215245565953', 1, 1)
def getOrgInfoByDB(status, key, territory, type):
    '''
    查询特定状态组织的某项数据
    :param status: 组织状态(1存续、2注销、3未启用),6-代表删除,
    10-根据orgid查询number
    :param key: ec_user表中的表字段信息
    :param territory: 1-内部组织，2-外部组织
    :param type: 1-公司，2-部门
    :return:
    '''
    try:
        dbname = db_manage
        connect = connectByDBName(dbname)

        t03 = " organization_status = 1 "
        if status == 6:
            t03 = " deleted = 1 "
        else:
            t03 = " organization_status = %s and deleted = 0 " % status
        t04 = " and organization_type = %s ;" % type
        if status == 10:
            t03 = " id = '%s' " % key
            key = 'account_number'
            t04 = " ;"

        t01 = "select %s from  ec_organization where " % key
        t02 = " and organization_territory = %s " % territory
        sql = t01 + t03 + t02 + t04
        res = execute_sql_convert_object(connect, sql)
        if res:
            return res[key]
        else:
            print(sys._getframe().f_code.co_name + 'sql查询数据异常')
        return None
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 断言校验
def assertNumber(errCode, key, value, result):
    '''
    检查接口出参的结果是否符合预期
    :param errCode: 200
    :param key: 检查的元素 (用户账号特殊处理检查code和no)
    :param value: 检查元素的值
    :param result: 接口实际的出参对象
    :return: 0-成功，1-对比失败
    '''
    try:
        print(errCode, key, value, result)
        dict2 = {}
        if errCode == 200:
            if (key != 'customAccountNo' and key != 'userCode'):
                dict2 = {key: value}
            else:
                if key == 'userCode' and value != '':
                    userCode0 = value
                    customAccountNo0 = get_inneruser_customAccountNo(userCode0)

                if key == 'customAccountNo' and value:
                    customAccountNo0 = value
                    userCode0 = get_inneruser_userCode(customAccountNo0, 'userCode')
                dict2 = {'userCode': userCode0, 'customAccountNo': customAccountNo0}
            l = len(dict2)
            if result:
                if type(result) == list:
                    for i in range(0, len(result)):
                        count = 0
                        if l < 2:
                            if dict2[key] == result[i][key]:
                                return 0
                            else:
                                continue
                        else:
                            for k in dict2.keys():
                                if dict2[k] == result[i][k]:
                                    count = count + 1
                                    continue
                                else:
                                    break
                            if count == l:
                                return 0
                            count = 0
                    if count == 0:
                        return 1
                if type(result) == dict:
                    count = 0
                    if l < 2:
                        if dict2[key] == result[key]:
                            return 0
                        else:
                            return 1
                    else:
                        for k in dict2.keys():
                            if dict2[k] == result[k]:
                                count = count + 1
                                continue
                            else:
                                return 1
                        if count == l:
                            return 0
        else:
            # 接口errcode非200的时候，一律不断言，返回true
            return 0
    except:
        print(sys._getframe().f_code.co_name + "-调用异常-key不存在")
        return 1


def gen_batch_sign_with_process_ids(uuidList, organizecodeList):
    time.sleep(2)
    try:
        url = MAIN_HOST + "/esign-signs/portal/task/batchSign"
        workInstance = []
        data = {
            "domain": "unified_portal_service",
            "params": {
                "workflowInstanceIds": workInstance
            }
        }
        for i in range(len(uuidList)):
            item = {
                "workflowInstanceId": uuidList[i],
                "organizeCode": ""
            }
            if organizecodeList is not None and organizecodeList[i] is not None:
                item["organizeCode"] = organizecodeList[i]
            workInstance.append(item)
        header = gen_unified_portal_header()
        res = requests.post(url=url, json=data, headers=header)
        print("创建批量签署任务：",json.loads(res.text))
        return json.loads(res.text).get("data").get("batchTaskUuId")
    except Exception as ex:
        print(ex)
        return None


# param = [#ofdFlag#,#ukeyOnly#]
# param = [0,0] pdf签署
# param = [0,1] pdf签署,仅支持UKey签署
# param = [1,0] ofd签署
# param = [1,1] ofd签署,仅支持UKey签署
# param = [2,0] pdf和ofd混合签署
def gen_simple_batch_sign(param):
    try:
        url = MAIN_HOST + "/esign-signs/portal/task/batchSign"
        if len(param) == 0:
            param = [0, 0]
        if param[0] == 1 and param[1] == 0:
            processId1 = get_signFlowId_status(1, get_business_no(), True, False, 0, 1, 1)
            processId2 = get_signFlowId_status(1, get_business_no(), True, False, 0, 1, 1)
        if param[0] == 0 and param[1] == 0:
            processId1 = get_signFlowId_status(1, get_business_no(), True, False, 0, 1, 0)
            processId2 = get_signFlowId_status(1, get_business_no(), True, False, 0, 1, 0)
        if param[0] == 2:
            processId1 = get_signFlowId_status(1, get_business_no(), True, False, 0, 1, 0)
            processId2 = get_signFlowId_status(1, get_business_no(), True, False, 0, 1, 1)

        data = {
            "domain": "unified_portal_service",
            "params": {
                "workflowInstanceIds": [
                    {
                        "workflowInstanceId": processId1,
                        "organizeCode": ""
                    },
                    {
                        "workflowInstanceId": processId2,
                        "organizeCode": ""
                    }
                ]
            }
        }
        header = gen_unified_portal_header()
        res = requests.post(url=url, json=data, headers=header)
        return json.loads(res.text).get("data").get("batchTaskUuId")
    except Exception as ex:
        print(ex)
        return None


def get_batch_sign_info(batchTaskUuid, isDefaultSign):
    print("批量签署任务：",batchTaskUuid)
    try:
        url = MAIN_HOST + "/esign-signs/task/getBatchSignInfo"
        data = {
            "params": {
                "batchTaskUuId": batchTaskUuid,
                "isDefaultSign": isDefaultSign
            }
        }
        header = get_headers_with_autho(PROJECT_ID)
        res = requests.post(url=url, json=data, headers=header)
        print("获取批量签署信息：", json.loads(res.text).get("data"))
        return json.loads(res.text).get("data")
    except Exception as ex:
        print("获取批量签署信息异常")
        print(ex)
        return None


def extract_taskDetailIds_from_batch_info(batchSignInfodata):
    try:
        processList = batchSignInfodata.get("processList")
        taskDetailIds = []
        for process in processList:
            taskDetailIds.append(process.get("taskDetailId"))
        return taskDetailIds
    except Exception as e:
        print(e)


def extract_actorIds_from_batch_info(batchSignInfoData):
    try:
        print("批量签署流程中获取签署方：",batchSignInfoData)
        processList = batchSignInfoData.get("processList")
        actorIds = []
        for process in processList:
            actorIds.append(process.get("actorUuId"))
        return actorIds
    except Exception as ex:
        print("extract_actorIds_from_batch_info， 提取actorIds失败")
        print(ex)
        return None


def batch_willing(actorIds):
    try:
        data = {
            "params": {
                "actorIds": actorIds,
                "clientType": 0,
                "redirectUrl": ""
            }
        }
        url = MAIN_HOST + "/esign-signs/auth/batchWilling"
        header = get_headers_with_autho(PROJECT_ID)
        res = requests.post(url=url, json=data, headers=header)
        return json.loads(res.text).get("data")
    except Exception as ex:
        print(ex)
        return None


# 获取发布平台id
def search_ip(serviceName=None, namespaceId=None):
    """
     获取服务ip，端口
     获取ip，端口不传；获取端口，则ip不传
     :param serviceName: nacos上的服务名，例 providers:cn.esign.ka.manage.facade.service.unifiedauth.RpcUserIdentityService
     :param namespaceId: nacos上的命名空间id， 例 0ee4f717-27a6-41b0-a09a-09068c9867ea
     :param nacosUrl: nacos上的url
     :param ip:
     :param port:
     :return:
     """
    nacosUrl = os.environ['nacosUrl']
    data = requests.get(
        url=f'{nacosUrl}/nacos/v1/ns/instance/list?serviceName=providers:{serviceName}::&namespaceId={namespaceId}')
    print(data.json())
    # serviceName 服务名称
    if len(data.json()['hosts']) == 1:
        return data.json()['hosts'][0]['ip']


# 查询意愿结果，后期可删
def auth_result(applyId):
    url = MAIN_HOST + "/esign-signs/auth/willing/result?batchFlag=true" + "&applyId=" + applyId
    header = get_headers_with_autho(PROJECT_ID)
    res = requests.get(url=url, headers=header)
    return json.loads(res.text).get("data").get("result")


def gen_unified_portal_header():
    authorization = PORTAL_TOKEN
    headers = {"Content-Type": "application/json", "x-timevale-project-id": PROJECT_ID, "authorization": authorization}
    headers['code'] = PORTAL_CODE
    return headers


def extract_from_array(array, index):
    return array[index];


# ====实名认证相关公有云接口===============
# 公有云请求头信息
def cloud_get_token(mobile):
    url = cloud_login_url + '/account-webserver/login/commit/user'
    headers = {
        "X-Tsign-Open-App-Id": CLOUD_APPID,
        "X-Tsign-Open-Auth-Mode": "simple",
        "Content-Type": "application/json",
        "X-Tsign-Service-GROUP": "DEFAULT"
    }
    body = {"principal": mobile, "credentials": "593f53728678566930be2a982efc5dda",
            "loginParams": {"endpoint": "PC", "env": {"fingerprint": "ed178723575e3cb643e54d53011669af"},
                            "needCheck": False}, "data": {"safeCheck": True}}
    response = requests.post(url, headers=headers, json=body)
    print(url)
    print(response.json())
    print(response.headers)
    return_token = response.headers["Set-Cookie"]
    print(return_token)
    if response.text.__contains__('您未设置密码，请通过验证码登录'):
        # 设置登录密码：abc_123456
        cloud_pwdReset_login(mobile)
        cloud_get_token(mobile)
    return return_token, response.json().get('data').get('ouid')


def cloud_gen_headers_webservice(account):
    headers = {
        "X-Tsign-Open-App-Id": CLOUD_APPID,
        "X-Tsign-Open-Auth-Mode": "simple",
        "Content-Type": "application/json",
        "X-Tsign-Service-Id": "footstone-oauth",
        "X-Tsign-Service-Group": "DEFAULT"
    }
    if (account):
        token = cloud_get_token(account)
        headers['cookie'] = token[0]
        headers['oid'] = token[1]
    return headers


# 重置标准签下个人实名
def cloud_resetIdentity(mobile):
    url = "http://datafactory.smlk8s.esign.cn/sign/resetIdentity/"
    headers = {
        "Content-Type": "application/json"
    }
    req_json = {"principal": mobile}
    if CLOUD_APPID.startswith('4'):
        headers['myenvironment'] = "simulation"
    else:
        headers['myenvironment'] = "test"
    response = requests.post(url, headers=headers, json=req_json, verify=False)
    res_json = json.loads(response.text)
    print('重置标准签下个人实名：', res_json)
    if res_json.get("code") != 0 or None == res_json.get("data"):
        print('接口调用异常：重置标准签下个人实名失败')
        return
    return res_json.get("data").get("account")

# 个人实名
def cloud_telecom3Oid(mobile):
    url = "http://datafactory.smlk8s.esign.cn/realname/telecom3Oid/"
    headers = {
        "Content-Type": "application/json"
    }
    # 个人已有oid的情况下-通过手机号查询标准签的oid：
    psnIdentity = cloud_query_psnIdentity(mobile)
    if psnIdentity[0] == 1:
        print('mobile: ' + mobile + " 已实名,无需再次实名")
        return True
    accountOid = psnIdentity[2]
    req_json = {"accountId": accountOid, "input_appid": CLOUD_APPID, "mobile": mobile}
    if CLOUD_APPID.startswith('4'):
        headers['myenvironment'] = "simulation"
    else:
        headers['myenvironment'] = "test"
    response = requests.post(url, headers=headers, json=req_json, verify=False)
    res_json = json.loads(response.text)
    print('个人已有oid的实名：', res_json)
    if res_json.get("code") != 0 or None == res_json.get("data"):
        print('接口调用异常：个人三要素实名失败')
        return False
    return res_json.get("data").get("realnameStatus")


# 个人授权
def cloud_psn_auth_url(mobile):
    url = cloud_url + "/v3/psn-auth-url"
    headers = cloud_gen_headers_webservice('')
    psn_json = {"authorizeConfig": {"authorizedScopes": ["get_psn_identity_info", "manage_psn_resource"]},
                "notifyUrl": "", "psnAuthConfig": {"psnAccount": mobile}, "repeatableRealName": False}
    response = requests.post(url, headers=headers, json=psn_json, verify=False)
    res_json = json.loads(response.text)
    print('个人授权', res_json)
    if res_json.get("code") != 0 or None == res_json.get("data"):
        return None
    return res_json.get("data").get("authFlowId")


# 企业授权
# mobile-经办人也就是管理员手机号，orgId是企业的oid
def cloud_org_auth_url(mobile, orgId):
    url = cloud_url + "/v3/org-auth-url"
    headers = cloud_gen_headers_webservice('')
    psn_json = {"authorizeConfig": {
        "authorizedScopes": ["get_org_identity_info", "get_psn_identity_info", "manage_org_resource",
                             "manage_psn_resource"]},
        "orgAuthConfig": {"orgName": "", "orgId": orgId, "orgInfo": {"orgIDCardType": "", "orgIDCardNum": ""},
                          "transactorInfo": {"psnId": "", "psnAccount": mobile}}, "repeatableRealName": False}
    response = requests.post(url, headers=headers, json=psn_json, verify=False)
    res_json = json.loads(response.text)
    print(url)
    print('企业授权', res_json)
    if res_json.get("code") != 0 or None == res_json.get("data"):
        return
    return res_json.get("data").get("authFlowId")


# 确认授权后，获取意愿链接，并完成意愿 暂仅仅支持使用签署密码完成实名意愿
def cloud_doAuthWillingness(authFlowId, mobile):
    try:
        # 组装请求确认授权
        web_login_headers = cloud_gen_headers_webservice(mobile)
        authOid = web_login_headers.get('oid')
        confirmAuth_url = cloud_login_url + "/webserver/v3/oauth/confirm-auth"
        confirmAuth_body = {
            "authFlowId": authFlowId,
            "authOid": authOid,  # 授权主体:个人-个人oid，企业-企业oid
            "bizAppId": CLOUD_APPID,  # 认证业务appId
            "timestamp": get_timestamp(0)  # 请求时间戳，单位精确到毫秒
        }
        confirmAuth_response = requests.post(confirmAuth_url, headers=web_login_headers,
                                             json=confirmAuth_body, verify=False)
        print("提交授权信息：" + confirmAuth_response.text)
        confirmJson = json.loads(confirmAuth_response.text)

        authUrl = confirmJson.get("data").get("authUrl")
        authFlowId = confirmJson.get("data").get("authFlowId")

        headers = {"Content-Type": "application/json;charset=utf-8"}

        print("确认授权后，返回意愿地址（原始）：" + authUrl)
        replace_url_str1 = cloud_h5_url + "/identity/login?willAuthParam="
        replace_url_str2 = cloud_login_url + "/webserver-will/v1/willingness/login?param="
        willUrl = authUrl.replace(replace_url_str1, replace_url_str2)
        print("确认授权后，返回的实名意愿地址：" + willUrl)
        response = requests.get(willUrl, headers=headers, verify=False)

        sessionId = response.json().get("data").get("sessionId")
        print("操作登录实名地址成功，sessionId: " + sessionId)

        pwdAuth = cloud_login_url + "/webserver-will/v1/willingness/sys_transId/pwdAuth?transId=sys_transId"
        print("完成实名意愿请求URL（签署密码完成意愿） " + pwdAuth)

        # 完成意愿
        headers["sessionId"] = sessionId
        headers["host"] = cloud_login_url[7:]
        print("headers ", headers)
        # 需要事先设置密码为：bbbb1111
        body = {"password": "229a729b82ce06972795ed71c00f1dcb"}
        response = requests.post(pwdAuth, headers=headers, verify=False, json=body)
        print("完成意愿 ", response.text)
        if response.json().get("code") == 1400400:
            # 设置密码
            cloud_pwdReset_sign(mobile)
            sleep(1)
            # 再次意愿
            response = requests.post(pwdAuth, headers=headers, verify=False, json=body)
        willAuthId = response.json().get("data").get("willAuthId")
        print("操作完成意愿成功-willAuthId: " + willAuthId + "; 认证授权流程ID-" + authFlowId)

        # 完成意愿后，调用 /v3/pre/oauth/{authFlowId}/auth-flow-process ， 更新授权状态
        auth_flow_process_url = cloud_login_url + "/webserver/v3/pre/oauth/" + authFlowId + "/auth-flow-process?&authFlowId=" + authFlowId

        authFlowProcessRes = requests.get(auth_flow_process_url, headers=web_login_headers, verify=False)
        print("更新授权状态: " + authFlowProcessRes.text)
    except Exception as ex:
        print(ex)


# 公有云saas重置登录密码：abc_123456
# 前置条件：手机号一定要在运营支持平台上mock过
def cloud_pwdReset_login(mobile):
    try:
        headers = cloud_gen_headers_webservice('')
        url1 = cloud_login_url + "/account-webserver/sender/robotAuth/apply/v3"
        body1 = {"principal": mobile, "client_type": "web"}
        response1 = requests.post(url1, headers=headers, verify=False, json=body1)
        resJson1 = json.loads(response1.text)
        print("发送重置登录密码请求: ", resJson1)
        challenge = resJson1.get('data').get('challenge')
        gt = resJson1.get('data').get('gt')

        url2 = cloud_login_url + "/account-webserver/sender/mobile/v3"
        body2 = {"bizType": "RESET_PWD", "preServiceId": "", "principal": mobile,
                 "auth": {"geetest_challenge": challenge, "geetest_validate": "e205c71963de01fd575fbbe7ca6fa34e",
                          "geetest_seccode": "e205c71963de01fd575fbbe7ca6fa34e|jordan"}}
        response2 = requests.post(url2, headers=headers, verify=False, json=body2)
        resJson2 = json.loads(response2.text)
        print("发送短信验证码: ", resJson2)
        serviceId = resJson2.get('data').get('serviceId')

        url3 = cloud_login_url + "/account-webserver/sender/verify"
        body3 = {"credentials": "123456", "serviceId": serviceId}
        response3 = requests.post(url3, headers=headers, verify=False, json=body3)
        resJson3 = json.loads(response3.text)
        print("验证码校验: ", resJson3)
        serviceId = resJson3.get('data').get('serviceId')

        url4 = cloud_login_url + "/account-webserver/sender/pwdReset"
        # 密码固定设置为：abc_123456
        body4 = {"encrypter": mobile, "newPassword": "593f53728678566930be2a982efc5dda", "serviceId": serviceId}
        response4 = requests.post(url4, headers=headers, verify=False, json=body4)
        resJson4 = json.loads(response4.text)
        print("修改密码（与原来密码不能一致）: ", resJson4)
        code = resJson4.get('code')
    except Exception as ex:
        print(ex)


# 取消授权信息
def cloud_cancel_auth(mobile):
    try:
        headers = cloud_gen_headers_webservice(mobile)

        url2 = cloud_login_url + "/webserver/v3/oauth/cancel-auth"
        req_json2 = {"appId": CLOUD_APPID}
        response2 = requests.post(url2, headers=headers, verify=False, json=req_json2)
        resJson2 = json.loads(response2.text)
        print("取消授权操作: ", resJson2)
        return resJson2.get('code')
    except Exception as ex:
        print(ex)


# 公有云saas重置签署密码：bbbb1111
def cloud_pwdReset_sign(mobile):
    try:
        headers = cloud_gen_headers_webservice(mobile)
        headers['X-Tsign-Open-Tenant-Id'] = "fd29fa1b680e42bbbcbe85531203fa7f"
        headers['X-Tsign-Client-Id'] = "WEB"
        headers['X-Tsign-Client-AppName'] = "web-treaty-front_2.0"
        url1 = cloud_login_url + "/webserver/v1/sender/sys_accountId/originAuthApply"
        body1 = {"bizType": "RESET_SIGN_PWD", "password": "229a729b82ce06972795ed71c00f1dcb"}
        response1 = requests.post(url1, headers=headers, verify=False, json=body1)
        resJson1 = json.loads(response1.text)
        print("发送重置签署密码请求: ", resJson1)
        if resJson1.get('code') == 1580022:
            body1 = {"bizType": "RESET_SIGN_PWD", "password": None}
            response1 = requests.post(url1, headers=headers, verify=False, json=body1)
            resJson1 = json.loads(response1.text)
            print("二次重置签署密码请求: ", resJson1)
        serviceId = resJson1.get('data').get('serviceId')

        url2 = cloud_login_url + "/webserver/sender/signPwdReset"
        body2 = {"serviceId": serviceId, "password": "229a729b82ce06972795ed71c00f1dcb"}
        response2 = requests.post(url2, headers=headers, verify=False, json=body2)
        resJson2 = json.loads(response2.text)
        print("设置签署密码（bbbb1111）: ", resJson2)
    except Exception as ex:
        print(ex)


def get_tuple_str(tuple, index):
    if tuple:
        return tuple[index]
    else:
        return None

def getInnerSeals(userType, params: dict, isMutil):
    """
    查询内部用户或企业的可用印章信息
    :param userType: 1-代表内部个人；2-代表内部企业
    :param params: e.g. {"organizationCode":"xx","userCode":"xx","sealPattern":"xx","sealTypeCode":"xxx"}
    :param isMutil: 是否返回多个 0-返回一个string ; 1-返回多个逗号分割; 2-9返回几个的意思,不允许超过10个
    :return:
    """
    if "sealPattern" not in params.keys():
        sealPattern = 1
    else:
        sealPattern = params.get('sealPattern')
    if userType == 1:
        from utils.esign6Seals import listUserseals
        obj1 =  listUserseals(params.get('userCode'),sealPattern)
    else:
        from utils.esign6Seals import listOrganizationSeals
        if "sealTypeCode" not in params.keys():
            sealTypeCode = ""
        else:
            sealTypeCode = params.get('sealTypeCode')
        obj1 =  listOrganizationSeals(params.get('organizationCode'),params.get('userCode'),sealPattern, sealTypeCode)
    seals = obj1.get('data').get('records')
    if seals:
        sealStr = seals[0].get('sealId')
        if isMutil != 0:
            if isMutil > obj1.get('data').get('total'):
                isMutil = obj1.get('data').get('total')
            for index in range(isMutil):
                print('xxxxxxxx====',index)
                sealId = seals[index].get('sealId')
                sealStr = sealStr + "," + sealId
        return sealStr
    else:
        return ENV('sign01.sealId')

#######从文档迁移过来的#############

def get_main_token(account=None, password=None):
    return getPortalToken(account, password)


def get_sha256(data, key):
    key = key.encode('utf-8')  # sha256加密的key
    if isinstance(data, dict):
        data = json.dumps(data)
    message = data.encode('utf-8')  # 待sha256加密的内容
    sign = hmac.new(key, message, digestmod=sha256).hexdigest()
    return sign


# openapi接口的headers
def gen_openapi_post_headers():
    headers = {"Content-Type": "application/json"}
    return headers


# 根据openapi是否走网关返回对应的header
def gen_openapi_post_headers_getway(body):
    if gateway_on == 'True':
        headers = {"Content-Type": "application/json", "x-timevale-project-id": PROJECT_ID,
                   "x-timevale-signature": get_sha256(body, PROJECT_SECRET)}
    else:
        headers = {"Content-Type": "application/json"}
    return headers

def test_1024(body=None):
    print("i love 天印")
    return None

# 管理平台的headers
def gen_manage_headers():
    headers = {"Content-Type": "application/json", "token": MANAGE_TOKEN}
    return headers


def gen_main_headers(navIdKey=None):
    """
    统一登录平台的headers
    :param navIdKey:
    :return: dict
    """
    headers = {"Content-Type": "application/json",
               "authorization": get_main_token(),
               "X-timevale-project-id": PROJECT_ID,
               "language": "zh-CN"
               }
    if navIdKey:
        headers['navId'] = ENV(navIdKey)
    return headers


def gen_main_headers_navId(navid_key):
    """
    弃用，统一使用 gen_main_headers
    """
    navId = ENV(navid_key)
    headers = {"Content-Type": "application/json", "authorization": get_main_token(),
               "X-timevale-project-id": PROJECT_ID, "navId": navId}
    return headers

def gen_file_main_headers():
    headers = {"authorization": get_main_token(), "X-timevale-project-id": PROJECT_ID}
    return headers


def gen_main_headers_for_urlencoded():
    headers = {"Content-Type": "application/x-www-form-urlencoded", "authorization": get_main_token(),
               "X-timevale-project-id": PROJECT_ID}
    return headers


def gen_main_headers_for_user(account, password, navid_key=None):
    """
    弃用，使用 gen_main_headers
    """
    token = get_main_token(account, password)
    if navid_key is None:
        headers = {"Content-Type": "application/json", "authorization": token,
                   "X-timevale-project-id": PROJECT_ID}
    else:
        navId = ENV(navid_key)
        headers = {"Content-Type": "application/json", "authorization": token,
                   "X-timevale-project-id": PROJECT_ID, "navId": navId}
    return headers

"""
有的接口需要内部用户或外部用户登录都可以
1、传account, password，视为内部用户登录
2、传加密的phone Or Mail，视为外部用户登录
"""
def gen_main_headers_for_interirl_or_external(account, password, phoneOrMail):
    try:
        if phoneOrMail:
            return gen_main_headers_for_ExternalUser(phoneOrMail)
        else:
            return gen_main_headers()
    except:
        print("执行gen_main_headers_for_interirl_or_external获取用户登录信息失败")


def gen_main_headers_for_ExternalUser(phoneOrMail):
    phoneOrMail_value = ENV(phoneOrMail)
    from tools.token import create_outerSigner_token2
    out_token = create_outerSigner_token2(phoneOrMail_value)
    headers = {"Content-Type": "application/json", "authorization": out_token,
               "X-timevale-project-id": PROJECT_ID}
    return headers

def get_value(data, key):
    """
    获取字典中某个字段的值
    :param data: 字典数据
    :param key: 键
    :return: 键对应的值
    """
    print("getvalue:data:key{} {}".format(data, key))
    if isinstance(key, int):
        # key是数字的时候data是列表，取下标的值
        return data[int(key)]
    return data[key]

# 如果执行路径不是在tests中
if not cur_dir.endswith("tests"):
    cur_dir = cur_dir + sep + "tests" + sep


# case运行后清理自动化测试产生的内容域,缺省调用方式：$(teardown_contentDomain_delete())
def teardown_contentDomain_delete():
    print("开始清理自动化测试产生的内容域数据......")
    headers = gen_main_headers()
    query_data = {"params": {"page": 1, "size": 200, "contentName": autotest_name_prefix}}
    response = requests.post(url=MAIN_HOST + '/esign-docs/content_domain/list',
                             json=query_data, headers=headers)
    json_response = response.json()
    assert (json_response['status'] == 200)
    domain_list = json_response['data']['list']
    for domain in domain_list:
        domainId = domain['domainId']
        data2 = {"params": {"domianId": domainId}}
        requests.post(url=MAIN_HOST + '/esign-docs/content_domain/delete', json=data2, headers=headers)
    print("自动化测试产生的内容域数据清理完毕！")


# case运行后清理自动化测试产生的企业模板,缺省调用方式：$(teardown_template_delete())
def teardown_template_delete():
    print("开始清理自动化测试产生的企业模板数据......")
    headers = gen_main_headers_navId("template_manage_navId")
    query_data = {"params": {"page": 1, "size": 500, "templateName": autotest_name_prefix}}
    response = requests.post(url=MAIN_HOST + '/esign-docs/template/manage/list', json=query_data, headers=headers)
    json_response = response.json()
    assert (json_response['status'] == 200)
    if json_response['data']['total'] > 0:
        template_list = json_response['data']['list']
        for template in template_list:
            templateUuid = template['templateUuid']
            version = template['version']
            # 停用
            suspend_data = {"params": {"templateUuid": templateUuid, "version": version}}
            requests.post(url=MAIN_HOST + '/esign-docs/template/manage/suspend', json=suspend_data, headers=headers)
            data2 = {"params": {"templateUuid": templateUuid}}
            requests.post(url=MAIN_HOST + '/esign-docs/template/manage/del', json=data2, headers=headers)
    print("自动化测试产生的企业模板数据清理完毕！")


def teardown_template_delete1():
    print("开始清理自动化测试产生的企业模板数据......")
    headers = gen_main_headers_navId("template_manage_navId")
    query_data = {"params": {"page": 1, "size": 500, "templateName": "五十"}}
    response = requests.post(url=MAIN_HOST + '/esign-docs/template/manage/list', json=query_data, headers=headers)
    json_response = response.json()
    assert (json_response['status'] == 200)
    if json_response['data']['total'] > 0:
        template_list = json_response['data']['list']
        for template in template_list:
            templateUuid = template['templateUuid']
            version = template['version']
            # 停用
            suspend_data = {"params": {"templateUuid": templateUuid, "version": version}}
            requests.post(url=MAIN_HOST + '/esign-docs/template/manage/suspend', json=suspend_data, headers=headers)
            data2 = {"params": {"templateUuid": templateUuid}}
            requests.post(url=MAIN_HOST + '/esign-docs/template/manage/del', json=data2, headers=headers)
    print("自动化测试产生的企业模板数据清理完毕！")


# case运行后清理自动化测试产生的文件类型和文件夹,缺省调用方式：$(teardown_docConfigure_delete())
def teardown_docConfigure_delete():
    print("开始清理自动化测试产生的文件配置数据......")
    docConfig_type_response = search_docConfig_list(autotest_name_prefix, 2)
    assert (docConfig_type_response['status'] == 200)
    docConfig_type_list = docConfig_type_response['data']
    for docConfig_type in docConfig_type_list:
        docUuid = docConfig_type['docUuid']
        can_delete_json1 = {"params": {"docType": 2, "docUuid": docUuid}}
        type_can_delete_response = requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/docCanDeleted',
                                                 json=can_delete_json1,
                                                 headers=gen_main_headers())
        assert (type_can_delete_response.json()['status'] == 200)
        type_can_delete = type_can_delete_response.json()['data']['canDeleted']
        # 先删文件类型
        if type_can_delete == 1:
            requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/deleteDocConfigure/' + docUuid,
                          headers=gen_main_headers())
    docConfig_folder_response = search_docConfig_list(autotest_name_prefix, 1)
    assert (docConfig_folder_response['status'] == 200)
    docConfig_folder_list = docConfig_folder_response['data']
    for docConfig_folder in docConfig_folder_list:
        docUuid = docConfig_folder['docUuid']
        can_delete_json2 = {"params": {"docType": 1, "docUuid": docUuid}}
        folder_can_delete_response = requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/docCanDeleted',
                                                   json=can_delete_json2,
                                                   headers=gen_main_headers())
        assert (folder_can_delete_response.json()['status'] == 200)
        folder_can_delete = folder_can_delete_response.json()['data']['canDeleted']
        # 再删除文件夹
        if folder_can_delete == 1:
            res = requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/deleteDocConfigure/' + docUuid,
                                headers=gen_main_headers())
    print("自动化测试产生的文件配置数据清理完毕！")

def pageOutsideOrganizationList(organization_name, account_number, curr_page, page_size):
    query_data = {"params": {"currPage": curr_page, "pageSize": page_size, "organizationName": organization_name,
                             "accountNumber": account_number}, "domain": "admin_platform"}
    headers = gen_manage_headers()
    response = requests.post(url=MAIN_HOST + '/manage/orguser/org/pageOutsideOrganizationList',
                             json=query_data, headers=headers)
    json_response = response.json()
    assert (json_response['status'] == 200)
    org_list = json_response['data']['list']
    total_page = json_response['data']['totalPage']
    return org_list, total_page


def allOutsideOrganizationList(organization_name, account_number):
    """
    所有外部组织
    返回格式：[{
                "id": "1706187209304707073",
                "parentOrganizationId": "1523545062837981184",
                "organizationName": "自动化测试新建外部组织用户特殊字段",
                "organizationCode": "a4db73e877d149568fa796e9393c79c6",
                "accountNumber": "ZDHCSXJWBZZYHTSZD",
                "organizationStatus": "1",
                "realnameStatus": "0",
                "createTime": "2023-09-25 14:01:44"
            }]
    """
    org_list_all = []
    org_list_result = pageOutsideOrganizationList("自动化测试", "", 1, 100)
    org_list1 = org_list_result[0]
    org_list_all = org_list_all + org_list1
    total_page = org_list_result[1]
    print("产生的自动化测试外部组织条数：", total_page)
    while total_page > 1:
        org_list_result = pageOutsideOrganizationList("自动化测试", "", total_page, 100)
        org_list_index = org_list_result[0]
        org_list_all = org_list_all + org_list_index
        total_page = total_page - 1
    return org_list_all

def teardown_delete_autotest_outer_organizations():
    """
    批量删除外部组织，用于删除自动化测试产生的外部组织数据，组织名称包含"自动化测试"
    """
    print("开始清理自动化测试产生的外部组织数据......")
    org_list_all = allOutsideOrganizationList("自动化测试批量", None)
    if len(org_list_all) > 0:
        for org in org_list_all:
            organization_code = org["organizationCode"]
            delete_outer_organizations(None, organization_code)
    print("外部组织清理完成.....")

# case运行后清理自动化测试产生的内容域、文件配置、企业模板，缺省调用方式：$(teardown_delete())
def teardown_delete():
    teardown_contentDomain_delete()
    teardown_template_delete()
    teardown_template_delete1()
    teardown_docConfigure_delete()
    teardown_delete_autotest_outer_organizations()


def teardown_write_env(key, value):
    print("写入env文件")
    print(value)
    os.putenv(key, value)

# 获取随机数
def get_randomNo():
    return random.randint(100000, 999999)


def get_randomNo_16():
    return random.randint(1000000000000000, 9999999999999999)


def get_randomNo_32():
    return random.randint(10000000000000000000000000000000, 99999999999999999999999999999999)


def get_randomNo_36():
    return random.randint(100000000000000000000000000000000000, 999999999999999999999999999999999999)

def get_randomStr_32():
    """
    生成32位随机数
    """
    return str(get_randomNo_32())

def random_str(slen=32):
    seed = "*********0abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    sa = []
    for i in range(slen):
        sa.append(random.choice(seed))
    return ''.join(sa)


def random_letter(slen=32):
    seed = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
    sa = []
    for i in range(slen):
        sa.append(random.choice(seed))
    return ''.join(sa)

def get_timestamp_13(var):
    timestamp = int(round(time.time() * 1000))
    return timestamp + int(var)


def hook_sleep_n_secs(n_secs):
    time.sleep(n_secs)


# 断言 - 看返回结果是否包含某个value
def containItems(results, value, key):
    contain = False
    for i in range(len(results)):
        if results[i][key] == value:
            contain = True
    return contain


# 断言 - 看返回结果是否包含某个key
def isContainKey(results, key):
    tmp = results
    ret = False
    for k, v in tmp.items():
        if k == key:
            ret = True
            break
        else:
            if type(v) == type(results):
                ret = isContainKey(v, key)
                if ret:
                    ret = True
                    break
            else:
                continue
    return ret


# 断言判定
def results_isExpected(resulets, conditionKey, conditionValue, expectedKey, expectedValue):
    contain = False
    for expectedDic in reversed(resulets):
        if expectedDic[conditionKey] == conditionValue:
            if expectedDic[expectedKey] == expectedValue:
                contain = True
    return contain


def get_randomString():
    salt = ''.join(random.sample(string.ascii_letters + string.digits, 20))
    return salt


def get_timestamp():
    import time
    return str(int(time.time() * 1000))


def toString(content):
    return str(content)


# 通过json文件存储数据，在debugtalk中添加以下函数，yml文件中调用  ${get_config(name)} ，即可获取name对应的值
def get_config(config_key):
    current_path = os.path.abspath(__file__)
    config_file_path = os.path.join(os.path.dirname(current_path), 'config')
    with open(config_file_path, 'r', encoding='UTF-8') as f:
        config_dict = json.load(f)
    key = config_dict[config_key]
    return key


def set_config(key, value):
    print("写入config文件", key, ":", value)
    current_path = os.path.abspath(__file__)
    config_file_path = os.path.join(os.path.dirname(current_path), 'config')
    with open(config_file_path, 'r', encoding="utf-8") as f:
        json_data = json.load(f)
        json_data[key] = value
    with open(config_file_path, 'w', encoding='utf-8') as fw:
        json.dump(json_data, fw, indent=4, ensure_ascii=False)


# 全部参数转为字符串，包含则返回True
def contain_keys(data, key):
    temp = str(data)
    if str(key) in temp:
        return True
    return False


def substring(temp: str, index1: int, index2: int):
    """
    截取字符串temp,下标index1开始取到index2，顾头不顾尾，所有不包含index2
    """
    return temp[index1:index2]

# 截取返回值中的某段
def get_substr(mystr: str,startStr: str,endChar: str):
    startStr_len=len(startStr)
    start_index = mystr.index(startStr)
    end_index = mystr.index(endChar)
    return mystr[start_index+startStr_len:end_index]

def get_user_code(account, password):
    data = {
        "platform": "pc",
        "referer": "",
        "account": ENV(account),
        "password": ENV(password),
        "type": 1
    }
    headers = {
        "Content-Type": "application/json;charset=UTF-8",
        "x-timevale-project-id": PROJECT_ID
    }
    response = requests.post(url=MAIN_HOST + '/sso/accountLogin', json=data, headers=headers)
    jsonResponse = response.json()
    userCode = jsonResponse['data']['userCode']
    return userCode


'''
方法说明：通过管理平台的openapi获取内部用户信息
（其他获取用户信息、用户编码的方法可删除以此下2方法替代get_innerUser_detail、get_inner_UserCode）
请求参数：
userName：用户姓名
userCode：用户编码
userAccount：用户账号
mobile：用户手机号
返回信息示例（response.json()["data"]["successData"]）：
"userCode": "ceswdzxzdhyhwgd1", 
"customAccountNo": "ceswdzxzdhyhwgd1", 
"name": "测试文档中心自动化用户勿改动", 
"email": "<EMAIL>", 
"mobile": "***********", 
"licenseType": "ID_CARD", 
"licenseNo": "", 
"bankCardNo": "", 
"otherOrganization": [ ], 
"mainCustomOrgNo": "ESIGNWDZXZDHCSGSWGD", 
"mainOrganizationCode": "6ac62c5fb7664cdf95b89469dab8af2e"
'''


def get_innerUser_detail(userName=None, userCode=None, userAccount=None, mobile=None, licenseNo=None):
    data = {
        "userCode": userCode,
        "customAccountNo": userAccount,
        "name": userName,
        "mobile": mobile,
        "licenseNo": licenseNo
    }

    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerUsers/detail', json=data,
                             headers=gen_openapi_post_headers_getway(data))
    jsonResponse = response.json()
    assert jsonResponse['code'] == 200

    return jsonResponse["data"]


'''
获取内部用户编码-精确搜索第一个用户
(最新，其他的方法检查无调用后可以删除，或替换为此方法)
'''


def get_inner_UserCode(userName=None, userCode=None, userAccount=None, mobile=None, licenseNo=None):
    userDetailList = get_innerUser_detail(userName, userCode, userAccount, mobile, licenseNo)
    thisUserCode = userDetailList[0]['userCode']
    return thisUserCode


def get_inner_userOrg(userName=None, userCode=None, userAccount=None, mobile=None, licenseNo=None):
    userDetailList = get_innerUser_detail(userName, userCode, userAccount, mobile, licenseNo)
    thisUserOrgCode = userDetailList[0]['mainOrganizationCode']
    print(thisUserOrgCode)
    return thisUserOrgCode


'''
方法说明：通过管理平台的openapi获取外部用户信息
(最新，其他的方法检查无调用后可以删除，或替换为此方法)
'''


def get_outerUser_detail(userName=None, userCode=None, userAccount=None, mobile=None, licenseNo=None):
    data = {
        "userCode": userCode,
        "customAccountNo": userAccount,
        "name": userName,
        "mobile": mobile,
        "licenseNo": licenseNo
    }
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/outerUsers/detail', json=data,
                             headers=gen_openapi_post_headers_getway(data))
    jsonResponse = response.json()
    assert jsonResponse['code'] == 200
    return jsonResponse["data"]


'''
获取外部用户编码-精确搜索第一个用户
(最新，其他的方法检查无调用后可以删除，或替换为此方法)
'''


def get_orgInfo(name=None, organizationCode=None):
    data = {
        "name": name,
        "organizationCode": organizationCode
    }
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerOrganizations/detail', json=data,
                             headers=gen_openapi_post_headers_getway(data))
    jsonResponse = response.json()
    if name == "None":
        return jsonResponse["data"][0]["organizationCode"]
    else:
        return jsonResponse["data"][0]["name"]


def get_outer_UserCode(userName=None, userCode=None, userAccount=None, mobile=None, licenseNo=None):
    userDetailList = get_outerUser_detail(userName, userCode, userAccount, mobile, licenseNo)
    thisUserCode = userDetailList[0]['userCode']
    return thisUserCode


def get_outer_orgId_by_userName():
    userCodeOut = ENV("wsignwb01.userCode")
    userDetailList = get_outerUser_detail(None, userCodeOut, None, None, None)
    thisUserCode = userDetailList[0]['userCode']
    return thisUserCode


def get_outer_user_code(outer_user_name):
    data = {
        "params":
            {
                "userName": outer_user_name, "organizationName": "", "pageSize": 10, "currPage": 1
            },
        "domain": "admin_platform"
    }
    try:
        headers = gen_manage_headers()
        response = requests.post(url=MAIN_HOST + '/manage/orguser/user/pageOutsideUserList', json=data, headers=headers)
        jsonResponse = response.json()
        userCode = jsonResponse['data']['list'][0]['userCode']
        return userCode
    except:
        raise Exception("get_outer_user_code获取失败")


def checkBusinessContent(contents: list):
    existDatasource = False
    notExistDatasource = False

    for content in contents:
        if content["contentSource"] == 1:
            existDatasource = True
        if content["contentSource"] != 1:
            notExistDatasource = True

    return existDatasource and notExistDatasource


# 插入文件类型1
def insert_doc_data1(docName, docCode):
    data1 = {"params": {
        "docName": docName,
        "docType": "2",
        "docCode": docCode
    }}
    headers = gen_main_headers()
    requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/addDocConfigure', json=data1, headers=headers)


# 多条插入文件类型7
def insert_doc_data7():
    data7 = {"params": {
        "docName": "0402自动化测试延平文件类型多条1",
        "docType": "2",
        "docCode": "zdhcswjlx0402dt1"
    }}
    data8 = {"params": {
        "docName": "0402自动化测试延平文件类型多条2",
        "docType": "2",
        "docCode": "zdhcswjlx0402dt2"
    }}
    data9 = {"params": {
        "docName": "0402自动化测试延平文件类型多条3",
        "docType": "2",
        "docCode": "zdhcswjlx0402dt3"
    }}
    data10 = {"params": {
        "docName": "0402自动化测试延平文件类型多条4",
        "docType": "2",
        "docCode": "zdhcswjlx0402dt4"
    }}
    data11 = {"params": {
        "docName": "0402自动化测试延平文件类型多条5",
        "docType": "2",
        "docCode": "zdhcswjlx0402dt5"
    }}
    headers = gen_main_headers()
    requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/addDocConfigure', json=data7, headers=headers)
    requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/addDocConfigure', json=data8, headers=headers)
    requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/addDocConfigure', json=data9, headers=headers)
    requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/addDocConfigure', json=data10, headers=headers)
    requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/addDocConfigure', json=data11, headers=headers)


# 批量删除文件配置测试数据
def deleteDocTestData():
    headers = gen_main_headers()
    data1 = {"params": {
        "docName": "自动化测试",
        "docType": ""
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/getDocConfigureListWithoutPermission',
                             json=data1, headers=headers)
    jsonResponse = response.json()
    docList = jsonResponse['data']
    for doc in docList:
        docUid = doc['docUuid']
        requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/deleteDocConfigure/' + docUid, headers=headers)


"""
文件/采集文件/附件上传，返回filekey
asSignFile:是否作为签署文件
"""

def doc_upload(asSignFile=False, fileName=''):
    url = MAIN_HOST + '/esign-docs/fileSystem/docUpload'
    data = {
        "fileName": fileName,
        "asSignFile": asSignFile
    }
    file = {'uploadFile': getFileForUpload(fileName)}
    response = requests.post(url=url, files=file, headers=gen_file_main_headers(), data=data)
    try:
        jsonResponse = response.json()
        fileKey = jsonResponse['data']['fileKey']
        return fileKey
    except:
        raise Exception("doc_upload方法获取fileKey失败，file_name={},response={}".format(fileName, response.text))


def check_outer_user_detail(data, except_value):
    """
    自定义校验器：校验内部用户详情字段
    :param data: 实际返回值
    :param except_value: 预期值
    """
    assert data in except_value or data == except_value

def check_outer_organitions_detail(data, except_value):
    """
    自定义校验器：校验外部组织详情字段
    :param data: 返回的字段值
    :param except_value: 期望的字段值
    :return:
    """
    if isinstance(except_value, int):
        except_value = str(except_value)
    assert data in except_value or data == except_value

def attachment_upload(file_name: str):
    url = MAIN_HOST + '/esign-docs/fileSystem/attachmentUpload'

    file = {'uploadFile': getFileForUpload(file_name)}

    response = requests.post(url=url, files=file, headers=gen_file_main_headers())
    jsonResponse = response.json()
    fileKey = jsonResponse['data']['fileKey']
    print(fileKey)
    return fileKey


def open_file(file_name):
    file_name_path = os.path.basename(file_name)
    file_path = os.path.join('data', file_name_path)
    file_path2 = os.path.join(cur_dir, file_path)
    print(file_path2)
    fd = open(file_path2, 'rb')
    return fd


# 用户机构列表是否返回了所属兼职组织:依据/esign-docs/user/getUserOrg接口返回的orgList
def getUserOrgTypeOfOrganization(orgList):
    organizationList = []
    for org in orgList:
        if org['userMainType'] == "2":
            organizationList.append(org)
    if len(organizationList) >= 1:
        return 2
    else:
        return -1


# 用户机构列表是否返回了所属组织:依据/esign-docs/user/getUserOrg接口返回的orgList
def getUserOrgTypeOfCompany(orgList):
    companyList = []
    for org in orgList:
        if org['userMainType'] == "1":
            companyList.append(org)
    if len(companyList) >= 1:
        return 1
    else:
        return -1




# 新建模板_合同1
def insert_template_data_contract1():
    data = {"params": {
        "fileKey": pdf_filekey,
        "templateName": "自动化测试 带合同字段模版",
        "createUserOrg": "testCreateUserOrg",
        "docUuid": get_a_docConfig_type(),
        "allRange": 1
    }}
    headers = gen_main_headers()
    requests.post(url=MAIN_HOST + '/esign-docs/template/add', json=data, headers=headers)
    queryData = {"params": {
        "templateName": "自动化测试 带合同字段模版",
        "page": 1,
        "size": 10
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/template/list', json=queryData, headers=headers)
    jsonResponse = response.json()
    templateUuid = jsonResponse['data']['list'][0]['templateUuid']
    version = jsonResponse['data']['list'][0]['version']
    publishData = {"params": {
        "templateUuid": templateUuid,
        "version": version,
        "isPublish": "false"
    }}
    requests.post(url=MAIN_HOST + '/esign-docs/template/saveOrPublish', json=publishData, headers=headers)


def insert_template_data_contract2():
    data = {"params": {
        "fileKey": pdf_filekey,
        "templateName": "自动化测试 带合 同字段模版",
        "createUserOrg": "testCreateUserOrg",
        "docUuid": get_a_docConfig_type(),
        "allRange": 1
    }}
    headers = gen_main_headers()
    requests.post(url=MAIN_HOST + '/esign-docs/template/add', json=data, headers=headers)
    queryData = {"params": {
        "templateName": "自动化测试 带合 同字段模版",
        "page": 1,
        "size": 10
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/template/list', json=queryData, headers=headers)
    jsonResponse = response.json()
    templateUuid = jsonResponse['data']['list'][0]['templateUuid']
    version = jsonResponse['data']['list'][0]['version']
    publishData = {"params": {
        "templateUuid": templateUuid,
        "version": version,
        "isPublish": "false"
    }}
    requests.post(url=MAIN_HOST + '/esign-docs/template/saveOrPublish', json=publishData, headers=headers)


def insert_template_data_publish():
    data = {"params": {
        "fileKey": pdf_filekey,
        "templateName": "自动化测试 带合同字段模版",
        "createUserOrg": "testCreateUserOrg",
        "docUuid": get_a_docConfig_type(),
        "allRange": 1
    }}
    headers = gen_main_headers()
    requests.post(url=MAIN_HOST + '/esign-docs/template/add', json=data, headers=headers)
    queryData = {"params": {
        "templateName": "自动化测试 带合同字段模版",
        "page": 1,
        "size": 10
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/template/list', json=queryData, headers=headers)
    jsonResponse = response.json()
    templateUuid = jsonResponse['data']['list'][0]['templateUuid']
    version = jsonResponse['data']['list'][0]['version']
    publishData = {"params": {
        "templateUuid": templateUuid,
        "version": version,
        "isPublish": "true"
    }}
    requests.post(url=MAIN_HOST + '/esign-docs/template/saveOrPublish', json=publishData, headers=headers)

# 获取内部用户的主职机构
def getUserMainOrg(account):
    headers = gen_main_headers()
    userJson = get_innerUser_detail(None, account, None, None)
    userName = userJson[0]["name"]
    data1 = {"params": {"userName": userName, "organizationTerritory": 1}}
    response = requests.post(url=MAIN_HOST + '/esign-docs/user/getUserListByUserCodeName',
                             json=data1, headers=headers)
    jsonResponse = response.json()
    userList = jsonResponse['data']['userList']
    for user in userList:
        userMainType = user['userMainType']
        if userMainType == "1":
            return user['organizationCode']


# 获取下一环节配置编码
def getNextNodeCode(processInstanceId, batchTemplateInitiationUuid):
    headers = gen_main_headers()

    data1 = {"params": {"processInstanceId": processInstanceId}}
    response = requests.post(url=MAIN_HOST + '/esign-docs/flow/manage/getWorkflowInstanceHandleInfo',
                             json=data1, headers=headers)

    jsonResponse = response.json()
    nextNodeConfig = jsonResponse['data']['nextNodeConfigList'][0]
    nodeClassType = nextNodeConfig['nodeClassType']
    if nodeClassType != "11":
        return nextNodeConfig['nodeConfigCode']
    branchNodeCode = nextNodeConfig['nodeConfigCode']
    data2 = {"params": {"nodeConfigCode": branchNodeCode, "processDefinitionKey": processDefinitionKey,
                        "processInstanceId": processInstanceId}}
    response2 = requests.post(url=MAIN_HOST + '/esign-docs/flow/manage/getExclusiveGatewayNextConfig',
                              json=data2, headers=headers)
    jsonResponse2 = response2.json()
    nextNodeList = jsonResponse2['data']

    data3 = {"params": {"batchTemplateInitiationUuid": batchTemplateInitiationUuid}}
    response3 = requests.post(url=MAIN_HOST + '/esign-docs/flow/batchSign/isNeedAudit',
                              json=data3, headers=headers)
    jsonResponse3 = response3.json()
    isNeedAudit = jsonResponse3['data']
    for nextNode in nextNodeList:

        if (not isNeedAudit) and nextNode['conditionExpression'].find("{!whetherNeedAudit}") != -1:
            return nextNode['nodeConfigCode']
        if isNeedAudit and nextNode['conditionExpression'].find("{whetherNeedAudit}") != -1:
            return nextNode['nodeConfigCode']


# 获取relateUuid
def getRelateUuid(account, password):
    headers = gen_main_headers_for_user(account, password)

    data1 = {"domain": "unified_portal_service", "params": {"currPage": 1, "pageSize": 5}}
    response = requests.post(url=MAIN_HOST + '/portal/task/queryUndoTask',
                             json=data1, headers=headers)
    jsonResponse = response.json()
    workflowRequestUrl = jsonResponse['data']['list'][0]['workflowRequestUrl']
    relateUuid = workflowRequestUrl.split("relateUuid=")[1]
    return relateUuid

def getRequestUrl(batchTemplateInitiationUuid):
    return "https://tianyin6-stable.tsign.cn/doc-manage-web/home-workFlow?workflowId=undefined&bussinessId=&noWorkflowCodePageName=WLYYSQ&batchTemplateInitiationUuid=" + batchTemplateInitiationUuid



def addSpace(str):
    return " " + str + " "


def beforeAddSpace(str):
    return " " + str


def afterAddSpace(str):
    return str + " "


# 至少2个字符长度才可使用
def middleAddSpace(str):
    return str[0] + " " + str[1:]


# 连接数据库进行查询
def connectDB(sqlLanguage):
    host = ENV('esign.dbHost')
    port = ENV('esign.dbPort')
    user = ENV('esign.dbUser')
    password = ENV('esign.dbPassword')
    db = ENV('esign.db.docs')
    # 参数分别为：ip，用户名，密码，库名
    db = pymysql.connect(host=host, port=int(port), user=user, password=password, db=db)
    cursor = db.cursor()
    cursor.execute(sqlLanguage)
    result = cursor.fetchone()
    return result


# 批量发起-断言对比-找到匹配的内部企业与用户
def assertBatchTemplateInitiationUuid1(sql):
    time.sleep(1)
    result = connectDB(sql)
    handleStatus = result[0]
    return handleStatus


# 批量发起-断言对比-没有找到匹配的内部企业与用户
def assertBatchTemplateInitiationUuid2(sql):
    time.sleep(1)
    result = connectDB(sql)
    handleResult = result[1]
    return handleResult


# 根据暂存任务id和excel内数据id获取执行结果
def getBatchTaskHandResult(batchTaskUuid, batchDataId):
    sql = "select a.handle_result from doc_template_initiation_data_related a " \
          "left join doc_batch_template_initiation b on a.batch_template_initiation_id  =  b.id " \
          "where b.uuid = '" + batchTaskUuid + "'and a.batch_data_id = '" + str(
        batchDataId) + "' order by a.gmt_create desc limit 1"
    result = connectDB(sql)
    handleResult = result[0]
    return handleResult


# 根据暂存任务id和excel内数据id获取执行状态
def getBatchTaskHandStatus(batchTaskUuid, batchDataId):
    sql = "select a.handle_status from doc_template_initiation_data_related a " \
          "left join doc_batch_template_initiation b on a.batch_template_initiation_id  =  b.id " \
          "where b.uuid = '" + batchTaskUuid + "'and a.batch_data_id = '" + str(
        batchDataId) + "' order by a.gmt_create desc limit 1"
    result = connectDB(sql)
    handleResult = result[0]
    return handleResult


def strToInt(str, inc):
    return int(str) + int(inc)


# 休眠
def sleep(n_secs):
    time.sleep(n_secs)


# 插入文件类型
def insert_doc_type():
    data1 = {"params": {
        "docName": "自动化测试文件类型1046",
        "docType": "2",
        "docCode": "zdhcswjlx1046"
    }}
    headers = gen_main_headers()
    requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/addDocConfigure', json=data1, headers=headers)
    data2 = {"params": {
        "docName": "自动化测试文件类型1046",
        "docType": "2"
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/getDocConfigureList',
                             json=data2, headers=headers)
    jsonResponse = response.json()
    docUuid = jsonResponse['data'][0]['docUuid']
    return docUuid


# 新增内容域
def addContentDomain(contentName, contentCode):
    data1 = {"params": {
        "domainId": "",
        "contentName": contentName,
        "contentCode": contentCode,
        "dataSource": "user",
        "formatType": 0,
        "required": 1,
        "description": "",
        "formatRule": "",
        "sourceField": "userName"
    }}
    headers = gen_main_headers()
    requests.post(url=MAIN_HOST + '/esign-docs/content_domain/add', json=data1, headers=headers)
    data2 = {"params": {
        "page": 1,
        "size": 1000,
        "contentName": "自动化测试-文本0507"
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/content_domain/list',
                             json=data2, headers=headers)

    jsonResponse = response.json()
    contentUuid = jsonResponse['data']['list'][0]['domainId']
    return contentUuid


# 删除内容域
def deleteContentDomainByContentName(contentName):
    headers = gen_main_headers()
    data1 = {"params": {"page": 1, "size": 1000, "contentName": contentName}}
    response = requests.post(url=MAIN_HOST + '/esign-docs/content_domain/list',
                             json=data1, headers=headers)
    jsonResponse = response.json()
    domainList = jsonResponse['data']['list']
    for domain in domainList:
        domainId = domain['domainId']
        data2 = {"params": {"domianId": domainId}}
        requests.post(url=MAIN_HOST + '/esign-docs/content_domain/delete', json=data2, headers=headers)


# 通用自动化测试文件夹
common_docName_folder = 'autotest通用文件夹1652153858625'
common_docName_folder_code = 'autotesttywjj1652153858625'
# 通用自动化测试文件类型
common_docName_type = 'autotest通用文件类型1652153858625'
common_docName_type_code = 'autotesttywjlx1652153858625'


def search_docConfig_list(doc_name, doc_type):
    data = {"params": {
        "docName": doc_name,
        "docType": doc_type
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/getDocConfigureList',
                             json=data, headers=gen_main_headers())
    json_response = response.json()
    return json_response


def add_docConfig(doc_name, doc_type, doc_code, parentUuid=""):
    data = {"params": {
        "docName": doc_name,
        "docType": doc_type,
        "docCode": doc_code,
        "parentUid": parentUuid
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/docConfigure/addDocConfigure',
                             json=data, headers=gen_main_headers())
    json_response = response.json()
    return json_response

def get_docConfig_type():
    """
    文档中心-文件类型配置
    获取一个自动化测试专用的文件类型，若该文件类型不存在，则新建一个后返回
    """
    headers = {"Content-Type": "application/json",
               "authorization": PORTAL_TOKEN,
               "X-timevale-project-id": PROJECT_ID}
    from utils.esign6Docs import get_a_docConfig_type
    return get_a_docConfig_type(headers)

# 获取一个通用自动化测试的文件类型，返回docUuid
#todo 可以写到autodata，少请求很多次接口
def get_a_docConfig_type():
    return get_docConfig_type()

docUuid = get_a_docConfig_type()

# 新建模板_合同1
def insert_template_data_contract1():
    data = {"params": {
        "fileKey": pdf_filekey,
        "templateName": "自动化测试 带合同字段模版",
        "createUserOrg": "testCreateUserOrg",
        "docUuid": docUuid,
        "allRange": 1
    }}
    headers = gen_main_headers()
    requests.post(url=MAIN_HOST + '/esign-docs/template/add', json=data, headers=headers)
    queryData = {"params": {
        "templateName": "自动化测试 带合同字段模版",
        "page": 1,
        "size": 10
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/template/list', json=queryData, headers=headers)
    jsonResponse = response.json()
    templateUuid = jsonResponse['data']['list'][0]['templateUuid']
    version = jsonResponse['data']['list'][0]['version']
    publishData = {"params": {
        "templateUuid": templateUuid,
        "version": version,
        "isPublish": "false"
    }}
    requests.post(url=MAIN_HOST + '/esign-docs/template/saveOrPublish', json=publishData, headers=headers)


def insert_template_data_contract2():
    data = {"params": {
        "fileKey": pdf_filekey,
        "templateName": "自动化测试 带合 同字段模版",
        "createUserOrg": "testCreateUserOrg",
        "docUuid": docUuid,
        "allRange": 1
    }}
    headers = gen_main_headers()
    requests.post(url=MAIN_HOST + '/esign-docs/template/add', json=data, headers=headers)
    queryData = {"params": {
        "templateName": "自动化测试 带合 同字段模版",
        "page": 1,
        "size": 10
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/template/list', json=queryData, headers=headers)
    jsonResponse = response.json()
    templateUuid = jsonResponse['data']['list'][0]['templateUuid']
    version = jsonResponse['data']['list'][0]['version']
    publishData = {"params": {
        "templateUuid": templateUuid,
        "version": version,
        "isPublish": "false"
    }}
    requests.post(url=MAIN_HOST + '/esign-docs/template/saveOrPublish', json=publishData, headers=headers)


def insert_template_data_publish():
    data = {"params": {
        "fileKey": pdf_filekey,
        "templateName": "自动化测试 带合同字段模版",
        "createUserOrg": "testCreateUserOrg",
        "docUuid": docUuid,
        "allRange": 1
    }}
    headers = gen_main_headers()
    requests.post(url=MAIN_HOST + '/esign-docs/template/add', json=data, headers=headers)
    queryData = {"params": {
        "templateName": "自动化测试 带合同字段模版",
        "page": 1,
        "size": 10
    }}
    response = requests.post(url=MAIN_HOST + '/esign-docs/template/list', json=queryData, headers=headers)
    jsonResponse = response.json()
    templateUuid = jsonResponse['data']['list'][0]['templateUuid']
    version = jsonResponse['data']['list'][0]['version']
    publishData = {"params": {
        "templateUuid": templateUuid,
        "version": version,
        "isPublish": "true"
    }}
    requests.post(url=MAIN_HOST + '/esign-docs/template/saveOrPublish', json=publishData, headers=headers)


def createBusiness():
    return sc.createBusiness(get_main_token())


def updateBusinessType(businessTypeId: str, isOpen: int):
    return sc.updateBusinessType(get_main_token(), businessTypeId, isOpen)


def businessOpenAndCloesd(businessTypeId: str):
    sc.updateBusinessType(get_main_token(), businessTypeId, 1)
    sc.updateBusinessType(get_main_token(), businessTypeId, 0)
    return True


# 校验流程按钮是否配置
def checkNodeButton(processInstanceId, buttonName):
    headers = gen_main_headers()

    data = {"params": {"processInstanceId": processInstanceId}}
    response = requests.post(url=MAIN_HOST + '/esign-docs/flow/manage/getWorkflowInstanceHandleInfo',
                             json=data, headers=headers)
    jsonResponse = response.json()
    nodeButtonList = jsonResponse['data']['nodeButtonList']
    for nodeButton in nodeButtonList:
        if nodeButton['buttonName'] == buttonName:
            return True
    return False


def gen_token_header_permissions(accountNumber: object, password: object, navId=None) -> object:
    projectId = ENV("esign.projectId")
    authorization = ""
    if accountNumber == sign01_customAccountNo:
        authorization = PORTAL_TOKEN
    if accountNumber == ENV('userCode'):
        authorization = SEAL_TOKEN
    if accountNumber == ENV('ceswdzxzdhyhwgd1.account'):
        authorization = DOC_TOKEN
    if authorization == "":
        authorization = getPortalToken(accountNumber, password)
    headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId, "authorization": authorization}
    headers['deptId'] = ENV("deptId")
    if navId is not None:
        headers['navId'] = navId
    return headers

def gratherUrl(workflowId, batchTemplateInitiationUuid):
    return MAIN_HOST + '/doc-manage-web/home-workFlow?workflowId=' + workflowId + '&bussinessId=' + batchTemplateInitiationUuid + '&noWorkflowCodePageName=&relateUuid=' + getRelateUuid(
        'wsignwb01_accountNo', 'wsignwb01.password')


# if __name__ == '__main__':


# 通过组织id获取用户列表GetUserListByOrganizationID数据校验
def getorganizationIdlist(response, organizationId):
    data = response.json['data']['userList']
    for i in data:
        if i['organizationId'] == organizationId:
            response.json['orgid'] = i['organizationId']


# 根据角色编码获取关联的用户列表getUserListByRoleCode数据校验
def getuserCode1(response, userCode):
    data = response.json['data']
    for i in data:
        if i['userCode'] == userCode:
            response.json['userCode1'] = i['userCode']


# 根据角色编码获取关联的用户(包括所属组织、所在公司)getUserOrgListByRoleCode数据校验
def getusercode(response, userCode):
    data = response.json['data']['userList']
    for i in data:
        if i['userCode'] == userCode:
            response.json['code'] = i['userCode']


# 通过用户组编码获取组员列表getUserListByUserGroupCode数据校验
def getuserCode(response, userCode):
    data = response.json['data']['userList']
    for i in data:
        if i['userCode'] == userCode:
            response.json['userName1'] = i['userName']


# 获取当天0点时间
def getTodayZeroTime():
    """
    弃用，推荐使用 getDateTime
    :return:
    """
    return (datetime.datetime.now()).strftime('%Y-%m-%d 00:00:00')


# 获取当前时间
def getNowTime():
    """
    弃用，推荐使用 getDateTime
    """
    return datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')


def getNowDate():
    """
    弃用，推荐使用 getDateTime
    """
    return datetime.datetime.now().strftime('%Y-%m-%d')


# 获取明天时间
def getTomorrowTime():
    """
    弃用，推荐使用 getDateTime
    """
    return (datetime.datetime.now() + datetime.timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')


# 获取特定的流程引擎名称的modelKey-用于发起电子签署关联流程
def getWorkFlowModelKey(workflowName):
    headers = gen_main_headers()
    response = requests.get(url=MAIN_HOST + '/esign-docs/businessPreset/getWorkflowList/0',
                            headers=headers)
    jsonResponse = response.json()
    workflowList = jsonResponse['data']['workflowList']
    for workflow in workflowList:
        if workflow['workflowName'] == '【电子签署】-' + workflowName:
            return workflow['modelKey']


# 模版新建-添加签名区-模板关联内容域-发布模板
def newTemplateId_word():
    wordFileKey = attachment_upload("word-测试模版.docx")
    response = requests.post(url=MAIN_HOST + '/esign-docs/template/owner/add',
                             json={"params": {"fileKey": wordFileKey,
                                              "templateName": "自动化测试通用模版-word-" + str(get_randomNo()),
                                              "createUserOrg": getUserMainOrg(os.environ['ceswdzxzdhyhwgd1.userCode']),
                                              "docUuid": get_a_docConfig_type(),
                                              "allRange": 1}},
                             headers=gen_main_headers())
    jsonResponse = response.json()
    templateUuid = jsonResponse['data']['templateUuid']
    version = jsonResponse['data']['version']
    requests.post(url=MAIN_HOST + '/esign-docs/template/owner/saveOrPublish',
                  json={"params": {"templateUuid": templateUuid, "version": version, "isPublish": "true"}},
                  headers=gen_main_headers())
    return templateUuid

# 模版新建-添加签名区-模板关联内容域-发布模板
def newTemplateId(contentNum, signatoryNum):
    headers = get_main_headers("template_manage_navId")
    from utils.esign6Docs import newTemplateIdV2
    return newTemplateIdV2(headers,contentNum, signatoryNum)

def getPreset_flowModel():
    return getPresetTypeId(11)

# 根据文件和签署方个数获取业务模板-优先获取config文件参数，判断状态非启用状态则走新建逻辑并替换config文件对应值
def getPreset(fileNum, SignerNum):
    headers = gen_main_headers()
    keyName = "presetId_" + str(fileNum) + "File_" + str(SignerNum) + "Signer"
    presetId = get_config(keyName)
    respond = requests.get(url=MAIN_HOST + '/esign-docs/businessPreset/detail/' + presetId, headers=headers)
    jsonRespond1 = respond.json()
    if jsonRespond1["status"] == 200:
        if jsonRespond1["data"]["status"] == 1:
            return presetId
    presetId = newPresetId(fileNum, SignerNum)
    set_config(keyName, presetId)
    return presetId

# 获取采集模板列表
def get_collectionTemplateId(collectionTemplateName=None):
    data = {
        "pageNo": 1,
        "pageSize": 50
    }
    if collectionTemplateName:
        data['collectionTemplateName'] = collectionTemplateName
    response = requests.post(url=OPENAPI_HOST + '/esign-docs/v1/collection/templateList', json=data,
                             headers=gen_openapi_post_headers_getway(data))
    jsonResponse = response.json()
    assert jsonResponse['code'] == 200
    collectionTemplateId = jsonResponse["data"]["collectionTemplateInfos"][0]["collectionTemplateId"]

    return collectionTemplateId


def newPresetId(fileNum, SignerNum):
    headers = gen_main_headers()
    presetName = "自动化测试业务模板-" + str(fileNum) + "签署文件和" + str(SignerNum) + "签署方-" + str(get_randomNo())
    requests.post(url=MAIN_HOST + '/esign-docs/businessPreset/create',
                  json={"params": {"presetName": presetName}},
                  headers=headers)
    headers2 = gen_main_headers_navId("businessPreset_navId")
    response = requests.post(url=MAIN_HOST + '/esign-docs/businessPreset/list',
                             json={"params": {"presetName": presetName, "page": 1, "size": 1}},
                             headers=headers2)
    responseJson = response.json()
    presetId = responseJson["data"]["list"][0]["presetId"]
    if fileNum == 0 and SignerNum == 0:
        requests.post(url=MAIN_HOST + '/esign-docs/businessPreset/addSigners',
                      json={"params": {"presetId": presetId, "status": 1, "allowAddSigner": 1, "sort": 0}},
                      headers=headers2)
    # 0个模板文件，1个签署方为内部个人指定
    if fileNum == 0 and SignerNum == 1:
        userCode = ENV('ceswdzxzdhyhwgd1.userCode'),
        sigerId = get_randomNo_32()
        userName = ENV('ceswdzxzdhyhwgd1.userName'),
        requests.post(url=MAIN_HOST + '/esign-docs/businessPreset/addSigners',
                      json={"params": {"presetId": presetId, "status": 1, "allowAddSigner": 0, "sort": 0,
                                       "signerNodeList": [{"signMode": 1,
                                                           "signerList": [{"signerId": sigerId, "signerTerritory": 1,
                                                                           "signerType": 1, "assignSigner": 1,
                                                                           "userName": userName, "userCode": userCode,
                                                                           "legalSign": 0, "autoSign": 0,
                                                                           "needGather": 0, "signNode": 2,
                                                                           "signOrder": 1, "signMode": 1,
                                                                           "signatoryList": []}]}]}},
                      headers=headers2)
    if fileNum == 1 and SignerNum == 0:
        res = requests.post(url=MAIN_HOST + '/esign-docs/businessPreset/addDetail',
                            json={"params": {"presetId": presetId, "initiatorAll": 1, "presetName": presetName,
                                             "initiatorEdit": 1, "allowAddFile": 1, "allowAddSigner": 1, "sort": 0,
                                             "workFlowModelStatus": 0,
                                             "templateList": [
                                                 {"templateId": getTemplateId(1, 2), "version": 1}],
                                             "checkRepetition": 1}},
                            headers=gen_main_headers())
        requests.post(url=MAIN_HOST + '/esign-docs/businessPreset/addSigners',
                      json={"params": {"presetId": presetId, "status": 1, "sort": 0, "allowAddSigner": 1}},
                      headers=gen_main_headers())
    # 1个模板文件，1个签署方为不指定
    if fileNum == 1 and SignerNum == 1:
        templateId = getTemplateId(1, 2)
        # templateId = "720fa0bf87faebdbb7b57f6b463f141f"
        headers3 = gen_main_headers_navId("template_manage_navId")
        response = requests.post(url=MAIN_HOST + '/esign-docs/template/manage/detail',
                                 json={"params": {"templateUuid": templateId, "version": 1}},
                                 headers=headers3)
        jsonResponse = response.json()
        signatoryId0 = jsonResponse["data"]["signatoryList"][0]["signatoryUuid"]
        signatoryId1 = jsonResponse["data"]["signatoryList"][1]["signatoryUuid"]
        signatoryName0 = jsonResponse["data"]["signatoryList"][0]["name"]
        signatoryName1 = jsonResponse["data"]["signatoryList"][1]["name"]
        templateName = jsonResponse["data"]["templateName"]
        headers2 = gen_main_headers_navId("businessPreset_navId")
        requests.post(url=MAIN_HOST + '/esign-docs/businessPreset/addDetail',
                      json={"params": {"presetId": presetId, "presetName": presetName, "initiatorAll": 1,
                                       "initiatorEdit": 0, "allowAddFile": 1, "allowAddSigner": 0,
                                       "sort": 0, "workFlowModelStatus": 0, "signatoryCount": 2,
                                       "templateList": [
                                           {"templateId": templateId, "version": 1}],
                                       "checkRepetition": 1, "fileFormat": 1}},
                      headers=headers2)
        requests.post(url=MAIN_HOST + '/esign-docs/businessPreset/addSigners',
                      json={"params": {"presetId": presetId, "status": 1, "allowAddSigner": 0, "sort": 0,
                                       "signerNodeList": [{"signMode": 1,
                                                           "signerList": [{"signerType": 1, "signerTerritory": 1,
                                                                           "autoSign": 0, "assignSigner": 0,
                                                                           "signerId": get_randomNo_32(),
                                                                           "onlyUkeySign": 0,
                                                                           "signatoryList": [
                                                                               {"signatoryId": signatoryId0,
                                                                                "templateId": templateId,
                                                                                "templateName": templateName,
                                                                                "signatoryName": signatoryName0,
                                                                                "sealType": 1},
                                                                               {"signatoryId": signatoryId1,
                                                                                "templateName": templateName,
                                                                                "signatoryName": signatoryName1,
                                                                                "templateId": templateId,
                                                                                "sealType": 1}
                                                                           ]}]}]}},
                      headers=headers2)
    return presetId


def getTime(n=0):
    """
    弃用，推荐使用 getDateTime
    """
    afterNdaysTime = datetime.datetime.now() + datetime.timedelta(days=-n)
    return afterNdaysTime.strftime('%Y-%m-%d %H:%M:%S')


def getDateTime(day=0, timeFormat=None):
    """
    获取时间、日期
    :param timeFormat: int  1-'%Y-%m-%d %H:%M:%S' 、2-'%Y-%m-%d'、3-'%Y-%m-%d %H:%M:%S.%f'
    :param day: int 距今天多少天，传负数则为过去时间
    :return: 默认返回当前时间到微秒级 20220616163258.767022 可作唯一字符串，防重
    """
    from utils.dateAndTime import somedayTime
    return str(somedayTime(day, timeFormat))


def getTimestamp(day=0):
    """
    :param day: 未来距今天多少天
    :return: 时间戳，1678695854818 毫秒
    """
    from utils.dateAndTime import somedayTimestamp
    return int(somedayTimestamp(day))

import datetime


def getTimesOff(sec=0):
    today = datetime.datetime.now()
    # 计算偏移量
    # 时间加1秒钟
    offset = datetime.timedelta(seconds=sec)
    # 获取修改后的时间并格式化
    re_date = (today + offset).strftime('%Y-%m-%d %H:%M:%S')
    return re_date


def getFileForUpload(fileName):
    """
    打开tests/data目录下的文件，上传文件接口请求体
    :param fileName: 文件名称
    :return:
    """
    if fileName.startswith("data/"):
        current_path = os.path.abspath(__file__)
        filePath = os.path.join(os.path.dirname(current_path), fileName)
    else:
        basePath = os.path.dirname(__file__)
        filePath = os.path.join(basePath, 'data', fileName)
    return open(filePath, "rb")


def filepath(file_name, isAll=False):
    base_path = os.path.dirname(__file__)
    file_path = os.path.join(base_path, 'data', file_name)
    url = OPENAPI_HOST + '/file/v1/upload'
    # data = {
    #     "filePwd": file_name
    # }
    file = {'file': open(file_path, 'rb')}
    response = requests.post(url=url, files=file)
    print(response)
    jsonResponse = response.json()
    if isAll:
        return jsonResponse
    else:
        fileKey = jsonResponse['data']['fileKey']
        return fileKey
##获取excel中单元格数据，i是行数，j是列数

def get_main_headers(navid_key=None):
    """
    统一登录平台的headers
    :param navIdKey:
    :return: dict
    """
    headers = {"Content-Type": "application/json",
               "authorization": PORTAL_TOKEN,
               "X-timevale-project-id": PROJECT_ID}
    if navid_key:
        headers['navId'] = os.environ.get(navid_key)
    return headers

def getInfoDealToSubmit(params):
    """
       组装数据主要处理签署方和签署区之间的关联
       :param signerInfos:  getSignerListTosubmit()的出参
       :param detailData:  getInfo接口的出参的data对象信息
       :return:
       """
    from utils.esign6Docs import getInfoDeal
    return getInfoDeal(params)

def getTemplateId(contentNum, signatoryNum, fileType=None, addNewOne=0):
    """
    【文档中心】根据传参获取一个符合条件的模板
    param:contentNum: 内容域个数
    param:signatoryNum：签署区个数
    param:fileType：模板类型，pdf/word
    param:addNewOne 是否每次调用都新增一个模板，如果为0:则只会返回一个固定的模板，如果为1：则每次调用都返回一个新的模板
    """
    headers = get_main_headers("template_manage_navId")
    from utils.esign6Docs import get_a_templateId
    return get_a_templateId(headers, contentNum, signatoryNum, fileType, addNewOne)

def getPresetIdAndBusinessTypeId(presetName, allowSetSigners=0, allowFill=0, outerFlag=None):
    """
    【文档中心】根据传参获取一个符合条件的模板
    param:presetName: 业务模板名称
    param:allowSetSigners：是否指定签署方，指定就默认绑定2个内部用户
    param:allowFill：是否需要内容域，需要则默认添加2个内容域
    """
    headers = {"Content-Type": "application/json","authorization": PORTAL_TOKEN,
               "X-timevale-project-id": PROJECT_ID,"navId": "1523545065987903488"}
    from utils.esign6Docs import get_a_businessPresetId
    presetIdAndBusinessTypeId = get_a_businessPresetId(headers, presetName, allowSetSigners, allowFill,outerFlag)
    return presetIdAndBusinessTypeId

def getBusinessTypeId(presetName, allowSetSigners=0, allowFill=0, outerFlag=None):
    res = getPresetIdAndBusinessTypeId(presetName, allowSetSigners, allowFill,outerFlag)
    return res[1]

def getPresetId(presetName, allowSetSigners=0, allowFill=0):
    res = getPresetIdAndBusinessTypeId(presetName, allowSetSigners, allowFill)
    return res[0]

def getBusinessTypeAttr(businessTypeId, paramKey, index=0):
    """
    【文档中心】查询业务模板并返回对应的值
    param:businessTypeId: 业务模板编号
    param:paramKey：需要获取值对应的key
    param:index：获取数组对象中的第几个，默认首个
    """
    from utils.esign6Docs import get_business_attr
    return get_business_attr(businessTypeId, paramKey, index)

def dealCollectionPageConfig(fields,fieIdNames):
    """
    【低代码采集】处理数据：创建采集模板的时候设置单行文本的模糊搜索开关
    param:fields: 当前采集模板的所有字段
    param:fieIdNames：需要进行模糊搜索的单行文本的名称 （注意制作采集模板表单的时候控件名称尽量不要相同）
    """
    from utils.esign6Docs import deal_collection_pageConfig
    return deal_collection_pageConfig(fields,fieIdNames)

def compareDict(keyParams, valueParams):
    """
    处理json: key是动态变更的数据
    :param keyParams:  e.g:['key_001', '_key_002']
    :param valueParams:  e.g:['测试签署一', '***********']
    :return:  e.g:{'key_001': '测试签署一', '_key_002': '***********'}
    """
    from utils.esign6Docs import compare_collection
    return compare_collection(keyParams, valueParams)

def dataArraySort(json_array, sort_str,index, key_str):
    """
    给数组按照某个字段排序后再返回第index个对象里面的某个字段值
    :param json_array: 需要处理的数组数据
    :param field_to_sort: 用于排序的字段
    :param index: 获取第N个对象
    :param key_str: 获取json中的键为key_str的值
    :return:
    """
    print(f"[排序数据]：{json_array}")
    from utils.esign6Signs import data_array_sort
    return data_array_sort(json_array, sort_str,index, key_str)

def extractParameterValue(url, parameter_name):
    """
    处理url中的参数
    :param url: 长链
    :param parameter_name: 需要获取的参数key
    :return: 对应的值
    """
    from utils.esign6Docs import extract_parameter_value
    return extract_parameter_value(url, parameter_name)

def getRemarkCollectionTaskId(collectionTaskName=None):
    """
    查询备注采集任务信息
    :param headers:
    :param collectionTaskName: 备注采集任务的名称 模糊查询
    :return:
    """
    headers = get_main_headers()
    from utils.esign6Docs import getRemarkCollectionTaskId
    return getRemarkCollectionTaskId(headers,collectionTaskName)

def getRemarkCollectionTaskName(collectionTaskName=None):
    """
    查询备注采集任务信息
    :param headers:
    :param collectionTaskName: 备注采集任务的名称 模糊查询
    :return:
    """
    headers = get_main_headers()
    from utils.esign6Docs import getRemarkCollectionTaskName
    return getRemarkCollectionTaskName(headers,collectionTaskName)

def getTaskId(url):
    # 解析URLtestcases/v1/sealcontrols/userCerts/certRevoke.yml
    parsed_url = urlparse(url)
    # 解析查询字符串
    query_params = parse_qs(parsed_url.query)
    # 获取taskId的值
    task_id = query_params.get('taskId', [None])[0]
    return task_id

def get_encrypt(params):
    """
    aes加密
    """
    from utils.esign6Manage import get_aes_encrypt
    return get_aes_encrypt(params)

def encryptKey(params):
    """
    RSA加密
    """
    from utils.common import encryptKey
    return encryptKey(params)

def getHeaderVerificationCode():
    """
    获取图形验证码的请求头里的VerificationCode
    :return:
    """
    from utils.esignToken import createVerificationCode
    return createVerificationCode()

def get_snowflake():
    """
    雪花算法生成随机值12位
    :return:
    """
    from tools import snowFlake
    return snowFlake.Snow().get_guid()

def checkSignFlowIdByDay(date0=None):
    """
    比对流程的消息总数并生成记录文件
    :param date0:
    :return:
    """
    token2 = MANAGE_TOKEN
    token = PORTAL_TOKEN
    from utils.esign6Message import checkSignFlowIdByDay
    return checkSignFlowIdByDay(token,token2,date0)

def checkSignFlowIdByDayWithDetail(date0=None):
    """
    比对流程的消息总数并生成记录文件
    :param date0:
    :return:
    """
    token2 = MANAGE_TOKEN
    token = PORTAL_TOKEN
    from utils.esign6Message import checkSignFlowIdByDayWithDetail
    return checkSignFlowIdByDayWithDetail(token,token2,date0)

def getBusinessPresetTypeAll(presetType):
    """
    获取固定类型的业务模板
    :param presetType: 自定义类型
    :return:
    """
    headers = get_main_headers()
    from utils.esign6Docs import get_businessPreset_type
    res = get_businessPreset_type(headers, presetType)
    return res

def getBusinessTypeId2(presetType):
    """
    获取固定类型的业务模板
    :param presetType: 自定义类型
    :return: 业务模板的编号
    """
    res = getBusinessPresetTypeAll(presetType)
    return res[1]

def getPresetTypeId(presetType):
    """
    获取固定类型的业务模板
    :param presetType: 自定义类型
    :return: 业务模板的id
    """
    res = getBusinessPresetTypeAll(presetType)
    return res[0]

def checkOrgToSign(sealPattern,isNeedCert,isNeedOrgSeal,isNeedLegalSeal,orgNo=None):
    """
    构造需要的内部企业签署方信息。比如：只有法人章
    :param presetType: 自定义类型
    :return:
    """
    if orgNo == None:
        orgNo = "ORG-SIGN-05" # esigntest同级企业CI 用于验证只有企业章或是只有法人章的时候的签署数据
    from utils.esign6Seals import check_org_tosign
    res = check_org_tosign(orgNo,sealPattern,isNeedCert,isNeedOrgSeal,isNeedLegalSeal)
    return res

def certOrgRevoke(orgCode,algorithm):
    from utils.esign6Seals import certOrgAllRevoke
    res = certOrgAllRevoke(orgCode,algorithm)
    return res


def dealDataTsp(paramsDatas, targetId, targetKey, targetValue):
    """
    处理签署方的通道信息，将指定的key的值替换
    :param paramsDatas:
    :param targetId:
    :param targetKey:
    :param targetValue:
    :return:
    """
    from utils.esign6Docs import deal_data_tsp
    return deal_data_tsp(paramsDatas, targetId, targetKey, targetValue)

def getCmcKeyId(configCode, groupId=None, moduleId=None):
    """
    处理签署方的通道信息，将指定的key的值替换
    :param paramsDatas:
    :param targetId:
    :param targetKey:
    :param targetValue:
    :return:
    """
    from utils.esign6Manage import getCmcKeyId
    return getCmcKeyId(MANAGE_TOKEN, configCode, groupId, moduleId)

def get_regular_str(str,key):
    """
    正则匹配字符串str,获取key相应的值
    :param str:
    :param key:
    :return:
    """
    from utils.common import get_regular_str
    return get_regular_str(str,key)

def checkUserToSign(sealPattern,isNeedCert,isNeedSeal,userNo=None):
    """
    构造需要的内部用户署方信息。比如：没有印章只有证书
    :param
    :return:
    """
    if userNo == None:
        userNo = "sign02"
    from utils.esign6Seals import check_user_tosign
    res = check_user_tosign(userNo,sealPattern,isNeedCert,isNeedSeal)
    return res

def getGroupInfo(groupName,organizationCode=None,userCode=None,wfGroup=None):
    """
    通过分组名称获取印章分组（无，则新建）
    wfGroup： 为空则创建普通类型的分组；1-创建带流程的分组
    """
    headers = {'code': TOKEN_OBJ.code,
               'authorization': TOKEN_OBJ.token,
               'X-timevale-project-id': PROJECT_ID}
    if organizationCode == None:
        organizationCode = ENV('csqs.orgCode')
    from utils.esign6Seals import getGroupInfo
    return getGroupInfo(headers,groupName,organizationCode,userCode,wfGroup)

def getGroupId(groupName,organizationCode=None,userCode=None,wfGroup=None):
    obj1 = getGroupInfo(groupName,organizationCode,userCode,wfGroup)
    return obj1.get('sealGroupId')

def getCreateByBizTemplateId(businessTypeCode=None):
    try:
        from utils.esign6Docs import getCreateByBizTemplate
        signFlowId = getCreateByBizTemplate(businessTypeCode)
        addFiles(OPENAPI_HOST, PROJECT_ID, PROJECT_SECRET, signFlowId, pdf_filekey)
        return signFlowId
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None


# 获取可用项目
def projectSelection():
    data = {
        "params": {},
        "domain": "seal_system"
    }
    headers = {
        'code': TOKEN_OBJ.code,
        'authorization': TOKEN_OBJ.token,
        'X-timevale-project-id': PROJECT_ID
    }
    try:
        response = requests.post(url=MAIN_HOST + '/seals/manage/project/projectSelection',
                      json=data, headers=headers)
        jsonResponse = response.json()
        projectId = jsonResponse['data'][0]['projectId']
        return projectId
    except:
        raise Exception("projectId获取失败")

# base的公共方法 ###################################XXXXXXXX
def async_spilt(myFileKey):
    url = OPENAPI_HOST + '/file/rpc/split'
    response = requests.post(url=url, json={'callbackUrl': 'http://baidu.com', "fileKey": myFileKey})
    return myFileKey

def ENV(keyname):
    value = os.environ.get(keyname, '')
    return value

def fileUploadAndSpilt(file_name, pwd=None):
    base_path = os.path.dirname(__file__)
    file_path = os.path.join(base_path, 'data', file_name)
    url = OPENAPI_HOST + '/file/v1/pdf/uploadAndSpilt'
    file = {'file': open(file_path, 'rb')}
    print('request----')
    response = requests.post(url=url, files=file, data={'filePwd': pwd})
    print(response.json())
    jsonResponse = response.json()
    fileKey = jsonResponse['data']['fileKey']
    return fileKey

def generate_upload_url(requestID, type):
    url = OPENAPI_HOST + '/file/v1/generateUploadUrl'
    print(url)
    data = {"requestID": requestID, "type": type}
    print(data)
    jsonResponse = requests.post(url=url, json=data, headers=gen_headers_signature(data)).json()
    print(jsonResponse)
    return jsonResponse['data']['url']

def tear_has_except_block_number(response, pageNumber):
    print(response.json)
    pageSize = response.json['data'][0]['pageEnd']
    print(pageSize)
    import math
    response.result = math.ceil(pageNumber / pageSize) == len(response.json['data'])
    # response.result = "true"

##aes加密
def get_aes_encrypt1(param):
    """
    AES加密，适用于各种手机号证件号明文转密文
    :param param: 明文
    :return: 密文
    """
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    from cryptography.hazmat.primitives.padding import PKCS7
    from cryptography.hazmat.backends import default_backend
    encryption_key = b'JZkaKU4k9J7Z9L7N'
    cipher = Cipher(algorithms.AES(encryption_key), modes.ECB(), backend=default_backend())
    encryptor = cipher.encryptor()
    padder = PKCS7(128).padder()
    padded_data = padder.update(param.encode('utf-8')) + padder.finalize()
    ciphertext = encryptor.update(padded_data) + encryptor.finalize()
    encrypted_data = base64.b64encode(ciphertext).decode('utf-8')
    return encrypted_data

def tear_file_stream_response(response):
    local_filename = "tear_file_stream_response.pdf"
    f = open(local_filename, 'wb')
    for chunk in response.iter_content(chunk_size=512 * 1024):
        if chunk:  # filter out keep-alive new chunks
            f.write(chunk)
    f.close()
    lengg = os.path.getsize(local_filename)
    print(lengg)
    response.result = os.path.getsize(local_filename) > 0
    os.remove(local_filename)

def teardown_file(response, res):
    response.code2 = res["code"]
    response.fileKey2 = res["data"]["fileKey"]

# 上传文件
def get_file1(file_name):
    """
    打开/data目录下的文件，上传文件接口请求体
    :param file_name: 文件名称
    :return: bytes
    """
    base_path = os.path.dirname(__file__)
    file_path = os.path.join(base_path, 'data', file_name)
    with open(file_path, "rb") as file:
        file_contents = file.read()
    return file_contents

# 获取token
def get_token_web(accountNumber, password):
    if accountNumber == sign01_customAccountNo:
        return PORTAL_TOKEN
    else:
        token_obj1 = createPortalTokenObj(accountNumber, password)
        return token_obj1.token

def gen_token_header(accountNumber, password, navId=None,language="zh-CN"):
    projectId = PROJECT_ID
    if accountNumber == sign01_customAccountNo:
        authorization = PORTAL_TOKEN
    else:
        authorization = get_token_web(accountNumber, password)
    headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId, "authorization": authorization,"navid":navId, "language":language}
    return headers

def create_inner_organizations(data):
    """
    创建内部组织
    :param customOrgNo: 内部组织账号
    :param name: 内部组织名称
    :param organizationType: 内部组织类型
    :param parentCode: 上级组织编码
    :return: organizationCode 内部组织编码
    author:须弥
    created:2022/3/30
    """
    time.sleep(3)
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerOrganizations/create', json=data,
                             headers=gen_headers_signature(data))
    if response.json()['data'] == '':
        print(response.json())
    else:
        print('[openapi创建组织]：', response.json())
        return response.json()['data']


def create_outer_organizations(data):
    """
    创建外部组织
    author:须弥
    created:2022/5/16
    """
    time.sleep(3)
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/outerOrganizations/create', json=data,
                             headers=gen_headers_signature(data))
    print(gen_headers_signature(data))
    if response.json()['code'] == 200:
        print(response.json())
        return response.json()['data']
    else:
        print(data)
        print(response.json())
        customOrgNo = response.json()['data']['customOrgNo']
        organizationCode = response.json()['data']['existOrganizationCode']
        data = {'customOrgNo': customOrgNo, 'organizationCode': organizationCode}
        return data


def create_inner_users(data):
    """
    创建内部用户
    """
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerUsers/create', json=data,
                             headers=gen_headers_signature(data))
    if response.json()['data'] == '':
        print(response.json())
        print(data)
    if response.json()['data']['successData']:
        return response.json()['data']['successData'][0]
    if response.json()['data']['failureData']:
        return response.json()['data']['failureData'][0]


def create_inner_users_2(data):
    """
    批量创建内部用户
    """

    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerUsers/create', json=data,
                             headers=gen_headers_signature(data))
    if response.json()['data'] == '':
        print(response.json())
        print(data)
    if response.json()['data']['successData']:
        return response.json()['data']['successData']
    if response.json()['data']['failureData']:
        return response.json()['data']['failureData']


def create_outer_users(data):
    """
    创建外部用户
    """

    response = requests.post(url=OPENAPI_HOST + '/manage/v1/outerUsers/create', json=data,
                             headers=gen_headers_signature(data))
    if response.json()['data'] == '':
        print(response.json())
    if response.json()['data']['successData']:
        return response.json()['data']['successData'][0]
    if response.json()['data']['failureData']:
        return response.json()['data']['failureData'][0]

def get_non_null_value(a, b):
    if a is not None:
        return a
    elif b is not None:
        return b
    else:
        return None

def get_organizationCode(data):
    '''
    :param data:
       结构，如:{"customOrgNo":"ZDHCSSCNBZZ_1","organizationCode":null,"existCustomOrgNo":"ZDHCSSCNBZZ_1","existOrganizationCode":"38afc0857edb417aa2af2407aa31a41e"}}'
    :return:
    '''
    organization_code = data['organizationCode']
    exist_organization_code = data['existOrganizationCode']
    return get_non_null_value(organization_code,exist_organization_code)

def gen_upload_token_header(accountNumber, password, navId=None,language="zh-CN"):
    projectId = PROJECT_ID
    authorization = get_token_web(accountNumber, password)
    headers = {"x-timevale-project-id": projectId, "authorization": authorization,"Navid":navId, "language":language}
    return headers

def assert_contains_equals(data, except_value):
    """
    自定义校验器：校验外部组织详情字段
    :param data: 返回的字段值
    :param except_value: 期望的字段值
    :return:
    """
    if isinstance(except_value, int):
        except_value = str(except_value)
    assert data in except_value or data == except_value

def inner_users_detail(data):
    user_details = []
    for item in data:
        param_data = {"mobile": item.get('mobile')}
        response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerUsers/detail', json=param_data,
                                 headers=gen_headers_signature(param_data))
        if response.json()['code'] == 200 and len(response.json()['data']) > 0:
            user_detail = response.json()['data'][0]
            user_details.append(user_detail)
        else:
            print(response.json())
            print("执行inner_users_detail获取用户信息失败")
    return user_details

def delete_inner_user(user_code):
    data = {"userCode": user_code}
    response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerUsers/delete', json=data,
                         headers=gen_headers_signature(data))


def delete_inner_users(successData):
    """
    :param successData: 内部用户列表
    :return:
    """
    for item in successData:
        user_code = item['userCode']
        delete_inner_user(user_code)


def delete_outer_users(successData):
    """
    :param successData: 外部用户列表
    :return:
    """
    for item in successData:
        data = {"customAccountNo": "", "userCode": item.get('userCode')}
        response = requests.post(url=OPENAPI_HOST + '/manage/v1/outerUsers/delete', json=data,
                                 headers=gen_headers_signature(data))
        assert response.json()['code'] == 200, response.json()
        assert response.json()['message'] == '成功'

def delete_inner_organizations(response=None, organizationCode=None):
    """
    Description: 删除内部组织
    Created: 2022/3/26
    Updated: 2022/3/29
    Author: 须弥
    :param response: httprunner response对象
    :return:
    """
    if organizationCode:
        data = {"organizationCode": organizationCode, "customOrgNo": ""}
    else:
        data = {"organizationCode": response.json['data']['organizationCode'], "customOrgNo": ""}
    del_response = requests.post(url=OPENAPI_HOST + '/manage/v1/innerOrganizations/delete', json=data,
                                 headers=gen_headers_signature(data))
    assert del_response.json()['code'] == 200, del_response.json()
    assert del_response.json()['message'] == '成功'


def delete_outer_organizations(response=None, organizationCode=None):
    """
    Description: 删除外部组织
    Created: 2022/4/7
    Updated: 2022/4/7
    Author: 夸父
    :param response: httprunner response对象
    :return:
    """
    if organizationCode:
        data = {"organizationCode": organizationCode, "customOrgNo": ""}
    else:
        data = {"organizationCode": response.json['data']['organizationCode'], "customOrgNo": ""}
    del_response = requests.post(url=OPENAPI_HOST + '/manage/v1/outerOrganizations/delete', json=data,
                                 headers=gen_headers_signature(data))
    # print(del_response.json())
    # assert del_response.json()['code'] == 200, del_response.json()
    # assert del_response.json()['message'] == '成功'

def deleteInnerUsers(userName,userCode=None):
    """
    通过用户名称或是code删除符合条件的用户
    """
    from utils.esign6Manage import delete_inner_user_by_name
    delete_inner_user_by_name(userName,userCode)

def deleteInnerOrgs(orgName,orgCode=None):
    """
    通过企业名称或是企业code删除符合条件的企业
    """
    from utils.esign6Manage import delete_inner_org_by_name
    delete_inner_org_by_name(orgName,orgCode)

def deleteOuterOrgs(orgName,orgCode=None):
    """
    通过企业名称或是企业code删除符合条件的相对方企业
    """
    from utils.esign6Manage import delete_outer_org_by_name
    delete_outer_org_by_name(orgName,orgCode)

def get_verificationCode(esignSignsWebUrl):
   print(esignSignsWebUrl)
   response = requests.get(url=esignSignsWebUrl + '/sso/sendVerifyCode')
   with open(verify_code_image_path, 'wb') as f:
      f.write(response.content)
   # with open(verify_code_image_path, 'rb') as fp:
   #         fp.read()
   vertification_code_header = response.headers['verificationCode']
   #verification_code = get_verification_code()
   verification_code = '9999'
   if len(verification_code) == 4 and verification_code.isdigit():
      return verification_code,vertification_code_header

verification_code, vertification_code_header = get_verificationCode(MAIN_HOST)

def get_1():
    return verification_code

def get_2():
    return vertification_code_header

def getFormatTime(days, type):
    seconds = datetime.timedelta(days).total_seconds()
    time_now = time.time()
    result = 0
    if "add" == type :
        result = time_now + seconds
    else :
        result = time_now - seconds
    result = time.localtime(result)
    time_str = time.strftime("%Y-%m-%d %H:%M:%S", result)
    print("目标时间：" + time_str)
    return time_str

def getVerCodeToken(phoneOrMail, userTerritory=None):
    """
    外部/内部签署人，通过验证码登录获取token
    :param phoneOrMail: 手机号或者邮箱
    :param userTerritory: '2'-外部，None-内部
    :return: token
    """
    # 自定义方法，调用时引入
    from utils.esignToken import createVerCodeToken
    return createVerCodeToken(phoneOrMail, userTerritory)

# 获取随机18位身份证号
def get_idNo():
    return cardGen.get_idNo()

# 获取随机组织机构代码
def get_orgCode():
    return cardGen.get_orgCode()

def get_randomNo_32():
    """
    生成32位随机数
    """
    return random.randint(10000000000000000000000000000000, 99999999999999999999999999999999)

def get_randomStr_32():
    """
    生成32位随机数
    """
    return str(get_randomNo_32())
def get_randomNo1():
    return random.randint(10000, 99999)
def getmodelId(modelId):
    seq = modelId.split(':')
    return seq[0]

# 获取data文件夹下的json里的初始化参数，默认使用default文件夹下的
def get_data(file_name, config_key, profile='default'):
    current_path = os.path.abspath(__file__)
    config_file_path = os.path.join(os.path.dirname(current_path), 'data', profile, file_name + '.json')

    with open(config_file_path, 'r') as f:
        config_dict = json.load(f)
    return config_dict[config_key]

def get_portal_data(file_name, config_key):
    profile = ENV('profile')
    # 默认测试环境
    if profile == '':
        profile = "test"
    current_path = os.path.abspath(__file__)
    config_file_path = os.path.join(os.path.dirname(current_path), 'data', profile, file_name + '.json')

    with open(config_file_path, 'r+', encoding="utf-8") as f:
        config_dict = json.load(f)
    return config_dict[config_key]

# 获取token需要提前安装依赖见http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145148972 第7点《6.0管理平台获取登录token需要安装的包》

# """
# 获取token需要提前执行安装依赖：pip3 install ddddocr
# 该依赖较大会有安装失败的风险，可参考：
# http://wiki.timevale.cn:8081/pages/viewpage.action?pageId=145148972 第7点《6.0管理平台获取登录token需要安装的包》解决失败问题
# """

def get_verificationCode1():
    response = requests.get(url=MAIN_HOST + '/sso/sendVerifyCode', headers=None)
    return response.headers['verificationCode']

def get_verification_code():
    import ddddocr
    ocr = ddddocr.DdddOcr()
    with open(verify_code_image_path, 'rb') as f:
        img_bytes = f.read()
    verification_code = ocr.classification(img_bytes)
    return verification_code

# 全局变量，用于实时保存图片验证码的值，初始值为空
verification_dict = {'verification_code': None, 'vertification_code_header': None}


def get_verification_dict():
    '''
    获取图片验证码，重新赋值全局变量verification_dict
    :return:
    '''

    try:
        code, code_header = get_verificationCode(MAIN_HOST)
        verification_dict['verification_code'] = code
        verification_dict['vertification_code_header'] = code_header
        return verification_dict
    except:
        print("-调用异常")

def get_dynamicCode(account, code):
    data = {"account": account, "platform": "pc", "accountType": 4, "verificationCode": code, "scene": 1,
            "userTerritory": 1}
    headers = {'verificationCode': get_verificationCode1()}
    res = requests.post(url=MAIN_HOST + '/sso/sendDynamicCode', headers=headers, json=data)
    # 触发了动态验证码发送
    if json.loads(res.text)["status"] == 200:
        print(res.text)
    else:
        print(res.text)
        print(sys._getframe().f_code.co_name + "-调用异常")


# 登录页-忘记密码：获取验证码
def get_dynamicCode1(account):
    data = {"account": account, "platform": "pc", "accountType": 4, "scene": 2, "userTerritory": None}
    res = requests.post(url=MAIN_HOST + '/sso/sendDynamicCode', headers=None, json=data)
    # 触发了动态验证码发送
    if json.loads(res.text)["status"] == 200:
        print(res.text)
    else:
        print(sys._getframe().f_code.co_name + "-调用异常")


def createOrgSeals(organizationCode, sealPattern=None, legalSealId=None, sealRelease=None, userCode=None,
                   needSeals=None, sealGroupName=None):
    from utils.esign6Seals import create_org_seal
    return create_org_seal(organizationCode,sealPattern,legalSealId,sealRelease,userCode,needSeals,sealGroupName)

def get_snowflake():
    """
    :param successData: 生成雪花id
    :return:
    """
    from tools import snowFlake
    return snowFlake.Snow().get_guid()

def get_navigationId(response, value):
    """
    :param successData: 通过value获取导航id并写入到返回值里面，导航列表接口返回专用
    :return:
    """
    data = response.json["data"]
    for i in data:
        if i['navigationName'] == value:
            response.json['navigationId'] = i['id']
            print(response.json['navigationId'])
            return i['id']
        else:
            if i['children'] != []:
                for bq in i['children']:
                    if bq['navigationName'] == value:
                        response.json['navigationId'] = bq['id']
                        return bq['id']

def get_subnavigationId(response, value):
    """
    :param successData: 通过value获取导航id并写入到返回值里面，导航列表接口返回专用
    :return:
    """
    data = response.json["data"]
    for i in data:
        if i['children']:
            for s in i['children']:
                if s['navigationName'] == value:
                    response.json['navigationId'] = i['id']
                    return i['id']


def get_roleFuncAuthNavigationId(response):
    """
    :param successData: 获取导航id的list并写入到返回值里面，功能授权专用
    :return:
    """
    data = response.json["data"]["navigationList"]
    l = []
    for i in data:
        if i['navigationName'] == "印控":
            for s in i['children']:
                l.append(s['id'])
                for j in s['children']:
                    l.append(j['id'])
                    for k in j['children']:
                        l.append(k['id'])
    response.json['AuthFuncNavigationListId'] = l


def get_roleAuthNavigationId(response):
    """
    :param successData: 获取导航id的list并写入到返回值里面，数据授权专用
    :return:
    """
    data = response.json["data"]["navigationList"]
    l = []
    for i in data:
        if i['navigationName'] == "印控":
            for s in i['children']:
                for k in s['children']:
                    l.append(k['parentId'])
                    l.append(k['id'])
    response.json['AuthNavigationListId'] = l


def get_dimissionId(response, value):
    """
    :param successData: 通过value获取离职id并写入到返回值里面，离职列表接口返回专用
    :return:
    """
    # data = response.json["data"]["list"]
    for i in response:
        if i['dimissionOrganizationName'] == value:
            # response.json['dimissionId'] = i['id']
            return i['id']


def get_pageConfig(response, value):
    """
    :param successData: 通过value获取页面配置并写入到返回值里面，查询页面配置列表接口返回专用
    :return:
    """
    data = response.json["data"]
    for i in data:
        if i['paramCode'] == value:
            response.json['data'][0] = i

def downloadLic(response):
    """
    :param successData: 将license信息保存到data目录下，授权管理导入接口专用
    :return:
    """
    content = response.json["licenseStr"]
    content = content.encode()
    fileName = 'license.txt'
    with open(os.path.join(DATAPATH, fileName), 'wb') as f:
        f.write(content)
def encrypt(param,password=b"&eU123n5ryx*@Qum"):
    """
    AES加密，适用于各种手机号证件号明文转密文
    :param param: 明文
    :return: 密文
    """
    encrypted_data = encryptKey(param)
    return encrypted_data

def get_date(d):
    """
    :param successData: 取系统d天后的日期，格式2020-10-29
    :return:
    """
    date_time = datetime.datetime.now() + datetime.timedelta(days=d)
    return date_time.strftime('%Y-%m-%d')


def get_current_date():
    """
    :param successData:取当前系统日期，格式2020-10-29
    :return:
    """
    date_time = datetime.datetime.now().strftime('%Y-%m-%d')
    return date_time


def get_current_time():
    """
    :param successData: 取当前系统时间，格式2020-10-29 16:50:37
    :return:
    """
    time_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    return time_time


def get_user_by_username(data):
    """
    根据姓名查询用户
    Author:须弥
    """
    print(f"执行get_user_by_username方法，传入参数{data}")
    response = requests.post(url=MAIN_HOST + '/manage/orguser/user/getUserOrgListByUserName', json=data, headers=gen_manage_headers())
    print(response.json())
    if len(response.json()['data']) == 1:
        return response.json()['data'][0]


def save_parameter_class(data):
    """
    新增参数分类
    Author:须弥
    """
    print(f"执行save_parameter_class方法，传入参数{data}")
    response = requests.post(url=MAIN_HOST + '/manage/appconfig/parameter/class/saveParameterClass', json=data,
                             headers=gen_manage_headers())

    if response.json()['message'] == '成功':
        return True
    print(response.json())


def get_parameter_by_code(code):
    """
    根据参数类编码查询ID
    Author:须弥
    """
    print(f"执行get_parameter_by_code方法，传入参数{code}")
    data = {
        "domain": "admin_platform",
        "params":
            {
                "currPage": 1,
                "outFlag": False,
                "pageSize": 10,
                "parameterClassName": "",
                "parameterClassType": "",
                "parameterItemParentId": ""
            }
    }
    response = requests.post(url=MAIN_HOST + '/manage/appconfig/parameter/class/pageParameterClassByParameterClassName',
                             json=data,
                             headers=gen_manage_headers())
    print(response.json())
    for item in response.json()['data']['list']:
        if item['parameterClassCode'] == code:
            return item['id']
    print(response.json())


def save_inner_user(data):
    """
    webapi创建内部用户
    Author:须弥
    """
    print(f"执行save_inner_user方法，传入参数{data}")
    response = requests.post(url=MAIN_HOST + '/manage/orguser/user/saveUser', json=data, headers=gen_manage_headers())
    if response.json()['message'] != '成功':
        print(response.json())
        print('webapi创建内部用户失败')

    return True


def save_project_config_info(data):
    """
    webapi新增项目配置信息
    Author:醉生
    """
    print(f"执行save_project_config_info方法，传入参数{data}")
    response = requests.post(url=MAIN_HOST + '/manage/proconfig/projectConfig/saveProjectConfigInfo', json=data,
                             headers=gen_manage_headers())
    if response.json()['message'] != '成功':
        print(response.json())
        print('webapi新增项目配置信息失败')

    return True


def get_project_config_by_name(name):
    """
    项目配置分页列表请求,根据name
    Author:醉生
    """
    print(f"执行get_parameter_by_code方法，传入参数{name}")
    data = {
        "domain": "admin_platform",
        "params":
            {
                "currPage": 1,
                "projectStatus": 1,
                "pageSize": 10,
                "projectId": "",
                "projectName": name
            }
    }
    response = requests.post(url=MAIN_HOST + '/manage/proconfig/projectConfig/getProjectConfigPageList',
                             json=data,
                             headers=gen_manage_headers())
    print(response.json())
    for item in response.json()['data']['list']:
        if item['projectName'] == name:
            return item['id']
    print(response.json())

def get_password():
    """
    :param successData: 从邮件获取用户的密码
    :return:
    """
    email_addr = "<EMAIL>"
    password = "1QAZ2wsx"
    pwd = readMail.print_info(readMail.get_mail(email_addr, password))
    if pwd:
        return encrypt(pwd, '&eU123n5ryx*@Qum')
    else:
        return False


def get_passwordByDynamicKey(DynamicKey):
    """
    :param successData: 从邮件获取用户的密码
    : Authour zuisheng 前后端交互用于邮件密码加密
    :return:
    """
    email_addr = "<EMAIL>"
    password = "1QAZ2wsx"
    pwd = readMail.print_info(readMail.get_mail(email_addr, password))
    if pwd:
        pw = encrypt(pwd, DynamicKey)
        ba = base64.b64encode(bytes(pw, encoding="UTF-8"))
        str2 = str(ba, encoding="utf-8")
        return str2
    else:
        return False


def get_passwordByDynamicKeyAndPwd(DynamicKey, pwd):
    """
    :param successData: 从邮件获取用户的密码
    : Authour zuisheng 前后端交互用于指定密码加密
    :return:
    """
    if pwd:
        pw = encrypt(pwd, DynamicKey)
        ba = base64.b64encode(bytes(pw, encoding="UTF-8"))
        str2 = str(ba, encoding="utf-8")
        return str2
    else:
        return False


def get_passwordByPwd(pwd):
    """
    :param successData: 从邮件获取用户的密码
    : Authour zuisheng 前后端交互用于指定密码加密
    :return:
    """
    if pwd:
        pw = encrypt(pwd, '&eU123n5ryx*@Qum')
        return pw
    else:
        return False


def get_code():
    """
    :param successData: 获取验证码和header
    :return:
    """
    from tools import get_manage_token as gt
    return gt.get_verificationCode()


def get_listByNum(l, n):
    """
    :param successData: 获取list值
    :return:
    """
    return l[n]


def get_parameter_class_by_name(name, code):
    """
    查询参数分类
    name：参数分类名称
    code：参数分类编码
    author:须弥
    """
    print(f"执行get_parameter_class_by_name方法，传入参数{name, code}")
    rs_id = None
    i = 1
    while rs_id is None and i < 10:
        data = {"params": {"outFlag": False, "pageSize": 100, "currPage": i, "parameterClassType": "",
                           "parameterItemParentId": "0", "parameterClassName": f"{name}"}, "domain": "admin_platform"}
        response = requests.post(
            url=MAIN_HOST + '/manage/appconfig/parameter/class/pageParameterClassByParameterClassName',
            json=data,
            headers=gen_manage_headers())
        for item in response.json()['data']['list']:
            if item['parameterClassCode'] == code:
                rs_id = item['id']
                # print(item['id'])
                break
        i += 1
    print(response.json())
    print(rs_id)
    return rs_id


def get_ternary_distribution_id(roleCode):
    """
    根据三元角色code获取id
    Author:易节
    """
    response = requests.post(url=MAIN_HOST + '/manage/rolepermissions/role/getTernaryDistribution', json={},
                             headers=gen_manage_headers())
    for item in response.json()['data']:
        if item['roleCode'] == roleCode:
            return item['id']


def add_list(list, data):
    """
    往list中添加数据
    :param list: 列表   data: 要添加的值
    :return:
    """
    for i in range(len(list)):
        list[i].pop('userMainType')
    list.append(data)
    return list


def check_usercode(usercode, expect):
    """
    检查usercode规则
    """
    assert expect == usercode[0:26] and usercode[26:].isdigit()


def save_parameter_item(data):
    """
    新增参数项
    Author:须弥
    """
    print(f"执行save_parameter_item方法，传入参数{data}")
    response = requests.post(url=MAIN_HOST + '/manage/appconfig/parameter/item/saveParameterItem', json=data,
                             headers=gen_manage_headers())

    if response.json()['message'] == '成功':
        return True
    print(response.json())


def get_parameter_item_by_name(name, classCode, code):
    """
    查询参数项分类
    Author:须弥
    """
    data = {
        "domain": "admin_platform",
        "params":
            {
                "currPage": 1,
                "outFlag": None,
                "pageSize": 100,
                "parameterClassCode": classCode,
                "parameterItemName": name,
                "parameterItemParentId": "",
            }
    }
    print("执行get_parameter_item方法，传入参数{data}")
    response = requests.post(url=MAIN_HOST + '/manage/appconfig/parameter/item/pageParameterItemByParameterItemName',
                             json=data,
                             headers=gen_manage_headers())
    print(response.json())
    for item in response.json()['data']['list']:
        if item['parameterItemCode'] == code:
            return item['id']
    print('查询参数项id失败')

def json_to_str(data):
    """
    将json数据data转换成字符串
    """
    return json.dumps(data)


def add_data(data, key, value):
    """
    往字典表中设置值,并返回新字典
    """
    import copy
    result = copy.copy(data)
    result[key] = value
    return result

def init_message():
    """
    初始化邮件配置
    """
    response = requests.post(url=MAIN_HOST + '/manage/appconfig/message/selectMessageConfigList', json={},
                             headers=gen_manage_headers())
    print(response.json())
    if len(response.json()['data']) == 0:
        message_config = json_to_str({
            "mailHost": "smtp.qq.com",
            "mailPort": "465",
            "mailUsername": "<EMAIL>",
            "mailPassword": "alodiughn",
            "mailProtocol": "smtp",
            "socketFactoryPort": "465",
            "mailTimeout": "3000",
            "sslEnable": "true",
            "mailAuth": "true",
            "testEMail": "<EMAIL>",
            "from": "<EMAIL>"
        })
        data = {
            "domain": "admin_platform",
            "params":
                {
                    "messageStatus": 0,
                    "messageType": 1,
                    "messageConfig": message_config,
                }
        }
        print(f"执行init_message方法，传入参数{data}")
        requests.post(url=MAIN_HOST + '/manage/appconfig/message/updateMessageTypeInfo', json=data, headers=gen_manage_headers())

def connectMysql():
    host = os.environ.get(key='esign.dbHost')
    port = os.environ.get(key='esign.dbPort')
    user = os.environ.get(key='esign.dbUser')
    passwd = os.environ.get(key='esign.dbPassword')
    dbname = os.environ.get(key='esign.db.manage')
    con = connect(host, port, user, passwd, dbname)
    return con


def get_param_by_sql(sql=None, pramskey=None, index=-1, iskg=1):
    """
    数据库查询：
    查询一个符合条件的字段，例如：已删除的用户usercode
    sql：执行的sql
    pramskey:指定字段
    index：指定第几条数据,默认是数据库中最新的一条
    iskg:返回字段是否自动加前后空格，1是加，0是不加
    """
    conn = connectMysql()
    result = execute_sql_convert_array(conn, sql)
    conn.close()
    if len(result) == 0 or result is None:
        print("表中无符合{}条件的数据".format(pramskey))
        return ""
    else:
        if result[index][pramskey] is None:
            print("参数{}为None".format(pramskey))
            return ""
        elif pramskey in ['user_code', 'account_number'] and result[index][pramskey] == 'admin':
            print(result[index + 1][pramskey])
            if iskg == 1:
                return ' ' + result[index + 1][pramskey] + ' '
            return result[index + 1][pramskey]
        else:
            print(result[index][pramskey])
            if iskg == 1:
                return ' ' + result[index][pramskey] + ' '
            return result[index][pramskey]


def listaddvalue(lt, data):
    """
    lt:列表
    data：列表追加的json数据
    return：追加数据后的list
    xumi
    """
    if isinstance(lt, list):
        lt.append(data)

        return lt
    else:
        raise "数据：{}，不是列表格式，请检查".format(lt)



def getRsaKey(msg):
    print("加密")
    # from utils.common import encryptKey
    encryptPhoneOrMail = encryptKey(msg)
    return encryptPhoneOrMail



def get_ip(namespaceId='0ee4f717-27a6-41b0-a09a-09068c9867ea'):
    """
     获取服务ip，端口
     获取ip，端口不传；获取端口，则ip不传
     :param serviceName: nacos上的服务名，例 providers:cn.esign.ka.manage.facade.service.unifiedauth.RpcUserIdentityService
     :param namespaceId: nacos上的命名空间id， 例 0ee4f717-27a6-41b0-a09a-09068c9867ea
     :param ip:
     :param port:
     :return:
     """
    data = requests.get(
        url=f'http://nacos620210308.testk8s.tsign.cn:80/nacos/v1/ns/instance/list?serviceName=providers:cn.esign.ka.manage.facade.service.orguser.org.RpcOrgService::&namespaceId={namespaceId}')
    # serviceName 服务名称
    # print(data.json())
    return data.json()['hosts'][0]['ip']

def deleteInnerUsers(userName,userCode=None):
    """
    通过用户名称或是code删除符合条件的用户
    """
    from utils.esign6Manage import delete_inner_user_by_name
    delete_inner_user_by_name(userName,userCode)

def deleteInnerOrgs(orgName,orgCode=None):
    """
    通过企业名称或是企业code删除符合条件的企业
    """
    from utils.esign6Manage import delete_inner_org_by_name
    delete_inner_org_by_name(orgName,orgCode)

def deleteOuterOrgs(orgName,orgCode=None):
    """
    通过企业名称或是企业code删除符合条件的相对方企业
    """
    from utils.esign6Manage import delete_outer_org_by_name
    delete_outer_org_by_name(orgName,orgCode)

def getDateTime(day=0, timeFormat=None):
    """
    获取时间、日期
    :param timeFormat: int  1-'%Y-%m-%d %H:%M:%S' 、2-'%Y-%m-%d'、3-'%Y-%m-%d %H:%M:%S.%f'
    :param day: int 距今天多少天，传负数则为过去时间
    :return: 默认返回当前时间到微秒级 20220616163258.767022 可作唯一字符串，防重
    """
    from utils.dateAndTime import somedayTime
    return str(somedayTime(day, timeFormat))

def getHeaderVerificationCode():
    """
    获取图形验证码的请求头里的VerificationCode
    :return:
    """
    from utils.esignToken import createVerificationCode
    return createVerificationCode()

def getAppDetailsByAppName(appName):
    """应用中心-我的应用，通过appName获取应用详情"""
    from tools import get_manage_token as gt
    headers = {
        "Esa-Token-Source": "MANAGE",
        "Esa-Token": gt.token,
        "Content-Type": "application/json;charset=UTF-8"
    }
    #根据应用状态随机获取一个应用名称并通过接口获取对应的uuid,用于后续的安装、启用、卸载、删除
    data={
        "appCategory": [],
        "appName": appName,
        "appStatus": [],
        "pageIndex": 1,
        "pageSize": 20
    }

    response = requests.post(url=MAIN_HOST + '/etl-integrate/v1/lc/manage/app/list',
                         json=data,
                         headers=headers)
    try:
        res=response.json()
        appObj=res['data']['data'][0]
        return appObj
    except Exception as e:
        raise SystemError(f"通过{appName}查询应用失败")

def getUuidByAppName(appName):
    appObj=getAppDetailsByAppName(appName)
    return appObj["uuid"]

def getStatusByAppName(appName):
    appObj=getAppDetailsByAppName(appName)
    return appObj["appStatus"]

# 上传文件
def get_file2(*filePath):
    """
    打开/data目录下的目录文件，上传文件接口请求体
    :param filePath: 多级文件目录
    :return: bytes
    """
    base_path = os.path.dirname(__file__)
    file_path = os.path.join(base_path, 'data', *filePath)
    with open(file_path, "rb") as file:
        file_contents = file.read()
    return file_contents

#包装响应数据
def responseWrapper(response,appName):
    #异步查询状态，需要需要等待一会
    time.sleep(5)
    appObj = getAppDetailsByAppName(appName)
    response.json["appStatus"]=appObj["appStatus"]
    return response

def calculate_sha256(file_name):
    base_path = os.path.dirname(__file__)
    file_path = os.path.join(base_path, 'data', file_name)
    with open(file_path, 'rb') as file:
        content = file.read()
        sha256_hash = hashlib.sha256(content)
        return sha256_hash.hexdigest()

def getTokenByAccount(account,password):
    """
    根据账号密码获得管理平台账号的登录态
    """
    res = createManageToken(account,password)
    if res == None:
        res = createManageToken(account, password)
    return res

def putTempEnv(key, value):
    """
    临时存入环境变量，取值方式${ENV(key)}
    :param key: 自定义key，避免与.env里的key重复
    :param value:
    :return:
    """
    os.environ[key] = value

# 生成一个 32 位的 16 进制字符串
def generate_random_hex_string(length):
    # 生成一个包含 0-9 和 a-f 范围内字符的字符串
    hex_chars = "0*********abcdef"
    result = ""
    for _ in range(length):
        # 随机选择一个 16 进制字符并添加到结果字符串中
        result += hex_chars[random.randint(0, 15)]
    return result

def getCloudRealNameAndAuth(type, params):
    """
    SAAS 个人和企业实名的各种场景
    :param type: 自定义的场景值
    :param params: {"mobile": "","orgName": ""}
    :return: []
    """
    # 自定义方法，调用时引入
    from utils.cloudScene import cloudRealNameAndAuthScene
    return cloudRealNameAndAuthScene(type, params)

# 获取数组中某个对象的某个属性值
def get_attr(obj, index, key):
    if len(obj) >= index + 1:
        print("属性值：" + key + "=" + obj[index][key])
        return obj[index][key]
    else:
        print("对象值为空：" + key + "= ''")
        return ""


# 获取对象的key属性值
def get_obj_attr(obj, key):
    return obj[key]

def is_empty(value):
    if len(value) == 0:
        return True
    else:
        return False


def is_not_empty(value):
    if len(value) == 0:
        return False
    else:
        return True
# 判断数组中某个对象的某个属性是空值
def attr_empty(obj, index, key):
    value = get_attr(obj, index, key)
    return is_empty(value)


def attr_not_empty(obj, index, key):
    value = get_attr(obj, index, key)
    return is_not_empty(value)


# 获取数组中对象的某属性集合
def attrs_from_collection(collection, key):
    results = []
    if len(collection) >= 1:
        for element in collection:
            results.append(element[key])
    print(results)
    return results


# 获取随机数值列表: len 列表长度 eleMinLen 元素最小长度， eleMaxLen 元素最大长度
def random_str_collection(len, eleMinLen, eleMaxLen):
    results = []
    for index in range(len):
        eleLen = random.randint(eleMinLen, eleMaxLen)
        results.append(generate_random_str(eleLen))
    print(results)
    return results


# 获取url中的某个参数
def get_url_param(urlStr, key):
    if urlStr == "":
        return ""
    # url 解码
    try:
        from urllib import parse
        urlData = parse.unquote(urlStr)
        print(urlData)
        params = parse.parse_qs(parse.urlparse(urlData).query)
        result = params[key]
        print("url参数：" + key + "=" + result[0])
        return result[0]
    except KeyError as e:
        print("字典中不存在该参数" + key)
        return ""


# 获取列表中的url参数
def parse_url_param_from_list(list, key):
    result = []
    for item in list:
        target_param = get_url_param(item, key)
        result.append(target_param)
    return result


# 如果 results[n]的字段condition_key == condition_value, 返回 results[n]的 return_key 字段的值
def return_target_value(results, condition_key, condition_value, return_key):
    for item in results:
        if item[condition_key] == condition_value:
            print("找到目标对象值：" + item[return_key])
            return item[return_key]
    print("未到目标对象值, 返回空字符串")
    return ""


def has_target_value(results, condition_key, condition_value, return_key):
    for item in results:
        if item[condition_key] == condition_value:
            print("找到目标对象值：" + item[return_key])
            return True
    print("未到目标对象值, 返回空字符串")
    return False


# 如果 results[n]的字段category == a, 返回 list[n]的 name 字段的值
def return_item_value(results, condition_key, condition_value, retrun_key):
    for i in range(len(results)):
        if results[i][condition_key] == condition_value:
            return results[i][retrun_key]
    return False


def replace_all(originStr, seq, targetseq):
    if originStr != None:
        originStr = originStr.replace(seq, targetseq, originStr.count(seq))
    print(originStr)
    return originStr


def choice_not_empty(a, b):
    if (len(a) == 0 or a == None):
        return b
    else:
        return a

# 格式化当前时间
def getFormatTime(days, type):
    seconds = datetime.timedelta(days).total_seconds()
    time_now = time.time()
    result = 0
    if "add" == type:
        result = time_now + seconds
    else:
        result = time_now - seconds
    result = time.localtime(result)
    time_str = time.strftime("%Y-%m-%d %H:%M:%S", result)
    print("目标时间：" + time_str)
    return time_str


# 格式化当前时间
def getFormatTimeNoHMS(days, type):
    seconds = datetime.timedelta(days).total_seconds()
    time_now = time.time()
    result = 0
    if "add" == type:
        result = time_now + seconds
    else:
        result = time_now - seconds
    result = time.localtime(result)
    time_str = time.strftime("%Y-%m-%d", result)
    print("目标时间：" + time_str)
    return time_str

def getmodelId(modelId):
    seq = modelId.split(':')
    return seq[0]

def CreateC9(code):
    # 第i位置上的加权因子
    weighting_factor = [3, 7, 9, 10, 5, 8, 4, 2]
    # 第9~17位为主体标识码(组织机构代码)
    organization_code = code[8:17]
    # 本体代码
    ontology_code = organization_code[0:8]
    # 生成校验码
    tmp_check_code = gen_check_code(
        weighting_factor, ontology_code, 11, ORGANIZATION_CHECK_CODE_DICT)
    return code[:16] + tmp_check_code


def getSocialCreditCode(code):
    code = CreateC9(code[:16])
    # 第i位置上的加权因子
    weighting_factor = [1, 3, 9, 27, 19, 26, 16,
                        17, 20, 29, 25, 13, 8, 24, 10, 30, 28]
    # 本体代码
    ontology_code = code[0:17]
    # 计算校验码
    tmp_check_code = gen_check_code(
        weighting_factor, ontology_code, 31, SOCIAL_CREDIT_CHECK_CODE_DICT)
    return code[:17] + tmp_check_code


def gen_check_code(weighting_factor, ontology_code, modulus, check_code_dict):
    total = 0
    for i in range(len(ontology_code)):
        if ontology_code[i].isdigit():
            total += int(ontology_code[i]) * weighting_factor[i]
        else:
            total += check_code_dict[ontology_code[i]
                     ] * weighting_factor[i]
    C9 = modulus - total % modulus
    C9 = 0 if C9 == 31 else C9
    C9 = list(check_code_dict.keys())[
        list(check_code_dict.values()).index(C9)]
    return C9


def getdistrictcode():
    with open('districtcode') as file:
        data = file.read()
    districtlist = data.split('\n')
    global codelist
    codelist = []
    for node in districtlist:
        # print node
        if node[10:11] != ' ':
            state = node[10:].strip()
        if node[10:11] == ' ' and node[12:13] != ' ':
            city = node[12:].strip()
        if node[10:11] == ' ' and node[12:13] == ' ':
            district = node[14:].strip()
            code = node[0:6]
            codelist.append({"state": state, "city": city, "district": district, "code": code})

#查询不同状态的印章，certStatus 证书状态(1正常 2申请中 3已过期 4吊销)
# algorithm 算法类型(1-rsa 2-sm2)
# deleted 0否 1 是
# 查询个人不同状态证书
def select_personal_certId(userCode, certStatus,certAlgorithm,deleted):
    try:
        conn = base_execute_db()
        cur = conn.cursor()
        selectSql ="SELECT id FROM smc_personal_cert WHERE user_code = '" + userCode + "' AND cert_status =" + str(certStatus)  + " AND cert_algorithm =" + str(certAlgorithm) + " AND deleted =" + str(deleted) +" AND cert_type = 1 LIMIT 1"
        print(selectSql)
        cur.execute(selectSql)
        result = cur.fetchall()
        certId = result[0][0]
        print(certId)
        conn.commit()
        conn.cursor()
        conn.close()
        return certId
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None
#查询不同状态的印章，certStatus 证书状态(1正常 2申请中 3已过期 4吊销)
# algorithm 算法类型(1-rsa 2-sm2)
# deleted 0否 1 是
# 查询企业不同状态证书
def select_enterprise_certId(organizationCode, certStatus,certAlgorithm,deleted):
    try:
        conn = base_execute_db()
        cur = conn.cursor()
        selectSql ="SELECT id FROM smc_enterprise_cert WHERE organization_code = '" + organizationCode + "' AND cert_status =" + str(certStatus)  + " AND cert_algorithm =" + str(certAlgorithm) + " AND deleted =" + str(deleted) +" AND cert_type = 1 order by create_time desc LIMIT 1"
        print(selectSql)
        cur.execute(selectSql)
        result = cur.fetchall()
        certId = result[0][0]
        print(certId)
        conn.commit()
        conn.cursor()
        conn.close()
        return certId
    except:
        print(sys._getframe().f_code.co_name + "-调用异常")
        return None

def update_seal_apply_status(seal_apply_id):
    print("sealApplyId: ", seal_apply_id)
    # conn = mdb.connect(host="test-nodeport.tsign.cn", port=32103, user="root", password="Q0ct4ab3gP1", db="esign6_seals")
    conn = base_execute_db()
    cur = conn.cursor()
    updateSql = "UPDATE smc_seal_apply SET seal_apply_status = 4 WHERE id = {seal_apply_id}".format(
        seal_apply_id=seal_apply_id)
    print(updateSql)
    cur.execute(updateSql)
    result = cur.fetchall()
    print(result)
    conn.commit()
    conn.cursor()
    conn.close()

# 更改流程的催办时间
def update_urge_time(workflow_instance_id):
    print("workflow_instance_id: ", workflow_instance_id)
    import pymysql as mdb
    # conn = mdb.connect(host="test-nodeport.tsign.cn", port=32103, user="root", password="Q0ct4ab3gP1", db="esign6_seals")
    conn = mdb.connect(host=ENV("esign.dbHost"), port=int(ENV("esign.dbPort")),
                       user=ENV("esign.dbUser"), password=ENV("esign.dbPassword"),
                       db=ENV("esign.db.manage"))
    cur = conn.cursor()
    updateSql = "UPDATE wf_my_start SET urge_time=null WHERE workflow_instance_id = '{workflow_instance_id}'".format(
        workflow_instance_id=workflow_instance_id)
    print(updateSql)
    cur.execute(updateSql)
    result = cur.fetchall()
    print(result)
    conn.commit()
    conn.cursor()
    conn.close()


def update_person_cert_end_time(person_cert_id):
    print("personCertId: ", person_cert_id)
    # conn = mdb.connect(host="test-nodeport.tsign.cn", port=32103, user="root", password="Q0ct4ab3gP1", db="esign6_seals")
    conn = base_execute_db()
    cur = conn.cursor()
    updateSql = "UPDATE smc_personal_cert SET end_time = start_time,cert_status = '3' WHERE id = {person_cert_id}".format(
        person_cert_id=person_cert_id)
    print(updateSql)
    cur.execute(updateSql)
    result = cur.fetchall()
    print(result)
    conn.commit()
    conn.cursor()
    conn.close()


def update_enterprise_cert_end_time(enterprise_cert_id):
    print("enterpriseCertId: ", enterprise_cert_id)
    # conn = mdb.connect(host="test-nodeport.tsign.cn", port=32103, user="root", password="Q0ct4ab3gP1", db="esign6_seals")
    conn = base_execute_db()
    cur = conn.cursor()
    updateSql = "UPDATE smc_enterprise_cert SET end_time = start_time,cert_status = '3' WHERE id = {enterprise_cert_id}".format(
        enterprise_cert_id=enterprise_cert_id)
    print(updateSql)
    cur.execute(updateSql)
    result = cur.fetchall()
    print(result)
    conn.commit()
    conn.cursor()
    conn.close()


# 修改用印申请为可补签状态
def teardown_hooks_update_seal_apply(response):
    seal_apply_id = response.json["message"]
    print("sealApplyId: ", seal_apply_id)
    # conn = mdb.connect(host="test-nodeport.tsign.cn", port=32103, user="root", password="Q0ct4ab3gP1", db="esign6_seals")
    conn = base_execute_db()
    cur = conn.cursor()
    updateSql = "UPDATE smc_seal_apply SET seal_apply_status = 4 WHERE id = {seal_apply_id}".format(
        seal_apply_id=seal_apply_id)
    print(updateSql)
    cur.execute(updateSql)
    result = cur.fetchall()
    print(result)
    conn.commit()
    conn.cursor()
    conn.close()


def update_seal_change_seal_status(old_seal_code, new_seal_code):
    # conn = mdb.connect(host="test-nodeport.tsign.cn", port=32103, user="root", password="Q0ct4ab3gP1", db="esign6_seals")
    conn = base_execute_db()
    cur = conn.cursor()
    updateSql2 = "UPDATE smc_seal_change SET deleted = 1 where  id in (select  seal_change_id from smc_seal_change_details WHERE old_seal_code = '{old_seal_code}')".format(
        old_seal_code=old_seal_code)
    updateSql = "UPDATE smc_seal_change_details SET deleted = 1 WHERE old_seal_code = '{old_seal_code}'".format(
        old_seal_code=old_seal_code)
    updateSql1 = "UPDATE smc_seal_change_details SET deleted = 1 WHERE new_seal_code = '{new_seal_code}'".format(
        new_seal_code=new_seal_code)
    print(updateSql2)
    cur.execute(updateSql2)
    cur.execute(updateSql)
    cur.execute(updateSql1)
    result = cur.fetchall()
    print(result)
    conn.commit()
    conn.cursor()
    conn.close()


# 删除用印宝
def update_device_status(id):
    print("deviceId =========>" + id)
    conn = base_execute_db()
    cur = conn.cursor()
    updateSql = "UPDATE smc_device SET deleted='1' WHERE id = {id}".format(id=id)
    print(updateSql)
    cur.execute(updateSql)
    result = cur.fetchall()
    print(result)
    conn.commit()
    conn.cursor()
    conn.close()

def createOrgSeals(organizationCode,sealPattern=None,legalSealId=None,sealRelease=None,userCode=None,needSeals=None,sealGroupName=None):
    """
    创建企业印章
    :param userCode:
    :param organizationCode:
    :param sealPattern:
    :param legalSealId:
    :param sealRelease:
    :param needSeals: #是否需要创建印章，不创建印章就是创建一个空分组
    :return:
    """
    if legalSealId == "":
        sealTypeCode = "COMMON-SEAL"
        sealGroupName = "全链路制章公章（勿动）"
    else:
        sealTypeCode = "LEGAL-PERSON-SEAL"
        sealGroupName = "全链路制章法人章（勿动）"
    if sealRelease == None:
        sealRelease = 1 #制作后是否直接发布 0否，1是
    if sealPattern == None:
        sealPattern = 1 #印章形态 1-云国际标准印章 2-物理印章 3-云中国标准印章
    if needSeals == False:
        sealInfos = []
        sealGroupName = sealGroupName
    else:
        sealInfos = [{ "sealPattern": sealPattern, "legalSealId": legalSealId }]
    obj1 = {
          "sealTypeCode": sealTypeCode,
          "sealRelease": sealRelease,
          "organizationCode": organizationCode,
          "sealGroupName": sealGroupName,
          "userCode": userCode,
          "sealInfos": sealInfos
    }
    headers = {'x-timevale-project-id': PROJECT_ID, 'x-timevale-signature': openApiSignature(obj1)}
    response = requests.post(url=OPENAPI_HOST + '/seals/v1/sealcontrols/organizationSeals/create',
                             json=obj1, headers=headers)
    json_response = response.json()
    print('createOrgSeals-[request]:', obj1)
    print('createOrgSeals-[respone]:', json_response)
    return json_response



# 数据库查询对应的导航栏navid值
def Get_navID(navigation_code):
    print("导航栏编码为: " + navigation_code)
    import pymysql as mdb
    conn = mdb.connect(host="test-nodeport.tsign.cn", port=32103, user="root", password="Q0ct4ab3gP1",
                       db="stable_esign6_manage")
    selectSql = "SELECT * FROM ec_navigation ea where navigation_code= " + "'" + navigation_code + "'"
    print(selectSql)
    cur = conn.cursor()
    cur.execute(selectSql)
    result = cur.fetchall()
    id = result[0][0]
    conn.commit()
    cur.close()
    conn.close()
    return id


# 电子印章列表
def gen_token_header_permissions_electronic(accountNumber: object, password: object) -> object:
    projectId = PROJECT_ID
    navigation_code = 'SMC-YZGL-QYYZGL'
    print(navigation_code)
    navId = Get_navID(navigation_code)
    print(navId)
    authorization = PORTAL_TOKEN
    headers = {"Content-Type": "application/json", "x-timevale-project-id": projectId, "authorization": authorization}
    headers['deptId'] = ENV("deptId")
    headers['navId'] = navId
    return headers

def gen_token_header_file(accountNumber, password):
    projectId = ENV("esign.projectId")
    authorization = PORTAL_TOKEN
    headers = {"x-timevale-project-id": projectId, "authorization": authorization}
    headers['deptId'] = ENV("deptId")
    return headers

# 上传文件
def get_file(fileName):
    """
    打开tests/data目录下的文件，上传文件接口请求体
    :param fileName: 文件名称
    :return: bytes
    """
    basePath = os.path.dirname(__file__)
    filePath = os.path.join(basePath, 'data', fileName)
    with open(filePath, "rb") as file:
        file_contents = file.read()
    return file_contents

def before_after_space_str(randomlength):
    """
    生成一个指定长度的中间空格随机字符串
    """
    random_str = ''
    base_str = 'ABCDEFGHIGKLMNOPQRSTUVWXYZabcdefghigklmnopqrstuvwxyz0*********'
    length = len(base_str) - 1
    for i in range(randomlength):
        random_str += base_str[random.randint(0, length)]
    return " " + random_str + " "

def str_insert_space(str, index):
    """
    传入一个字符串，返回中间带空格的字符串
    index: 1 前 2中 3后
    """
    if index == 1:
        str = " " + str
    elif index == 2:
        middle = round(len(str) / 2)
        length = len(str)
        str = str[0:middle] + "  " + str[middle:length]
    elif index == 3:
        str = str + " "
    print("str=" + str)
    return str

# 根据response删除印章类型
def teardown_hook_delete_sealType(response):
    if response.json["status"] == 200:
        print("印章类型id为：" + response.json["message"])
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/sealtype/deleteSealType"
        data = {"params": {"id": response.json["message"]}, "domain": "seal_system"}
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        print("删除印章类型接口入参为：" + str(data))
        res = requests.post(url=Url, headers=headers, json=data)
        print("删除印章类型接口响应信息为：" + str(json.loads(res.text)))
        return json.loads(res.text)["message"]
    else:
        print("删除印章类型接口调用失败，message:" + response.json["message"])


# 根据印章类型id删除印章类型
def delete_sealType(sealTypeId):
    print("入参印章类型id为：" + sealTypeId)
    authorization = PORTAL_TOKEN
    Url = ENV("esign.projectHost") + "/seals/smc/seals/sealtype/deleteSealType"
    data = {"params": {"id": sealTypeId}, "domain": "seal_system"}
    headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
               "authorization": authorization}
    print("删除印章类型接口入参为：" + str(data))
    res = requests.post(url=Url, headers=headers, json=data)
    print("删除印章类型接口响应信息为：" + str(json.loads(res.text)))
    return json.loads(res.text)["message"]


def create_sealType():
    print("===开始执行创建印章类型接口===")
    authorization = PORTAL_TOKEN
    Url = ENV("esign.projectHost") + "/seals/smc/seals/sealtype/saveSealType"
    sealTypeName = "temp" + generate_random_str(5)
    sealTypeCode = sealTypeName
    data = {"params": {"id": "", "sealTypeName": sealTypeName, "sealTypeCode": sealTypeCode, "sealTypeStatus": "0"},
            "domain": "seal_system"}
    print("执行印章类型创建的接口入参为：" + str(data))
    headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
               "authorization": authorization}
    res = requests.post(url=Url, headers=headers, json=data)
    print("执行印章类型添加的接口响应信息为：" + str(json.loads(res.text)))
    return json.loads(res.text)["message"]



# 根据userCode获取用户详情，根据key值获取需要用到的value值
# 常用key值说明：customAccountNo：用户编码 name:姓名 mobile：手机号 licenseNo：身份证号码
def getInnerUserValue(userCode, param):
    try:
        userInfo = detailInnerUsers(ENV("esign.gatewayHost"), ENV("esign.projectId"),
                                    ENV("esign.projectSecrect"), userCode)

        for key in userInfo:
            if key == param:
                return userInfo[key]
    except:
        return None


from tools.esignOrganizationUser import detailInnerUsers, createInnerUserCode, deleteInnerUserCode, updateInnerUserName, \
    createInnerOrgCode, updateInnerOrgName, deleteInnerOrg, createInnerLegalOrgCode, createInnerUserCodeOtherOrg

# 创建用户
def getInnerUserCode(name):
    try:
        userCode = createInnerUserCode(ENV("esign.gatewayHost"), ENV("esign.projectId"),
                                       ENV("esign.projectSecrect"),
                                       name, ENV("csqs.orgCode"))
        return userCode
    except:
        print("getInnerUserCode method error")
        return None


# 创建内部用户，organizationCode必填
def getInnerUserCode2(name, organizationCode):
    try:
        userCode = createInnerUserCode(ENV("esign.gatewayHost"), ENV("esign.projectId"),
                                       ENV("esign.projectSecrect"),
                                       name, organizationCode)
        return userCode
    except:
        print("getInnerUserCode method error")
        return None


# 创建兼职企业内部用户
def getInnerUserOtherOrg(name, otherOrganizationCode):
    otherOrganization = [
        {"otherOrganizationCode": otherOrganizationCode}
    ]
    userCode = createInnerUserCodeOtherOrg(ENV("esign.gatewayHost"), ENV("esign.projectId"),
                                           ENV("esign.projectSecrect"),
                                           name, ENV("csqs.orgCode"), otherOrganization)
    return userCode


# 删除用户
def deleteUserCode(userCode):
    try:
        deleteInnerUserCode(ENV("esign.gatewayHost"), ENV("esign.projectId"), ENV("esign.projectSecrect"),
                            userCode)
    except:
        print("deleteUserCode method error")


# 创建用户并删除
def createAndDelUser(name):
    try:
        userCode = getInnerUserCode(name)
        deleteUserCode(userCode)
        return userCode
    except:
        print("getDeleteInnerUserCode method error")
        return None


# 更新用户名称
def updateUserName(userCode, updateName):
    updateInnerUserName(ENV("esign.gatewayHost"), ENV("esign.projectId"), ENV("esign.projectSecrect"),
                        userCode, updateName)


# 创建机构
def getInnerOrganizationCode(name):
    organizationCode = createInnerOrgCode(ENV("esign.gatewayHost"), ENV("esign.projectId"),
                                          ENV("esign.projectSecrect"), name)
    return organizationCode


# 创建机构-返回组织信息
def getInnerOrganization(name):
    organizationdata = createInnerOrgCode(ENV("esign.gatewayHost"), ENV("esign.projectId"),
                                          ENV("esign.projectSecrect"), name)
    return organizationdata


# #创建机构-返回组织name
# def getInnerOrganizationCode(name):
#     organizationName = createInnerOrgCode(ENV("esign.gatewayHost"), ENV("esign.projectId"), ENV("esign.projectSecrect"),name)
#     return organizationName

# 创建机构带法人
def getInnerLegalOrganizationCode(name, legalRepUserCode):
    organizationCode = createInnerLegalOrgCode(ENV("esign.gatewayHost"), ENV("esign.projectId"),
                                               ENV("esign.projectSecrect"), name, legalRepUserCode)
    return organizationCode


# 更新机构名称
def updateOrganizationName(organizationCode, updateOrgName):
    updateInnerOrgName(ENV("esign.gatewayHost"), ENV("esign.projectId"), ENV("esign.projectSecrect"),
                       organizationCode, updateOrgName)


# 删除机构
def deleteInnerOrganization(organizationCode):
    deleteInnerOrg(ENV("esign.gatewayHost"), ENV("esign.projectId"), ENV("esign.projectSecrect"),
                   organizationCode)


# 给特定用户添加、编辑个人章
def saveOrUpdatePersonalSeal(sealName, sealStatus, sealId, userCode, userName, managerOrgCode, managerOrgName):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/personal/savePersonalSeal"
        if userCode is None:
            userCode = ENV("userCode")
        if userName is None:
            userName = ENV("userName")
        if managerOrgCode is None:
            managerOrgCode = ENV("csqs.orgCode")
        if managerOrgName is None:
            managerOrgName = ENV("csqs.orgName")
        from utils.esign6Seals import gen_saveOrUpdatePersonalSeal_data
        data = gen_saveOrUpdatePersonalSeal_data(sealId, sealStatus, userCode, userName, managerOrgCode,
                                                 managerOrgName, sealName)
        print("执行创建印章的请求信息为：" + str(data))
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        print("执行创建印章的接口响应信息为：" + str(json.loads(res.text)))
        return json.loads(res.text)["message"]
    except:
        print("saveOrUpdatePersonalSeal method error")
        return None


# 删除印章
def deletePersonSeal(sealId, response):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/personal/deletePersonalSeal"
        if response is not None:
            if response.json["status"] == 200:
                data = {"params": {"id": response.json["message"]}, "domain": "seal_system"}
        if sealId is not None:
            data = {"params": {"id": sealId}, "domain": "seal_system"}
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        print("执行删除印章的接口响应信息为：" + str(json.loads(res.text)))
    except:
        print("deletePersonSeal method error")


# 查询用户userId
def getInnerUserId(userName, organizationId):
    try:
        if organizationId is None:
            organizationId = "0"
        Url = ENV("esign.projectHost") + "/manage/orguser/user/getUserByOrganization"
        data = {
            "params": {
                "organizationId": organizationId,
                "currPage": 1,
                "pageSize": 10,
                "userName": userName,
                "allChildOrganizationFlag": True,
                "userStatusList": ["1"]
            },
            "domain": "admin_platform"
        }
        headers = {"Content-Type": "application/json",
                   "token":MANAGE_TOKEN }
        res = requests.post(url=Url, json=data, headers=headers)
        print("接口响应信息为：" + str(json.loads(res.text)))
        if res.json()['data']:
            return res.json()['data']['list'][0]['id']
    except:
        print("getInnerUserId methond error")
        return None


# 离职内部用户
def addUserDimission(userId):
    Url = ENV("esign.projectHost") + "/manage/orguser/dimission/addUserDimission"
    data = {
        "params": {
            "userDimissionId": userId,
            "userReceiveId": "",
            "handoverStatus": "2",
            "handoverDate": "",
            "handoverDesc": "",
            "needToResign": "2"
        },
        "domain": "admin_platform"
    }
    headers = {"Content-Type": "application/json", "token": MANAGE_TOKEN}
    res = requests.post(url=Url, json=data, headers=headers)
    print("离职用户接口响应信息为：" + str(json.loads(res.text)))


# 获取印章类型id
def getSealTypeId(sealTypeName):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/sealtype/getSmcSealTypeList"
        data = {
            "params": {"isShowPersonalSeal": "1"}, "domain": "seal_system"
        }
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        print("执行印章类型列表接口的接口响应信息为：" + str(json.loads(res.text)))
        resData = res.json()['data']
        for i in resData:
            if i['sealTypeName'] == sealTypeName:
                return i['id']
    except:
        print("getSealTypeId method error")
        return None

# 删除印章管理员
def deleteSealManager(id, response):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/sealmanager/deleteSealManager"
        if response is not None:
            if response.json["status"] == 200:
                data = {"params": {"id": response.json["message"]}, "domain": "seal_system"}
        if id is not None:
            data = {"params": {"id": id}, "domain": "seal_system"}
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        print("删除印章管理员接口入参为：" + str(data))
        res = requests.post(url=Url, headers=headers, json=data)
        print("删除印章管理员接口响应信息为：" + str(json.loads(res.text)))
        return json.loads(res.text)["message"]
    except:
        print("deleteSealManager method error")
        return None


# 获取企业organizationId
def getOrganizationId(organizationName, organizationCode):
    try:
        Url = ENV("esign.projectHost") + "/manage/orguser/org/getOrganizationListByOrgCodeName"
        data = {
            "params": {"organizationName": organizationName, "organizationCode": organizationCode,
                       "organizationTerritory": "1"}, "domain": "admin_platform"
        }
        headers = {"Content-Type": "application/json", "token": MANAGE_TOKEN}
        res = requests.post(url=Url, json=data, headers=headers)
        print(sys._getframe().f_code.co_name + "-[respone]: " + str(res.json()))
        return res.json()['data'][0]['id']
    except:
        print("getOrganizationId mehtod error")
        return None


# 保存、编辑、变更后保存企业电子印章
def saveElectronicSeal(organizationCode, organizationName, organizationId, sealTypeCode, sealTypeName, sealStatus,
                       sealName, id):
    try:
        authorization = PORTAL_TOKEN
        if sealName is None:
            sealName = "自动化电子印章" + generate_random_str(8)
        Url = ENV("esign.projectHost") + "/seals/smc/seals/enterprise/electronic/saveElectronicSeal"
        data = {
            "params": {
                "id": id,
                "organizationCode": organizationCode,
                "organizationName": organizationName,
                "organizationId": organizationId,
                "sealBodyStructure": "1",
                "sealBottomword": "",
                "sealCenterImg": "2",
                "sealCode": "YZZDHRCBXYSPDZYZ",
                "sealColour": "1",
                "sealHeight": 30,
                "sealWidth": 45,
                "oldStyle": None,
                "sealName": sealName,
                "sealOpacity": 1,
                "sealShape": "2",
                "sealSource": "2",
                "sealStatus": sealStatus,
                "sealSurroundword": organizationName,
                "sealTypeCode": sealTypeCode,
                "sealTypeName": sealTypeName,
                "sealChargeOrganizationCode": organizationCode,
                "sealChargeOrganizationName": organizationName,
                "workFlowFlag": "2",
                "sealUserList": []
            },
            "domain": "seal_system"
        }
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        print("执行保存企业电子印章接口的接口响应信息为：" + str(json.loads(res.text)))
        return json.loads(res.text)["message"]
    except:
        print("saveElectronicSeal method error")


# 保存、编辑、变更后保存企业物理印章
def savePhysicalSeal(organizationCode, organizationName, organizationId, sealTypeCode, sealStatus, sealName, id):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/enterprise/physical/makingseals/savePhysicalSeal"
        data = {
            "params": {
                "id": id,
                "organizationCode": organizationCode,
                "originalSealId": "",
                "organizationId": organizationId,
                "organizationName": organizationName,
                "physicalSealBottomword": sealName,
                "physicalSealCode": "YZZDHWLYZRC",
                "physicalSealMaterial": "1",
                "sealModelUrlList": [],
                "physicalSealName": sealName,
                "physicalSealShape": "1",
                "physicalSealStatus": sealStatus,
                "physicalSealSurroundword": sealName,
                "sealTypeCode": sealTypeCode,
                "sealModelFileCode": "YZGL-QYWLYZ",
                "sealManagerOrganizationCode": organizationCode,
                "sealManagerOrganizationName": organizationName,
                "workflowId": "",
                "workFlowFlag": "2"
            },
            "domain": "seal_system"
        }
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        print("执行保存企业物理印章接口的接口响应信息为：" + str(json.loads(res.text)))
        return json.loads(res.text)["message"]
    except:
        print("saveOrUpdatePhysicalSeal method error")
        return None

# 删除物理企业章
def deletePhysicalSeal(id, response):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/enterprise/physical/makingseals/deletePhysicalSeal"
        if response is not None:
            if response.json["status"] == 200:
                data = {"params": {"id": response.json["message"]}, "domain": "seal_system"}
        if id is not None:
            data = {
                "params": {"id": id}, "domain": "seal_system"
            }
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        print("执行物理印章接口的接口响应信息为：" + str(json.loads(res.text)))
        return json.loads(res.text)["message"]
    except:
        print("deletePhysicalSeal method error")


# 执行sql
def executeSql(sql):
    try:
        con = base_execute_db()
        cursor = con.cursor()
        if cursor == None:
            print('cursor 数据库连接异常！')
        elif sql != None:
            cursor.execute(sql)
            con.commit()
            cursor.close()
            con.close()
    except Exception:
        print('execute 数据库执行异常！' + sql)


# 查询sql
def get_execute_sql(sql):
    con = base_execute_db()
    cursor = con.cursor()
    if cursor == None:
        print('cursor 数据库连接异常！')
    elif sql != None:
        cursor.execute(sql)
        sqlQuery = cursor.fetchall()
        if sqlQuery:
            return sqlQuery[0][0]


# 数据库统一封装
def base_execute_db():
    try:
        # host = "test-nodeport.tsign.cn"
        # user = "root"
        # password = "Q0ct4ab3gP1"
        # db = "esign6_seals"
        # port = 32103
        # return mdb.connect(host="test-nodeport.tsign.cn", port=32103, user="root", password="Q0ct4ab3gP1", db="esign6_seals")
        # 数据库信息从配置文件中获取
        import pymysql as mdb
        return mdb.connect(host=ENV("esign.dbHost"), port=int(ENV("esign.dbPort")),
                           user=ENV("esign.dbUser"), password=ENV("esign.dbPassword"),
                           db=ENV("esign.db.seals"))
    except Exception:
        print('execute 数据库连接异常！')
        return None


# 解绑物理印章
def unbindPhysicalSeal(id, response):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/enterprise/electronic/unbindPhysicalSeal"
        if response is not None:
            if response.json["status"] == 200:
                data = {"params": {"id": response.json["message"]}, "domain": "seal_system"}
        if id is not None:
            data = {
                "params": {"id": id}, "domain": "seal_system"
            }
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        headers['navId'] = ENV("electronic_navId")
        res = requests.post(url=Url, headers=headers, json=data)
        print("执行解绑物理印章的接口响应信息为：" + str(json.loads(res.text)))
        return json.loads(res.text)["message"]
    except:
        print("unbindPhysicalSeal method error")


# 创建个人云证书（在线），返回证书id
def createPersonalCert(data):
    authorization = PORTAL_TOKEN
    Url = ENV("esign.projectHost") + "/seals/smc/certs/personal/savePersonalCert"
    headers = {"Content-Type": "application/json", "authorization": authorization}
    res = requests.post(url=Url, headers=headers, json=data)
    print("创建个人证书接口响应信息：" + str(json.loads(res.text)))
    return json.loads(res.text)["data"]


# 将个人证书状态变更为已过期，入参certId,
def update_certStatusToExpired(certId):
    print("需要变更状态的certId为: " + certId)
    conn = base_execute_db()
    cur = conn.cursor()
    current_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time()))
    updateSql = "UPDATE smc_personal_cert SET cert_status = '3',start_time =" + "\'" + current_time + "\' " + ",end_time = " + "\'" + current_time + "\'" + " WHERE id =" + "\'" + certId + "\'"
    print(updateSql)
    cur.execute(updateSql)
    conn.commit()
    cur.close()
    conn.close()
    return certId


# 创建企业云证书
def createOrganizationCert(organizationCode, organizationName, licenseNumber, certName):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/certs/enterprise/saveEnterpriseCert"
        data = {"params": {"organizationCode": organizationCode, "organizationName": organizationName,
                           "licenseNumber": licenseNumber, "licenseType": "12", "certAlgorithm": "1",
                           "applyMethod": "1", "certName": certName, "certType": "1"}, "domain": "seal_system"}
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization.token}
        res = requests.post(url=Url, headers=headers, json=data)
        return json.loads(res.text)["message"]
    except:
        print("创建企业云证书接口调用失败")


# 查询用户所有生效的云证书
def getUserCertsList(userCode):
    try:
        Url = ENV("esign.projectHost") + "/seals/v1/sealcontrols/userCerts/list"
        data = {"userCode": userCode}
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId")}
        res = requests.post(url=Url, headers=headers, json=data)
        return json.loads(res.text)["data"]
    except:
        print("查询个人证书接口调用失败")


# 查询机构所有生效的云证书
def getOrganizationCertsList(organizationCode):
    try:
        Url = ENV("esign.projectHost") + "/seals/v1/sealcontrols/organizationCerts/list"
        data = {"organizationCode": organizationCode}
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId")}
        res = requests.post(url=Url, headers=headers, json=data)
        return json.loads(res.text)["data"]
    except:
        print("查询机构云证书接口调用失败")


# 吊销个人证书
def revokePersonalCert(id):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/certs/personal/revokePersonalCert"
        data = {"params": {"id": id}, "domain": "seal_system"}
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        print("吊销个人证书接口：" + str(json.loads(res.text)))
        return json.loads(res.text)["success"]
    except:
        print("吊销个人证书接口调用失败")


# 停用个人印章
def stopPersonalSeal(id):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/personal/stopPersonalSeal"
        data = {"params": {"id": id}, "domain": "seal_system"}
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        print("吊销个人证书接口：" + str(json.loads(res.text)))
        return json.loads(res.text)["message"]
    except:
        print("停用个人印章接口调用失败")


# 停用机构印章
def stopOrganizationSeal(id, disablePhysicalSeal):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/enterprise/electronic/disableSeal"
        data = {"params": {"id": id, "disablePhysicalSeal": disablePhysicalSeal}, "domain": "seal_system"}
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        print("吊销个人证书接口：" + str(json.loads(res.text)))
        return json.loads(res.text)["message"]
    except:
        print("停用机构印章接口调用失败")


# 吊销机构证书
def revokeOrganizationCert(id):
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/certs/enterprise/revokeEnterpriseCert"
        data = {"params": {"id": id}, "domain": "seal_system"}
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        print("吊销机构证书接口：" + str(json.loads(res.text)))
        return json.loads(res.text)["success"]
    except:
        print("吊销机构证书接口调用失败")


# 获取未发布的个人印章
def getUnpublishedSeal():
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/personal/pagePersonalSealList"
        sealstatus = ["1", "h"]
        for i in sealstatus:
            data = {
                "params": {"currPage": "1", "pageSize": "100", "sealStatus": i}, "domain": "seal_system"
            }
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        # print("执行印章类型列表接口的接口响应信息为：" + str(json.loads(res.text)))
        return json.loads(res.text)["data"]
    except:
        print("getUnpublishedSeal method error")
        return None


# 获取未发布的机构印章
def getUnpublishedOrganizationSeal():
    try:
        authorization = PORTAL_TOKEN
        Url = ENV("esign.projectHost") + "/seals/smc/seals/enterprise/electronic/pageElectronicSealList"
        sealstatus = ["1", "2", "3", "4", "5", "a", "h"]
        for i in sealstatus:
            data = {
                "params": {"currPage": "1", "pageSize": "100", "sealStatus": i}, "domain": "seal_system"
            }
        headers = {"Content-Type": "application/json", "x-timevale-project-id": ENV("esign.projectId"),
                   "authorization": authorization}
        res = requests.post(url=Url, headers=headers, json=data)
        # print("执行印章类型列表接口的接口响应信息为：" + str(json.loads(res.text)))
        return json.loads(res.text)["data"]
    except:
        print("getUnpublishedOrganizationSeal method error")
        return None


def getUserSealsByStatus(userCode, sealStatus=None, sealBodyStructure=None):
    """
    查询个人的各种状态的印章数据
    sealStatus: 印章状态(1草稿 g发布 h停用 5吊销)
    """
    # 自定义方法，调用时引入
    from utils.esign6Seals import getUserSealsByStatus
    headers = {'code': TOKEN_OBJ.code,
              'authorization': TOKEN_OBJ.token,
              'X-timevale-project-id': PROJECT_ID}
    res =  getUserSealsByStatus(headers, userCode, sealStatus, sealBodyStructure)
    sleep(2)
    return res

def createUserSeals(userCode, sealPattern=None):
    """
    创建个人印章
    """
    # 自定义方法，调用时引入
    from utils.esign6Seals import createUserseals
    obj = createUserseals(userCode, sealPattern)
    return obj

def createOrganizationSeals(organizationCode, userCode, sealStatus=None,sealPattern=None):
    """
    创建企业印章: sealStatus-2已发布状态，3-已停用状态，4-已吊销
    return: 返回指定状态的印章
    """
    # 自定义方法，调用时引入
    from utils.esign6Seals import createOrgSeals,updateStatusOrgSeals
    if sealStatus == 0:
        obj = createOrgSeals(organizationCode, sealPattern, "", 0, userCode)
    else:
        obj = createOrgSeals(organizationCode, sealPattern, "", 1, userCode)
    sealId = obj.get('data').get('sealInfos')[0].get('sealId')
    if sealStatus == 3:
        updateStatusOrgSeals(sealId, 3)
    if sealStatus == 4:
        updateStatusOrgSeals(sealId, 3)
        updateStatusOrgSeals(sealId, 4)
    return sealId

def getOrganizationCertsByStatus(organizationCode, certStatus=None, algorithm=None):
    """
    查询企业的各种状态的证书数据
    sealStatus: 印章状态(1草稿 g发布 h停用 5吊销)
    """
    # 自定义方法，调用时引入
    from utils.esign6Seals import getOrganizationCertsByStatus
    headers = {'code': TOKEN_OBJ.code,
              'authorization': TOKEN_OBJ.token,
              'X-timevale-project-id': PROJECT_ID}
    return getOrganizationCertsByStatus(organizationCode, certStatus, algorithm, headers)

def getOrganizationSealsByStatus(organizationCode, sealStatus=None, sealPattern=None, sealTypeCode=None):
    """
    查询企业的各种状态的印章数据
    sealStatus: 页面印章状态(1草稿 g发布 h停用 5吊销)， openapi企业印章状态： 1-待发布 2-已发布 3-已停用 4-已吊销
    """
    # 自定义方法，调用时引入
    from utils.esign6Seals import getOrganizationSealsByStatus
    headers = {'code': TOKEN_OBJ.code,
              'authorization': TOKEN_OBJ.token,
              'X-timevale-project-id': PROJECT_ID}
    return getOrganizationSealsByStatus(organizationCode, sealStatus, sealPattern, sealTypeCode, headers)

# 授权用印人给所有人
def AuthorizedIndiansforAll(sealid):
    try:
        from utils.esign6Seals import sealssignersAll
        return sealssignersAll(sealid)
    except:
        print("AuthorizedIndiansforAll method error")
        return None

# 删除电子企业章
def deleteOrgSealById(sealId, response=None):
    try:
        from utils.esign6Seals import deleteOrgSeal,listOrganizationSeals,updateStatusOrgSeals
        obj1 = listOrganizationSeals("", "", 1, "", sealId)
        if obj1.get('data').get('total')>0:
            updateStatusOrgSeals(sealId, 3)
        else:
            deleteOrgSeal(sealId)
    except:
        print("deleteOrgSealById method error")

def getGroupInfo(groupName,organizationCode=None,userCode=None,wfGroup=None):
    """
    通过分组名称获取印章分组（无，则新建）
    wfGroup： 为空则创建普通类型的分组；1-创建带流程的分组
    """
    headers = {'code': TOKEN_OBJ.code,
               'authorization': TOKEN_OBJ.token,
               'X-timevale-project-id': PROJECT_ID}
    if organizationCode == None:
        organizationCode = ENV('csqs.orgCode')
    from utils.esign6Seals import getGroupInfo
    return getGroupInfo(headers,groupName,organizationCode,userCode,wfGroup)

def getGroupId(groupName,organizationCode=None,userCode=None,wfGroup=None):
    obj1 = getGroupInfo(groupName,organizationCode,userCode,wfGroup)
    return obj1.get('sealGroupId')

def createSealTypeWorkFlow():
    """
    创建一个固定的带有流程引擎的印章类型
    """
    headers = {'code': TOKEN_OBJ.code,
               'authorization': TOKEN_OBJ.token,
               'X-timevale-project-id': PROJECT_ID}
    from utils.esign6Seals import createSealTypeWorkFlow
    return createSealTypeWorkFlow(headers)


#上面已有这个方法,
#不注释掉执行用例testcases/signs/signFlow/quickSign/quickSignCases.yml会报错
# def get_file_base64(image_path):
#     with open(image_path, "rb") as image_file:
#         # 读取图片文件的内容
#         image_data = image_file.read()
#         # 使用 base64 编码
#         base64_encoded = base64.b64encode(image_data)
#         # 将 bytes 转换为字符串
#         base64_string = base64_encoded.decode('utf-8')
#         # print(base64_string)
#         return base64_string

def getDateTime(day=0, timeFormat=None):
    """
    获取时间、日期
    :param timeFormat: int  1-'%Y-%m-%d %H:%M:%S' 、2-'%Y-%m-%d'、3-'%Y-%m-%d %H:%M:%S.%f'
    :param day: int 距今天多少天，传负数则为过去时间
    :return: 默认返回当前时间到微秒级 20220616163258.767022 可作唯一字符串，防重
    """
    from utils.dateAndTime import somedayTime
    return str(somedayTime(day, timeFormat))

# 获取可用项目
def projectSelection():
    data = {
        "params": {},
        "domain": "seal_system"
    }
    headers = {'code': TOKEN_OBJ.code,
               'authorization': TOKEN_OBJ.token,
               'X-timevale-project-id': PROJECT_ID}
    try:
        response = requests.post(url=ENV("esign.projectHost") + '/seals/manage/project/projectSelection',
                      json=data, headers=headers)
        jsonResponse = response.json()
        projectId = jsonResponse['data'][0]['projectId']
        return projectId
    except:
        raise Exception("projectId获取失败")

def queryLinkEvi(bizId, linkTemplateCode=None):
    """
    查询证据管理
    :param bizId:
    :param linkTemplateCode:
    :return:
    """
    from utils.esign6Evidence import query_link
    header = {'code': TOKEN_OBJ.code,
              'authorization': TOKEN_OBJ.token,
              'X-timevale-project-id': PROJECT_ID}
    return query_link(header, bizId, linkTemplateCode)


def docsAttachmentUpload(fileName):
    """
    文档中心：附件上传接口
    :param fileName,位于/data目录下的文件名称
    """
    file = {'uploadFile': getFileForUpload(fileName)}
    header = {"authorization": PORTAL_TOKEN, "X-timevale-project-id": PROJECT_ID}
    from utils.esign6Docs import attachmentUpload
    return attachmentUpload(MAIN_HOST, file, header)

def getOuterOrganizationCode(customOrgNo=None, name=None, organizationCode=None):
    """
    管理平台：通过调用openapi获取外部组织编码
    """
    json = {
        "customOrgNo": customOrgNo,
        "name": name,
        "organizationCode": organizationCode
    }
    from utils.esign6Manage import detailOuterOrg
    data0 = detailOuterOrg(getSignature(json), json)['data']
    if data0:
        return data0[0]['organizationCode']
    else:
        return None


def getFutureTime(interval):
    """
    :param interval: 时间间隔值  秒
    :return: 例2022-01-01 23：56：11
    """
    from utils.dateAndTime import get_future_time
    return get_future_time(interval)

#截取字符串
def substring(temp: str, index1: int, index2: int):
    """
    截取字符串temp,下标index1开始取到index2，顾头不顾尾，所有不包含index2
    """
    return temp[index1:index2]

def docSignerListTosubmit(params):
    """
    组装页面电子签署发起得时候得签署方信息
    :param params
    :return: 电子签署发起页面得提交接口得签署方信息
    """
    from utils.esign6Docs import getSignerListTosubmit
    from utils.esign6Manage import detailInnerUsers, detailInnerOrg, detailOuterUsers, detailOuterOrg

    params
    if params:
        for item in params:
            headers = getSignature(item)
            organizationName01 = ''
            departmentName01 = ''
            departmentCode01 = ''
            if 'userType' in item.keys() and item.get('userType') == 2:
                userInfo01 = detailOuterUsers(headers, item)
                userName01 = userInfo01.get('data')[0].get('name')
                if 'organizationCode' in item.keys():
                    org01 = detailOuterOrg(headers, item)
                    organizationName01 = org01.get('data')[0].get('name')
                    departmentName01 = org01.get('data')[0].get('name')
            else:
                userInfo01 = detailInnerUsers(headers, item)
                userName01 = userInfo01.get('data')[0].get('name')
                if 'organizationCode' in item.keys():
                    org01 = detailInnerOrg(headers, item)
                    organizationName01 = org01.get('data')[0].get('name')
                if 'departmentCode' in item.keys():
                    departmentCode01 = item.get('departmentCode')
                    query = {"organizationCode": departmentCode01}
                else:
                    departmentCode01 = userInfo01.get('data')[0].get('mainOrganizationCode')
                    query = {"organizationCode": departmentCode01}
                headers2 = getSignature(query)
                depart01 = detailInnerOrg(headers2, query)
                departmentName01 = depart01.get('data')[0].get('name')
            item['userName'] = userName01
            item['organizationName'] = organizationName01
            item['departmentName'] = departmentName01
            item['departmentCode'] = departmentCode01
    return getSignerListTosubmit(params)


def saveSignature(batchTemplateInitiationUuid, free: int, signerInfos: list, fileInfos: list):
    """
    获取签署区设置页的save接口的参数
    :param batchTemplateInitiationUuid:  发起电子列表的数据
    :param free:  是否自由签署. 0-自由签;1-指定签署
    :param signerInfos:  签署方设置 e.g.[{'signMode': 1, 'id': 'add-2', 'nodeX': 2, 'signerList': [{'userCode': 'zhengml', 'userName': None, 'id': 'add-2', 'signerSnapshotId': '51158456243987001070123714630551', 'signerTerritory': 1, 'signerType': 1, 'assignSigner': 1}]}]
    :param fileInfos: 签署文档 e.g. [{"appendType":1,"attachmentInfo":{"fileKey":"$fileKey0"}},{"appendType":1,"attachmentInfo":{"fileKey":"$fileKey1"}}]
    :return:
    """
    from utils.esign6Docs import getSaveSignature
    return getSaveSignature(batchTemplateInitiationUuid, free, signerInfos, fileInfos)


def getInfoDealToSubmit(params):
    """
       组装数据主要处理签署方和签署区之间的关联
       :param signerInfos:  getSignerListTosubmit()的出参
       :param detailData:  getInfo接口的出参的data对象信息
       :return:
       """
    from utils.esign6Docs import getInfoDeal
    return getInfoDeal(params)


def getEnterpriseSeal(params):
    """
    获取企业印章，包括法人印章
    :param params: e.g.
    商密印章params(可以自定义印模类型和印章图片的)：{'organizationCode': '要创建印章的企业','sealTypeCode': '印章类型-非必填默认会自动生成getTimestamp()', 'userCode':'制章的人-非必填默认ceswdzxzdhyhwgd1'，'sealName': '非必填，默认自动生成','sealShape'：'非必填，默认是1'，'sealSource': '制章方式：1自定义印章 2使用印章模板 3复用印章图片,非必填，默认是2', 'sealThumbnailUrl'：'印章图片的fileKey,非必填，默认是自动生成', 'cryptoFileKey': '印章图片的加密cryptoFileKey,非必填，默认是自动生成'}
    商密法人印章params：{'organizationCode': '要创建印章的企业','personalSealId': '法人章关联的个人'}
    国密印章params：{'organizationCode': '要创建印章的企业','sealTypeCode': '印章类型-非必填默认会自动生成getTimestamp()', 'sealPatternSlected': 3}
    :return:
    """
    # 印章创建者信息:个人和所属企业
    print('[request]:', params)
    if 'personalSealId' in params.keys():
        params['personalSealId'] = params.get('personalSealId')
        params['sealTypeCode'] = 'LEGAL-PERSON-SEAL'
    else:
         params['personalSealId'] = None
#        params['personalSealId'] = ""
    # 处理电子印章的形态-云商密 / 云国密 # 印章形态(1-商密印章 2-物理印章 3-国密印章 4-UKey商密印章 5-UKey国密印章 )
    if 'sealPatternSlected' in params.keys():
        params['sealPatternSlected'] = 3
    else:
        params['sealPatternSlected'] = 1
    if 'sealName' in params.keys():
        params['sealName'] = params.get('sealName')
    params['userCode'] = ENV('ceswdzxzdhyhwgd1.account')
    from utils.esign6Seals import createEnterpriseSeal
    return createEnterpriseSeal(params)


def getEnterpriseCert(organizationCode, certAlgorithm, customAccountNo=None):
    """
    获取企业证书
    :param organizationCode: 要创建证书的企业
    :param certAlgorithm: 证书算法 1-rsa,2-sm2
    :param customAccountNo: 证书创建者，默认ceswdzxzdhyhwgd1
    :return:
    """
    params = {"certAlgorithm": certAlgorithm, "organizationCode": organizationCode}
    if customAccountNo == None or customAccountNo == ENV('ceswdzxzdhyhwgd1.account'):
        token01 = PORTAL_TOKEN
        code01 = PORTAL_CODE
    else:
        obj = createPortalTokenObj(customAccountNo, ENV('ceswdzxzdhyhwgd1.password'))
        token01 = obj.token
        code01 = obj.code
    headers = {"authorization": token01, "X-timevale-project-id": PROJECT_ID, "code": code01}

    query2 = {"organizationCode": organizationCode}
    headers3 = getSignature(query2)
    from utils.esign6Manage import detailInnerOrg
    obj3 = detailInnerOrg(headers3, query2)
    params['organizationName'] = obj3.get('data')[0].get('name')
    params['licenseNumber'] = obj3.get('data')[0].get('licenseNo')

    from utils.esign6Seals import createElectronicCert
    return createElectronicCert(headers, params)


def getPersonCert(userCode, certAlgorithm, customAccountNo=None):
    """
    获取个人证书
    :param userCode: 要创建证书的个人
    :param certAlgorithm: 证书算法 1-rsa,2-sm2
    :param customAccountNo: 证书创建者，默认ceswdzxzdhyhwgd1
    :return:
    """
    params = {"certAlgorithm": certAlgorithm, "userCode": userCode}
    if customAccountNo == None or customAccountNo == ENV('ceswdzxzdhyhwgd1.account'):
        token01 = PORTAL_TOKEN
        code01 = PORTAL_CODE
    else:
        obj = createPortalTokenObj(customAccountNo, ENV('ceswdzxzdhyhwgd1.password'))
        token01 = obj.token
        code01 = obj.code
    headers = {"authorization": token01, "X-timevale-project-id": PROJECT_ID, "code": code01}

    query2 = {"userCode": userCode}
    headers3 = getSignature(query2)

    from utils.esign6Manage import detailInnerUsers
    obj3 = detailInnerUsers(headers3, query2)
    params['userName'] = obj3.get('data')[0].get('name')
    licenseNumber = obj3.get('data')[0].get('licenseNo')
    params['licenseNumber'] = get_encrypt(licenseNumber)

    from utils.esign6Seals import createPersonCert
    return createPersonCert(headers, params)


def addOuterOtherOrgOpenApi(userCode, organizationCode):
    """
    为相对方企业成员用户添加兼职企业
    :param userCode: 必须是企业成员
    :param organizationCode: 非成员的主责企业
    :return:
    """
    from utils.esign6Manage import addOuterOtherOrg
    return addOuterOtherOrg(userCode, organizationCode)


def resetRealName(mobile):
    from utils.cloudScene import realNameReset
    realNameReset(CLOUD_APPID, mobile)


def clearEnterpriseSeal(sealId, customAccountNo=None):
    """
    清理某个印章
    :param sealId:
    :param customAccountNo:
    :return:
    """
    from utils.esign6Seals import DeleteEnterpriseSeal
    if customAccountNo == None or customAccountNo == ENV('ceswdzxzdhyhwgd1.account'):
        token01 = PORTAL_TOKEN
        code01 = PORTAL_CODE
    else:
        obj = createPortalTokenObj(customAccountNo, ENV('ceswdzxzdhyhwgd1.password'))
        token01 = obj.token
        code01 = obj.code
    headers = {"authorization": token01, "X-timevale-project-id": PROJECT_ID, "code": code01}
    return DeleteEnterpriseSeal(headers, sealId)


def compareF(originFile, zipurl):
    from utils.compareFile import compareFile
    res = compareFile(originFile, zipurl)
    return res


def compareDict(keyParams, valueParams):
    """
    处理json: key是动态变更的数据
    :param keyParams:  e.g:['key_001', '_key_002']
    :param valueParams:  e.g:['测试签署一', '***********']
    :return:  e.g:{'key_001': '测试签署一', '_key_002': '***********'}
    """
    res = {}
    if type(keyParams) == list:
        index = 0
        for i in keyParams:
            res[i] = valueParams[index]
            index = index + 1
    return res

def getUserSeal(userCode,sealPattern=None):
    """
    获取或创建内部用户个人印章
    """
    from utils.esign6Seals import getUserseals
    return getUserseals(userCode,sealPattern)

def getUserCert(userCode,sealPattern=None):
    """
    获取或创建内部用户个人证书
    """
    from utils.esign6Seals import getUsercerts
    return getUsercerts(userCode,sealPattern)

def epaasHeader(epaasUrl):
    """
    epaas模板文档对接天印使用的接口的请求头信息
    :param epaasUrl:
    :return:
    """
    from utils.esignToken import epaasHeader
    return epaasHeader(epaasUrl)

def getTplToken(epaasUrl):
    """
    epaas模板文档对接天印使用的接口的token
    :param epaasUrl:
    :return:
    """
    from utils.esignToken import getTplToken
    return getTplToken(epaasUrl)


def epaasSignContentField(signtype,templateRoleId,sealType,defaultValue=None,addSealRule=None):
    '''signtype=1:正文签署区
           signtype=2：骑缝章签署区
           signtype=3：备注签署区
           sealType=1:个人签署区
           sealType=2:企业签署区
           sealType=3:企业经办人签署区
           sealType=4:法人签署区
           defaultValue:印章id
        '''
    from utils.esign6Docs import epaasSignContentField
    return epaasSignContentField(signtype,templateRoleId,sealType,defaultValue,addSealRule)

def epaasFillContent(num,type=None,labelName=None,isrequired=None):
    from utils.esign6Docs import epaasFillContent
    filed =  epaasFillContent(num,type,labelName,isrequired)
    fileds = [*filed]
    return fileds


def getEpaasTemplateContent(fillCotentNum,signContentNum,customType):
    """
    epaas模板文档对接天印使用的接口的token
    :param epaasUrl:
    :return:
    """
    from utils.esign6Docs import getEpaasTemplateContent
    return getEpaasTemplateContent(fillCotentNum,signContentNum,customType)

def getEpaasTemplateContentWithRoleId(fields,fillParams=None,signParams=None):
    '''
    通过控件名称指定参与方
    :param fields: 控件列表
    :param fillParams: 填写控件关联 [ {"label": "XXX","templateRoleId": "xxx" }]
    :param signParams: 签署控件关联
    :return:
    '''
    from utils.esign6Docs import getEpaasTemplateContentWithRoleId
    return getEpaasTemplateContentWithRoleId(fields,fillParams,signParams)

def protal_wait_backend_task_ready(accountNumber, task_name, max_retries=10, retry_interval=2):
    """
    业务平台后台任务轮巡方法

    :param accountNumber: 登录业务平台账号
    :param task_name: 后台任务名称
    :param max_retries: 最大轮巡次数
    :param retry_interval: 轮巡间隔（秒）
    :return:
    """
    password = os.environ['password01']
    headers = {
        "Content-Type": "application/json",
        "authorization": getPortalToken(accountNumber, password),
        "X-timevale-project-id": PROJECT_ID
    }
    data = {
        "params": {
            "currPage": 1,
            "pageSize": 1},
        "domain": "evidence_system",
        "userCode": accountNumber
    }
    for attempt in range(max_retries):
        response = requests.post(url=MAIN_HOST + '/esign-docs/portal/backend/getBackendTaskList',
                                 json=data,
                                 headers=headers)

        context = response.json()
        # 提取关键字段
        status =  context.get("data", {}).get("list", [{}])[0].get("status") #任务状态，1处理中 2成功 3失败
        name = context.get("data", {}).get("list", [{}])[0].get("name")
        file_url = context.get("data", {}).get("list", [{}])[0].get("fileUrl")
        file_name = context.get("data", {}).get("list", [{}])[0].get("fileName")
        #检查任务名是否符合
        if name == task_name:
            # 检查任务状态是否成功，成功直接跳出
            if status == 2:
                return
            # 检查是否非空
            if file_url is not None and file_name is not None:
                return

            time.sleep(retry_interval)
    #     # 超时处理
    # pytest.fail(f"在 {max_retries} 次重试后未获取到有效的 fileUrl 和 fileName")
    return

def read_excel_withdraw(file_download_url, file_name, sheet_name):
    """
    传入Excel文件下载链接、文件名、工作表名，将Excel文档中值提取出
    """
    from utils.esign6Manage import download_file_to_fixed_path
    download_file_to_fixed_path(file_download_url, file_name)

    file_path = "tests/data/download/" + file_name  # 拼接硬编码路径和文件名
    df = pd.read_excel(file_path, sheet_name=sheet_name)
    df_json = df.to_json(orient='records',force_ascii=False)

    #解析文件流
    # df_json = read_excel_stream_withdraw(file_path, sheet_name)

    from utils.esign6Manage import delete_file
    delete_file(file_name)

    return df_json

def get_dict_value(data_str, index, target_key):
    """
    从字典列表中提取指定索引和键的值
    :param data_str: json格式字段
    :param index: 要访问的列表索引（从0开始）
    :param target_key: 要提取的字典键名
    :return: 对应的值（若不存在返回None）
    """
    #将str转为字典
    data_list = json.loads(data_str)
    # 检查索引有效性
    if not isinstance(data_list, list) or index < 0 or index >= len(data_list):
        return None

    target_dict = data_list[index]

    # 检查是否为字典类型
    if not isinstance(target_dict, dict):
        return None
    # 使用get方法安全获取值
    return target_dict.get(target_key)

def replace_template_role_id(json_data, new_value):
    from utils.esign6Docs import getEpaasTemplateContentWithRoleId
    return replace_template_role_id(json_data, new_value)

def generate_field_object(label, field_type,fieldId=None):
    """
    根据label和type生成fields对象
    :param label: 字段标签
    :param field_type: 字段类型 (SIGN, QF_SIGN, REMARK_SIGN, TEXT, MULTILINE_TEXT, NUM, DATE, PHONE_NUM, ID_CARD, TICK_BOX, PULL_DOWN, IMAGE, UNIFY_THE_SOCIAL_CREDIT_CODE等)
    :return: 生成的field对象
    """
    from utils.esign6Docs import generate_field_object
    return generate_field_object(label, field_type,fieldId)

def add_template_role_id(field_object, template_role_id):
    """
    为字段对象添加templateRoleId字段
    :param field_object: 字段对象（字典格式）
    :param template_role_id: 模板角色ID
    :return: 添加了templateRoleId字段的对象
    """
    if isinstance(field_object, dict):
        # 创建对象的副本以避免修改原对象
        updated_object = field_object.copy()
        # 添加templateRoleId字段，与settings同级
        updated_object['templateRoleId'] = template_role_id
        return updated_object
    else:
        raise ValueError("field_object必须是字典类型")


def update_template_role_id(fields, label, template_role_id,sealType=None):
    """
    通过label找到数组中的对象，添加或更新templateRoleId字段

    Args:
        field_array: 包含字段对象的数组
        label: 要查找的标签名称
        template_role_id: 要设置的templateRoleId值
        sealType=PSN,ORG_PSN,ORG,ORG_LEGAL
    """
    sealType1 = []
    for field_obj in fields:
        if field_obj['label'] == label:
            field_obj['templateRoleId'] = template_role_id
            if sealType:
                sealType1.append(sealType)
                field_obj['settings']['sealTypes'] = sealType1
            return fields  # 返回更新后的对象
    return fields

def extract_table_cell(data: str, row: int, column: int):
    """
    从JSON数据中提取指定行列的单元格内容

    Args:
        data (str): JSON格式的数据字符串
        row (int): 行号，从1开始
        column (int): 列号，从1开始

    Returns:
        str: 指定位置的单元格内容，如果不存在则返回空字符串
    """
    from utils.esign6Docs import extract_table_cell
    return extract_table_cell(data, row, column)


def remove_key_from_dict_list(dict_list, key_to_remove):
    from utils.esign6Docs import remove_key_from_dict_list
    return remove_key_from_dict_list(dict_list, key_to_remove)


def get_value_by_fieldId(data, target_key, target_value,  target_key2):
    from utils.esign6Docs import get_value_by_fieldId_fieldValue
    return get_value_by_fieldId_fieldValue(data, target_key, target_value,  target_key2)

def verify_pdf_contains_text(file_key, expected_texts, download_url):
    from utils.pdfTextExtractor import verify_pdf_contains_text
    return verify_pdf_contains_text(file_key, expected_texts, download_url)

def delete_temp_file(file_key):
    from utils.pdfTextExtractor import delete_temp_file
    return delete_temp_file(file_key)

# 更新签署流程接口测试用例需要的时间处理函数
def get_future_time(days):
    """
    获取未来指定天数的时间
    :param days: 天数
    :return: 格式化时间字符串 'yyyy-MM-dd HH:mm:ss'
    """
    future_time = datetime.datetime.now() + datetime.timedelta(days=days)
    return future_time.strftime('%Y-%m-%d %H:%M:%S')

def get_past_time(days):
    """
    获取过去指定天数的时间
    :param days: 天数
    :return: 格式化时间字符串 'yyyy-MM-dd HH:mm:ss'
    """
    past_time = datetime.datetime.now() - datetime.timedelta(days=days)
    return past_time.strftime('%Y-%m-%d %H:%M:%S')

def get_current_time_plus_seconds(seconds):
    """
    获取当前时间加指定秒数的时间
    :param seconds: 秒数
    :return: 格式化时间字符串 'yyyy-MM-dd HH:mm:ss'
    """
    future_time = datetime.datetime.now() + datetime.timedelta(seconds=seconds)
    return future_time.strftime('%Y-%m-%d %H:%M:%S')

def get_current_time_minus_seconds(seconds):
    """
    获取当前时间减指定秒数的时间
    :param seconds: 秒数
    :return: 格式化时间字符串 'yyyy-MM-dd HH:mm:ss'
    """
    past_time = datetime.datetime.now() - datetime.timedelta(seconds=seconds)
    return past_time.strftime('%Y-%m-%d %H:%M:%S')

def get_current_time_plus_minutes(minutes):
    """
    获取当前时间加指定分钟数的时间
    :param minutes: 分钟数
    :return: 格式化时间字符串 'yyyy-MM-dd HH:mm:ss'
    """
    future_time = datetime.datetime.now() + datetime.timedelta(minutes=minutes)
    return future_time.strftime('%Y-%m-%d %H:%M:%S')

def get_future_time_with_offset(days, hours):
    """
    获取未来指定天数和小时数的时间
    :param days: 天数
    :param hours: 小时数
    :return: 格式化时间字符串 'yyyy-MM-dd HH:mm:ss'
    """
    future_time = datetime.datetime.now() + datetime.timedelta(days=days, hours=hours)
    return future_time.strftime('%Y-%m-%d %H:%M:%S')

if __name__ == '__main__':
    #getTplToken()
    param11 = [1, 0]
    # print(get_signFlow_signing("", False, False))