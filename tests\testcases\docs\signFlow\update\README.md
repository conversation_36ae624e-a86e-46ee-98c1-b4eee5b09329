# 更新签署流程接口测试用例

## 概述

本目录包含了"更新签署流程"接口（`/esign-docs/v1/signFlow/update`）的完整测试用例，基于httprunner框架编写。

## 接口信息

- **接口名称**: 更新签署流程
- **接口URL**: `/esign-docs/v1/signFlow/update`
- **请求方式**: POST
- **主要功能**: 更新签署流程的合同到期日期（contractExpirationTime）

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| signFlowId | string | 否 | 签署流程id，signFlowId、businessNo二选一必填 |
| businessNo | string | 否 | 第三方签署流程业务id，signFlowId、businessNo二选一必填 |
| signFlowExpireTime | string | 是 | 新的签署截止日期（预留字段，会校验格式但不落库） |
| contractExpirationTime | string | 是 | 新的合同到期日期，格式"yyyy-MM-dd hh:mm:ss" |

## 文件结构

```
tests/testcases/docs/signFlow/update/
├── README.md                                    # 本说明文件
├── tc_update_signflow_1.yml                     # 基础功能测试用例
├── tc_update_signflow_status_permission.yml     # 状态和权限测试用例
├── tc_update_signflow_smoke.yml                 # 冒烟测试用例
└── tc_update_signflow_performance_exception.yml # 性能和异常测试用例
```

## 测试用例说明

### 1. tc_update_signflow_1.yml - 基础功能测试
包含24个测试用例，覆盖：
- **参数验证**: signFlowId、businessNo参数的有效性验证
- **参数优先级**: signFlowId和businessNo同时传入时的优先级处理
- **格式验证**: contractExpirationTime和signFlowExpireTime的格式校验
- **日期校验**: 时间有效性和边界值验证
- **异常处理**: 无效ID、超长参数等异常情况

### 2. tc_update_signflow_status_permission.yml - 状态和权限测试
包含17个测试用例，覆盖：
- **流程状态限制**: 不同状态流程的更新权限验证
- **业务模板规则**: 与业务模板规则的交互验证
- **多方签署场景**: 顺序签署、无序签署、或签等场景
- **权限控制**: 不同角色用户的操作权限验证
- **并发处理**: 并发更新的数据一致性验证

### 3. tc_update_signflow_smoke.yml - 冒烟测试
包含12个核心测试用例，覆盖：
- **基本功能**: 核心更新功能验证
- **关键参数**: 必要参数的验证
- **格式校验**: 时间格式的正确性验证
- **错误处理**: 基本错误场景的处理

### 4. tc_update_signflow_performance_exception.yml - 性能和异常测试
包含20个测试用例，覆盖：
- **性能测试**: 响应时间和连续调用性能验证
- **异常处理**: SQL注入、XSS攻击等安全测试
- **边界测试**: 超长参数、特殊字符等边界情况
- **兼容性测试**: 向下兼容性和数据格式兼容性

## 依赖的API接口

测试用例中引用了以下API接口（需要确保这些接口存在）：

```yaml
# 创建签署流程接口（Setup阶段使用）
api: api/esignDocs/signFlow/create.yml

# 查询签署流程详情接口（验证阶段使用）
api: api/esignDocs/signFlow/detail.yml

# 作废签署流程接口（Teardown阶段使用）
api: api/esignDocs/signFlow/revoke.yml
```

## 辅助函数

在`tests/debugtalk.py`中添加了以下时间处理函数：

```python
def get_future_time(days)                    # 获取未来指定天数的时间
def get_past_time(days)                      # 获取过去指定天数的时间
def get_current_time_plus_seconds(seconds)   # 获取当前时间+秒数
def get_current_time_minus_seconds(seconds)  # 获取当前时间-秒数
def get_current_time_plus_minutes(minutes)   # 获取当前时间+分钟数
def get_future_time_with_offset(days, hours) # 获取未来指定天数和小时数的时间
```

## 运行方式

### 1. 运行单个测试文件
```bash
# 运行基础功能测试
hrun tests/testcases/docs/signFlow/update/tc_update_signflow_1.yml

# 运行冒烟测试
hrun tests/testcases/docs/signFlow/update/tc_update_signflow_smoke.yml

# 运行状态权限测试
hrun tests/testcases/docs/signFlow/update/tc_update_signflow_status_permission.yml

# 运行性能异常测试
hrun tests/testcases/docs/signFlow/update/tc_update_signflow_performance_exception.yml
```

### 2. 运行所有更新签署流程测试
```bash
hrun tests/testcases/docs/signFlow/update/
```

### 3. 生成测试报告
```bash
hrun tests/testcases/docs/signFlow/update/ --html=reports/signflow_update_report.html
```

## 测试数据准备

### 环境变量配置
确保以下环境变量已正确配置：
- `esign.gatewayHost`: 网关地址
- `esign.projectId`: 项目ID
- `esign.projectSecret`: 项目密钥

### 测试账号权限
确保测试账号具有以下权限：
- 创建签署流程权限
- 更新签署流程权限
- 查询签署流程权限
- 作废签署流程权限

## 测试覆盖范围

### 功能覆盖
- ✅ 参数验证（signFlowId、businessNo、contractExpirationTime）
- ✅ 参数优先级处理
- ✅ 时间格式校验
- ✅ 日期有效性校验
- ✅ 流程状态限制
- ✅ 权限控制
- ✅ 业务规则交互
- ✅ 异常处理
- ✅ 性能验证
- ✅ 安全测试

### 场景覆盖
- ✅ 正常更新场景
- ✅ 参数缺失场景
- ✅ 格式错误场景
- ✅ 权限不足场景
- ✅ 流程状态不支持场景
- ✅ 并发更新场景
- ✅ 边界值场景
- ✅ 异常攻击场景

## 注意事项

1. **依赖接口**: 运行前请确保相关的创建、查询、作废接口已实现
2. **测试数据**: 测试会自动创建和清理测试数据，无需手动准备
3. **环境隔离**: 建议在测试环境运行，避免影响生产数据
4. **并发测试**: 性能测试中的并发场景可能需要调整并发数量
5. **时间依赖**: 部分测试用例依赖系统时间，确保测试环境时间准确

## 测试结果分析

### 成功标准
- 所有正向测试用例返回200状态码
- 所有负向测试用例返回非200状态码
- 数据更新验证通过
- 权限控制验证通过
- 性能指标在合理范围内

### 常见问题
1. **时间格式错误**: 检查contractExpirationTime格式是否为"yyyy-MM-dd HH:mm:ss"
2. **权限不足**: 检查测试账号是否具有相应权限
3. **流程状态错误**: 检查流程当前状态是否支持更新操作
4. **参数缺失**: 检查signFlowId和businessNo是否至少传入一个

## 维护说明

1. **新增测试场景**: 在对应的yml文件中添加新的test块
2. **修改验证逻辑**: 更新validate部分的断言条件
3. **调整测试数据**: 修改variables部分的测试数据
4. **更新依赖**: 如果API接口路径变更，需要更新api字段

## 联系方式

如有问题或建议，请联系测试团队。
