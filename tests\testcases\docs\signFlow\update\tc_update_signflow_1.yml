- config:
    name: "更新签署流程接口测试"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      - future_time_1: ${get_future_time(1)}  # 当前时间+1天
      - future_time_2: ${get_future_time(2)}  # 当前时间+2天
      - past_time: ${get_past_time(1)}        # 当前时间-1天
      - current_time_plus_1s: ${get_current_time_plus_seconds(1)}  # 当前时间+1秒
      - current_time_minus_1s: ${get_current_time_minus_seconds(1)} # 当前时间-1秒
      - invalid_signflow_id: "invalid_flow_id_123"
      - invalid_business_no: "invalid_business_no_123"
      - test_reason: "自动化测试更新签署流程"
      - second: 3
      - fileKey0: ${ENV(fileKey)}
      - userCode0: ${ENV(sign01.userCode)}
      - customAccountNoSigner0: ${ENV(sign01.accountNo)}
      - customOrgNoSigner0: ${ENV(sign01.main.orgNo)}

# Setup: 创建测试用的签署流程
- test:
    name: setup-创建测试签署流程1
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessNo: ${random_str(20)}
      signFlowExpireTime: $future_time_2
      subject: "自动化测试更新签署流程1"
      remark: "自动化测试更新签署流程1"
      fileKey: $fileKey0
      userCodeSigner: $userCode0
      customAccountNoSigner: $customAccountNoSigner0
      customOrgNoSigner: $customOrgNoSigner0
    setup_hooks:
      - ${sleep($second)}
    extract:
      test_signflow_id_1: content.data.signFlowId
      test_business_no_1: content.data.businessNo
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

- test:
    name: setup-创建测试签署流程2
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessNo: ${random_str(20)}
      signFlowExpireTime: $future_time_2
      subject: "自动化测试更新签署流程2"
      remark: "自动化测试更新签署流程2"
      fileKey: $fileKey0
      userCodeSigner: $userCode0
      customAccountNoSigner: $customAccountNoSigner0
      customOrgNoSigner: $customOrgNoSigner0
    setup_hooks:
      - ${sleep($second)}
    extract:
      test_signflow_id_2: content.data.signFlowId
      test_business_no_2: content.data.businessNo
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 测试用例1: 使用signFlowId参数验证
- test:
    name: case1-使用有效signFlowId更新contractExpirationTime
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_2
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 测试用例2: 使用businessNo参数验证
- test:
    name: case2-使用有效businessNo更新contractExpirationTime
    variables:
      signFlowId: ""
      businessNo: $test_business_no_2
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_2
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 测试用例3: 参数优先级验证
- test:
    name: case3-同时传入signFlowId和businessNo验证优先级
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: $test_business_no_2  # 不同的businessNo
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_2
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 应该以signFlowId为准进行更新

# 测试用例4: 必填参数验证
- test:
    name: case4-signFlowId和businessNo都为空
    variables:
      signFlowId: ""
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回参数缺失错误

# 测试用例5: contractExpirationTime格式验证 - 正确格式
- test:
    name: case5-contractExpirationTime正确格式验证
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "2024-12-31 23:59:59"
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 测试用例6: contractExpirationTime格式验证 - 错误格式1
- test:
    name: case6-contractExpirationTime错误格式验证1
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "2024/12/31 23:59:59"  # 错误格式
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回格式错误

# 测试用例7: contractExpirationTime格式验证 - 错误格式2
- test:
    name: case7-contractExpirationTime错误格式验证2
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "2024-12-31"  # 只有日期
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回格式错误

# 测试用例8: contractExpirationTime格式验证 - 错误格式3
- test:
    name: case8-contractExpirationTime错误格式验证3
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "2024-12-31T23:59:59"  # ISO格式
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回格式错误

# 测试用例9: contractExpirationTime格式验证 - 非法字符
- test:
    name: case9-contractExpirationTime非法字符验证
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "abc-def-ghi hh:mm:ss"  # 非法字符
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回格式错误

# 测试用例10: signFlowExpireTime预留字段验证 - 正确格式
- test:
    name: case10-signFlowExpireTime正确格式验证
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: "2024-12-31 23:59:59"
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 会校验格式但不会真的落库操作

# 测试用例11: signFlowExpireTime预留字段验证 - 错误格式
- test:
    name: case11-signFlowExpireTime错误格式验证
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: "2024/12/31 23:59:59"  # 错误格式
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回格式错误

# 测试用例12: 无效signFlowId验证
- test:
    name: case12-无效signFlowId验证
    variables:
      signFlowId: $invalid_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回流程不存在错误

# 测试用例13: 无效businessNo验证
- test:
    name: case13-无效businessNo验证
    variables:
      signFlowId: ""
      businessNo: $invalid_business_no
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回流程不存在错误

# 测试用例14: 日期时间校验 - 大于当前时间
- test:
    name: case14-contractExpirationTime大于当前时间
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 测试用例15: 日期时间校验 - 小于当前时间
- test:
    name: case15-contractExpirationTime小于当前时间
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $past_time
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回"合同到期日期应大于当前时间"错误

# 测试用例16: 日期边界值验证 - 当前时间+1秒
- test:
    name: case16-contractExpirationTime当前时间加1秒
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $current_time_plus_1s
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 测试用例17: 日期边界值验证 - 当前时间-1秒
- test:
    name: case17-contractExpirationTime当前时间减1秒
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $current_time_minus_1s
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回时间校验错误

# 测试用例18: 清空到期日期验证
- test:
    name: case18-清空contractExpirationTime
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: ""  # 传入空值
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 测试用例19: 数据格式异常处理 - 超长字符串
- test:
    name: case19-contractExpirationTime超长字符串
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "${random_str(1000)}"  # 超长字符串
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回参数长度错误

# 测试用例20: 数据格式异常处理 - 特殊字符
- test:
    name: case20-contractExpirationTime特殊字符
    variables:
      signFlowId: $test_signflow_id_1
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "2024-12-31 23:59:59<script>alert('xss')</script>"
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回格式错误或被正确过滤

# 测试用例21: 参数长度边界值验证 - 最长signFlowId
- test:
    name: case21-最长signFlowId验证
    variables:
      signFlowId: "${random_str(255)}"  # 假设最长255字符
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 不存在的signFlowId应该返回错误

# 测试用例22: 参数长度边界值验证 - 超长signFlowId
- test:
    name: case22-超长signFlowId验证
    variables:
      signFlowId: "${random_str(1000)}"  # 超长字符串
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回长度错误

# 测试用例23: 参数长度边界值验证 - 最长businessNo
- test:
    name: case23-最长businessNo验证
    variables:
      signFlowId: ""
      businessNo: "${random_str(255)}"  # 假设最长255字符
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 不存在的businessNo应该返回错误

# 测试用例24: 参数长度边界值验证 - 超长businessNo
- test:
    name: case24-超长businessNo验证
    variables:
      signFlowId: ""
      businessNo: "${random_str(1000)}"  # 超长字符串
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回长度错误

# 验证测试用例: 检查更新结果
- test:
    name: check-验证contractExpirationTime更新结果
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: ""
      signFlowId: $test_signflow_id_1
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 验证contractExpirationTime是否更新成功

# Teardown: 清理测试数据
- test:
    name: teardown-删除测试签署流程1
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      businessNo: ""
      signFlowId: $test_signflow_id_1
      reason: $test_reason
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

- test:
    name: teardown-删除测试签署流程2
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      businessNo: ""
      signFlowId: $test_signflow_id_2
      reason: $test_reason
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
