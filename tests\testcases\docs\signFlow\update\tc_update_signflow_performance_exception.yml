- config:
    name: "更新签署流程接口性能和异常测试"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      - future_time_1: ${get_future_time(1)}  # 当前时间+1天
      - future_time_2: ${get_future_time(2)}  # 当前时间+2天
      - test_reason: "自动化性能异常测试更新签署流程"
      - second: 3
      - fileKey0: ${ENV(fileKey)}
      - userCode0: ${ENV(sign01.userCode)}
      - customAccountNoSigner0: ${ENV(sign01.accountNo)}
      - customOrgNoSigner0: ${ENV(sign01.main.orgNo)}

# Setup: 创建测试用的签署流程
- test:
    name: setup-创建性能测试签署流程
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessNo: ${random_str(20)}
      signFlowExpireTime: $future_time_2
      subject: "自动化性能异常测试更新签署流程"
      remark: "自动化性能异常测试更新签署流程"
      fileKey: $fileKey0
      userCodeSigner: $userCode0
      customAccountNoSigner: $customAccountNoSigner0
      customOrgNoSigner: $customOrgNoSigner0
    setup_hooks:
      - ${sleep($second)}
    extract:
      perf_test_signflow_id: content.data.signFlowId
      perf_test_business_no: content.data.businessNo
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 性能测试用例1: 响应时间验证
- test:
    name: perf-case1-接口响应时间验证
    variables:
      signFlowId: $perf_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_2
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 监控响应时间应在合理范围内

# 性能测试用例2: 连续调用性能验证
- test:
    name: perf-case2-连续调用性能验证1
    variables:
      signFlowId: $perf_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: ${get_future_time_with_offset(1, 1)}  # 不同时间值
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep(1)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

- test:
    name: perf-case3-连续调用性能验证2
    variables:
      signFlowId: $perf_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: ${get_future_time_with_offset(1, 2)}
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep(1)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 异常测试用例1: 数据格式异常处理
- test:
    name: exception-case1-超长字符串contractExpirationTime
    variables:
      signFlowId: $perf_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "${random_str(1000)}"  # 超长字符串
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回参数长度错误

# 异常测试用例2: SQL注入攻击防护
- test:
    name: exception-case2-SQL注入攻击防护验证
    variables:
      signFlowId: $perf_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "2024-12-31 23:59:59'; DROP TABLE signflow; --"
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # SQL注入攻击应该被正确过滤

# 异常测试用例3: XSS攻击防护
- test:
    name: exception-case3-XSS攻击防护验证
    variables:
      signFlowId: $perf_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "2024-12-31 23:59:59<script>alert('xss')</script>"
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # XSS攻击脚本应该被正确过滤

# 异常测试用例4: 特殊字符处理
- test:
    name: exception-case4-特殊字符处理验证
    variables:
      signFlowId: $perf_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "2024-12-31 23:59:59\n\r\t"  # 包含换行符等特殊字符
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 特殊字符应该返回格式错误

# 异常测试用例5: 空值和null处理
- test:
    name: exception-case5-null值处理验证
    variables:
      signFlowId: $perf_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: null
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # null值应该被当作清空处理

# 兼容性测试用例: 向下兼容性
- test:
    name: compatibility-case1-向下兼容性验证
    variables:
      signFlowId: $perf_test_signflow_id
      businessNo: ""
      # 不传入signFlowExpireTime字段
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 不传入预留字段应该正常工作

# 验证测试用例: 检查最终状态
- test:
    name: final-check-验证最终更新结果
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: ""
      signFlowId: $perf_test_signflow_id
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 验证最终的contractExpirationTime状态

# Teardown: 清理测试数据
- test:
    name: teardown-删除性能测试签署流程
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      businessNo: ""
      signFlowId: $perf_test_signflow_id
      reason: $test_reason
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
