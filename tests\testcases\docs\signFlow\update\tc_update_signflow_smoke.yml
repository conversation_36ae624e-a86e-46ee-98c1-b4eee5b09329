- config:
    name: "更新签署流程接口冒烟测试"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      - future_time_1: ${get_future_time(1)}  # 当前时间+1天
      - future_time_2: ${get_future_time(2)}  # 当前时间+2天
      - past_time: ${get_past_time(1)}        # 当前时间-1天
      - test_reason: "自动化冒烟测试更新签署流程"
      - second: 3
      - fileKey0: ${ENV(fileKey)}
      - userCode0: ${ENV(sign01.userCode)}
      - customAccountNoSigner0: ${ENV(sign01.accountNo)}
      - customOrgNoSigner0: ${ENV(sign01.main.orgNo)}

# Setup: 创建测试用的签署流程
- test:
    name: setup-创建测试签署流程
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessNo: ${random_str(20)}
      signFlowExpireTime: $future_time_2
      subject: "自动化冒烟测试更新签署流程"
      remark: "自动化冒烟测试更新签署流程"
      fileKey: $fileKey0
      userCodeSigner: $userCode0
      customAccountNoSigner: $customAccountNoSigner0
      customOrgNoSigner: $customOrgNoSigner0
    setup_hooks:
      - ${sleep($second)}
    extract:
      smoke_test_signflow_id: content.data.signFlowId
      smoke_test_business_no: content.data.businessNo
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 冒烟测试用例1: 基本功能验证
- test:
    name: smoke-case1-使用signFlowId更新contractExpirationTime基本功能
    variables:
      signFlowId: $smoke_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_2
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 冒烟测试用例2: 参数验证
- test:
    name: smoke-case2-使用businessNo更新contractExpirationTime
    variables:
      signFlowId: ""
      businessNo: $smoke_test_business_no
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_2
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 冒烟测试用例3: 参数优先级验证
- test:
    name: smoke-case3-signFlowId和businessNo优先级验证
    variables:
      signFlowId: $smoke_test_signflow_id
      businessNo: "different_business_no"  # 不同的businessNo
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_2
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 应该以signFlowId为准进行更新

# 冒烟测试用例4: 时间格式验证
- test:
    name: smoke-case4-contractExpirationTime正确格式验证
    variables:
      signFlowId: $smoke_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "2024-12-31 23:59:59"
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 冒烟测试用例5: 时间格式错误验证
- test:
    name: smoke-case5-contractExpirationTime错误格式验证
    variables:
      signFlowId: $smoke_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: "2024/12/31 23:59:59"  # 错误格式
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回格式错误

# 冒烟测试用例6: 日期校验验证
- test:
    name: smoke-case6-contractExpirationTime大于当前时间验证
    variables:
      signFlowId: $smoke_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 冒烟测试用例7: 日期校验失败验证
- test:
    name: smoke-case7-contractExpirationTime小于当前时间验证
    variables:
      signFlowId: $smoke_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $past_time
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回"合同到期日期应大于当前时间"错误

# 冒烟测试用例8: 必填参数验证
- test:
    name: smoke-case8-signFlowId和businessNo都为空验证
    variables:
      signFlowId: ""
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回参数缺失错误

# 冒烟测试用例9: 无效ID验证
- test:
    name: smoke-case9-无效signFlowId验证
    variables:
      signFlowId: "invalid_signflow_id_123"
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回流程不存在错误

# 冒烟测试用例10: signFlowExpireTime预留字段验证
- test:
    name: smoke-case10-signFlowExpireTime预留字段正确格式验证
    variables:
      signFlowId: $smoke_test_signflow_id
      businessNo: ""
      signFlowExpireTime: "2024-12-31 23:59:59"
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 会校验格式但不会真的落库操作

# 冒烟测试用例11: signFlowExpireTime预留字段错误格式验证
- test:
    name: smoke-case11-signFlowExpireTime预留字段错误格式验证
    variables:
      signFlowId: $smoke_test_signflow_id
      businessNo: ""
      signFlowExpireTime: "2024/12/31 23:59:59"  # 错误格式
      contractExpirationTime: $future_time_1
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - ne: [content.code, 200]
      # 应该返回格式错误

# 冒烟测试用例12: 清空到期日期验证
- test:
    name: smoke-case12-清空contractExpirationTime验证
    variables:
      signFlowId: $smoke_test_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: ""  # 传入空值
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 验证测试用例: 检查更新结果
- test:
    name: smoke-check-验证contractExpirationTime更新结果
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: ""
      signFlowId: $smoke_test_signflow_id
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 验证contractExpirationTime是否被清空

# Teardown: 清理测试数据
- test:
    name: smoke-teardown-删除测试签署流程
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      businessNo: ""
      signFlowId: $smoke_test_signflow_id
      reason: $test_reason
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
