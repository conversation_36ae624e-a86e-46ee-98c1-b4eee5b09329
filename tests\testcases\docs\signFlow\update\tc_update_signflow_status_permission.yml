- config:
    name: "更新签署流程接口状态和权限测试"
    base_url: ${ENV(esign.gatewayHost)}
    variables:
      - future_time_1: ${get_future_time(1)}  # 当前时间+1天
      - future_time_2: ${get_future_time(2)}  # 当前时间+2天
      - test_reason: "自动化测试更新签署流程状态权限"
      - second: 3
      - fileKey0: ${ENV(fileKey)}
      - userCode0: ${ENV(sign01.userCode)}
      - customAccountNoSigner0: ${ENV(sign01.accountNo)}
      - customOrgNoSigner0: ${ENV(sign01.main.orgNo)}

# Setup: 创建不同状态的测试签署流程
- test:
    name: setup-创建填写中状态流程
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessNo: ${random_str(20)}
      signFlowExpireTime: $future_time_2
      subject: "自动化测试状态权限-填写中"
      remark: "自动化测试状态权限-填写中"
      fileKey: $fileKey0
      userCodeSigner: $userCode0
      customAccountNoSigner: $customAccountNoSigner0
      customOrgNoSigner: $customOrgNoSigner0
    setup_hooks:
      - ${sleep($second)}
    extract:
      filling_signflow_id: content.data.signFlowId
      filling_business_no: content.data.businessNo
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

- test:
    name: setup-创建签署中状态流程
    api: api/esignSigns/signFlow/createAndStart.yml
    variables:
      businessNo: ${random_str(20)}
      signFlowExpireTime: $future_time_2
      subject: "自动化测试状态权限-签署中"
      remark: "自动化测试状态权限-签署中"
      fileKey: $fileKey0
      userCodeSigner: $userCode0
      customAccountNoSigner: $customAccountNoSigner0
      customOrgNoSigner: $customOrgNoSigner0
    setup_hooks:
      - ${sleep($second)}
    extract:
      signing_signflow_id: content.data.signFlowId
      signing_business_no: content.data.businessNo
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 流程状态限制验证 - 支持状态
- test:
    name: case1-更新填写中状态流程contractExpirationTime
    variables:
      signFlowId: $filling_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_2
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

- test:
    name: case2-更新签署中状态流程contractExpirationTime
    variables:
      signFlowId: $signing_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_2
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

# 权限验证测试
- test:
    name: case3-流程发起人更新权限验证
    variables:
      signFlowId: $filling_signflow_id
      businessNo: ""
      signFlowExpireTime: ""
      contractExpirationTime: $future_time_2
    api: api/esignDocs/signFlow/update.yml
    setup_hooks:
      - ${sleep($second)}
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 流程发起人应该有更新权限

# 验证更新结果
- test:
    name: check-验证填写中流程更新结果
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: ""
      signFlowId: $filling_signflow_id
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 验证contractExpirationTime是否更新为future_time_2

- test:
    name: check-验证签署中流程更新结果
    api: api/esignSigns/signFlow/signDetail.yml
    variables:
      businessNo: ""
      signFlowId: $signing_signflow_id
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
      # 验证contractExpirationTime是否更新成功

# Teardown: 清理测试数据
- test:
    name: teardown-删除填写中状态流程
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      businessNo: ""
      signFlowId: $filling_signflow_id
      reason: $test_reason
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]

- test:
    name: teardown-删除签署中状态流程
    api: api/esignSigns/signFlow/revoke.yml
    variables:
      businessNo: ""
      signFlowId: $signing_signflow_id
      reason: $test_reason
    validate:
      - eq: [content.code, 200]
      - eq: [content.message, "成功"]
