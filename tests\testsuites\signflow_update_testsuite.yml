config:
    name: "更新签署流程接口测试套件"
    variables:
        test_env: ${ENV(test_env)}
    base_url: ${ENV(esign.gatewayHost)}

testcases:
    # 冒烟测试 - 优先执行，验证核心功能
    - name: "更新签署流程接口冒烟测试"
      testcase: testcases/docs/signFlow/update/tc_update_signflow_smoke.yml
      
    # 基础功能测试 - 全面的参数和格式验证
    - name: "更新签署流程接口基础功能测试"
      testcase: testcases/docs/signFlow/update/tc_update_signflow_1.yml
      
    # 状态和权限测试 - 业务规则验证
    - name: "更新签署流程接口状态和权限测试"
      testcase: testcases/docs/signFlow/update/tc_update_signflow_status_permission.yml
      
    # 性能和异常测试 - 稳定性和安全性验证
    - name: "更新签署流程接口性能和异常测试"
      testcase: testcases/docs/signFlow/update/tc_update_signflow_performance_exception.yml
