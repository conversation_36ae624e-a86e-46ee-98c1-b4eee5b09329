# 合同到期提醒功能-测试用例

## 功能测试

### 业务模板配置

#### TL-业务模板合同到期自动提醒默认配置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，找到签署方式-通知设置

##### 步骤二：查看"合同到期自动提醒"配置项状态和默认值

##### 步骤三：检查各项默认参数设置

##### ER-预期结果：1：合同到期自动提醒默认开启；2：前N天开始提醒默认30天；3：每N天默认15天；4：提醒时间默认10:00；5：次数限制默认3次；

#### TL-提醒频率参数边界值设置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，点击合同到期自动提醒设置

##### 步骤二：设置前N天开始提醒为1、999、1000进行保存

##### 步骤三：设置每N天为1、999、1000进行保存

##### 步骤四：设置次数限制为1、99、100进行保存

##### ER-预期结果：1：前N天开始提醒1-999范围内可正常保存；2：每N天1-999范围内可正常保存；3：次数限制1-99范围内可正常保存；4：超出范围的值无法保存并提示错误；

#### TL-提醒时间格式设置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，点击合同到期自动提醒设置

##### 步骤二：设置提醒时间为0:00、12:30、23:59进行保存

##### 步骤三：尝试设置提醒时间为24:00、25:30等无效时间

##### ER-预期结果：1：有效时间格式0:00-23:59可正常保存；2：无效时间格式无法保存并提示错误；3：时间选择器限制在有效范围内；

#### TL-签署通知策略合同到期通知消息配置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，找到签署通知策略设置

##### 步骤二：在合同到期通知消息中选择提醒对象：发起人、签署人、抄送人

##### 步骤三：选择提醒方式：站内信、邮箱、短信、第三方通道

##### 步骤四：保存配置

##### ER-预期结果：1：可多选提醒对象；2：提醒方式受消息模板通道控制显示；3：配置保存成功；4：短信默认关闭状态；

### 用户信息消息通知配置

#### TL-用户信息合同到期通知消息配置验证

##### PD-前置条件：用户已登录；

##### 步骤一：进入用户信息-消息通知设置页面

##### 步骤二：查看合同到期通知消息配置项

##### 步骤三：检查短信通知默认状态

##### 步骤四：修改各通知方式开关状态

##### ER-预期结果：1：存在合同到期通知消息配置项；2：短信默认不发送（关闭状态）；3：可修改各通知方式开关；4：配置保存成功；

#### TL-用户中心消息配置与业务模板配置交互验证

##### PD-前置条件：用户已登录；业务模板已配置合同到期提醒；

##### 步骤一：在业务模板中开启所有通知方式

##### 步骤二：在用户中心关闭部分通知方式

##### 步骤三：触发合同到期提醒

##### 步骤四：检查实际发送的通知方式

##### ER-预期结果：1：根据业务模板和用户中心配置的交集发送通知；2：用户中心关闭的通知方式不发送；3：通知发送逻辑正确；

### 消息模板管理

#### TL-合同到期通知消息模板创建验证

##### PD-前置条件：用户已登录；具有消息模板管理权限；

##### 步骤一：进入消息模板管理页面

##### 步骤二：创建短信、邮件、站内信、第三方通道的合同到期通知模板

##### 步骤三：设置模板内容和变量

##### 步骤四：保存模板配置

##### ER-预期结果：1：可创建四种类型的消息模板；2：模板内容支持变量替换；3：模板保存成功；4：模板状态正常；

#### TL-通知内容合并发送规则验证

##### PD-前置条件：存在多个到期流程；配置了合同到期提醒；

##### 步骤一：设置多个流程在同一天到期

##### 步骤二：等待定时任务执行

##### 步骤三：检查邮件通知内容

##### 步骤四：检查短信和站内信通知内容

##### ER-预期结果：1：邮件内可以体现具体流程详情；2：短信和站内信采用合并发送方式；3：通知内容格式正确；4：信息完整准确；

### 管理平台通知开关

#### TL-管理平台消息通知开关配置验证

##### PD-前置条件：用户已登录；具有管理平台权限；

##### 步骤一：进入管理平台-消息设置页面

##### 步骤二：查看合同到期通知的通知开关

##### 步骤三：修改开关状态并保存

##### 步骤四：验证开关对通知发送的影响

##### ER-预期结果：1：存在合同到期通知开关；2：短信默认关闭；3：开关状态可修改；4：关闭后不发送对应通知；

#### TL-业务模板通知开关配置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；

##### 步骤一：进入业务模板编辑页面

##### 步骤二：查看合同到期通知开关配置

##### 步骤三：修改各通知方式开关状态

##### 步骤四：保存配置并验证效果

##### ER-预期结果：1：业务模板支持合同到期通知开关设置；2：短信默认关闭；3：开关状态可独立控制；4：配置生效正确；

#### TL-个人信息通知开关配置验证

##### PD-前置条件：用户已登录；

##### 步骤一：进入个人信息-消息通知设置页面

##### 步骤二：查看合同到期通知开关配置

##### 步骤三：修改各通知方式开关状态

##### 步骤四：保存配置并验证效果

##### ER-预期结果：1：个人信息支持合同到期通知开关设置；2：短信默认关闭；3：个人设置优先级高于模板设置；4：配置保存成功；

### 定时任务发送机制

#### TL-定时任务按业务模板配置时间发送验证

##### PD-前置条件：业务模板设置提醒时间为10:00；存在需要提醒的流程；

##### 步骤一：等待每日10:00时间点

##### 步骤二：监控定时任务执行情况

##### 步骤三：检查消息发送时间

##### 步骤四：验证发送对象和内容

##### ER-预期结果：1：定时任务在10:00准时执行；2：消息发送时间正确；3：发送给正确的用户；4：消息内容准确；

#### TL-多个业务模板不同时间定时发送验证

##### PD-前置条件：模板A设置10:00提醒；模板B设置14:00提醒；都有到期流程；

##### 步骤一：等待10:00时间点，检查模板A流程提醒

##### 步骤二：等待14:00时间点，检查模板B流程提醒

##### 步骤三：验证两个时间点的发送情况

##### ER-预期结果：1：10:00只发送模板A相关提醒；2：14:00只发送模板B相关提醒；3：时间隔离正确；4：发送内容准确；

### 提醒规则逻辑

#### TL-合同到期前按规则发送提醒验证

##### PD-前置条件：签署流程已完成；设置到期日期为30天后；配置前30天开始每15天提醒一次；

##### 步骤一：等待第一次提醒时间（到期前30天）

##### 步骤二：等待第二次提醒时间（到期前15天）

##### 步骤三：等待第三次提醒时间（到期当天）

##### 步骤四：检查到期后是否还有提醒

##### ER-预期结果：1：到期前30天发送第一次提醒；2：到期前15天发送第二次提醒；3：到期当天发送第三次提醒；4：到期后不再发送提醒；

#### TL-签署完成时已到期次日提醒验证

##### PD-前置条件：签署流程设置了过去的到期日期；

##### 步骤一：完成签署流程

##### 步骤二：等待次日定时任务执行

##### 步骤三：检查通知发送情况

##### ER-预期结果：1：签署完成次日发送提醒通知；2：通知内容包含已过期信息；3：发送给配置的提醒对象；

#### TL-签署完成时未到期但无其他提醒日通知验证

##### PD-前置条件：签署流程未到期；但距离到期日小于提醒开始天数；

##### 步骤一：完成签署流程

##### 步骤二：等待到期日当天

##### 步骤三：检查通知发送情况

##### ER-预期结果：1：在合同到期日发送提醒通知；2：通知内容正确；3：发送给配置的提醒对象；

#### TL-次数限制控制验证

##### PD-前置条件：配置次数限制为3次；设置提醒规则；

##### 步骤一：等待前3次提醒发送

##### 步骤二：检查第4次是否还会发送

##### 步骤三：验证次数限制生效

##### ER-预期结果：1：前3次提醒正常发送；2：第4次及以后不再发送；3：次数限制控制正确；

## 异常测试

### 特殊场景处理

#### TL-或签节点一方已签署另一方无需签署通知处理验证

##### PD-前置条件：设置了或签节点；一方已完成签署；配置了到期提醒；

##### 步骤一：完成或签节点中一方的签署

##### 步骤二：等待到期提醒时间

##### 步骤三：检查通知发送对象

##### ER-预期结果：1：无需签署的签署人不接收通知；2：其他相关人员正常接收通知；3：通知内容正确；

#### TL-转交已签流程发起人通知对象变更验证

##### PD-前置条件：签署流程已完成；发起人已转交；配置了到期提醒；

##### 步骤一：完成流程转交

##### 步骤二：等待到期提醒时间

##### 步骤三：检查通知发送对象

##### ER-预期结果：1：通知发送给转交后的发起人；2：原发起人不再接收通知；3：其他通知对象不受影响；

#### TL-流程全部作废后不再通知验证

##### PD-前置条件：签署流程已设置到期提醒；

##### 步骤一：作废签署流程

##### 步骤二：等待原定的提醒时间

##### 步骤三：检查通知发送情况

##### ER-预期结果：1：作废流程不再发送到期提醒；2：定时任务不处理作废流程；3：系统日志记录正确；

### 系统异常

#### TL-消息服务异常时到期提醒处理验证

##### PD-前置条件：签署流程已完成；到达提醒时间；消息服务异常；

##### 步骤一：模拟消息服务不可用

##### 步骤二：定时任务执行到期提醒

##### 步骤三：检查系统处理机制

##### ER-预期结果：1：系统记录发送失败日志；2：具备重试机制；3：不影响其他正常流程的提醒；

#### TL-定时任务异常时处理验证

##### PD-前置条件：存在需要提醒的流程；定时任务服务异常；

##### 步骤一：模拟定时任务服务故障

##### 步骤二：检查系统监控和告警

##### 步骤三：恢复服务后检查补发机制

##### ER-预期结果：1：系统监控发现异常；2：产生告警通知；3：服务恢复后有补发机制；

## 性能测试

### 并发性能

#### TL-大量流程同时到期提醒性能验证

##### PD-前置条件：存在大量已完成的签署流程；同一天需要发送到期提醒；

##### 步骤一：准备1000个流程在同一天到期

##### 步骤二：定时任务执行到期提醒

##### 步骤三：监控系统性能指标

##### ER-预期结果：1：定时任务在合理时间内完成；2：系统资源使用正常；3：所有提醒正常发送；4：不影响其他业务功能；

#### TL-多种通知方式并发发送性能验证

##### PD-前置条件：配置了站内信、邮箱、短信、第三方通道；大量用户需要通知；

##### 步骤一：触发大量合同到期提醒

##### 步骤二：监控各通道发送性能

##### 步骤三：检查发送成功率

##### ER-预期结果：1：各通道并发发送正常；2：发送成功率达标；3：系统响应时间合理；4：资源使用均衡；

## 兼容性测试

### 浏览器兼容性

#### TL-不同浏览器业务模板配置兼容性验证

##### PD-前置条件：准备Chrome、Firefox、Safari、Edge浏览器；

##### 步骤一：在Chrome浏览器中配置合同到期提醒

##### 步骤二：在Firefox浏览器中配置合同到期提醒

##### 步骤三：在Safari浏览器中配置合同到期提醒

##### 步骤四：在Edge浏览器中配置合同到期提醒

##### ER-预期结果：1：所有浏览器都能正常显示配置页面；2：配置功能正常；3：页面布局正确；4：交互体验一致；

### 移动端兼容性

#### TL-移动端消息通知接收兼容性验证

##### PD-前置条件：准备iOS和Android设备；配置了合同到期提醒；

##### 步骤一：在iOS设备上接收站内信通知

##### 步骤二：在Android设备上接收站内信通知

##### 步骤三：检查短信和邮件在移动端的显示

##### ER-预期结果：1：iOS设备正常接收通知；2：Android设备正常接收通知；3：通知内容显示正确；4：链接跳转正常；

## 安全测试

### 权限控制

#### TL-无权限用户访问业务模板配置验证

##### PD-前置条件：用户无业务模板管理权限；

##### 步骤一：尝试访问业务模板编辑页面

##### 步骤二：尝试修改合同到期提醒配置

##### 步骤三：检查系统权限控制

##### ER-预期结果：1：无权限用户无法访问配置页面；2：系统返回权限错误提示；3：系统记录访问日志；

#### TL-消息模板管理权限控制验证

##### PD-前置条件：用户无消息模板管理权限；

##### 步骤一：尝试访问消息模板管理页面

##### 步骤二：尝试创建或修改合同到期通知模板

##### 步骤三：检查权限控制效果

##### ER-预期结果：1：无权限用户无法访问模板管理；2：无法创建或修改模板；3：系统记录权限违规日志；

## 五、测试用例优化

**优化维度检查：**
1. 完整性检查：覆盖了业务模板配置、消息通知、定时任务、特殊场景等所有功能点
2. 准确性检查：步骤描述清晰，期望结果明确，测试数据准确
3. 可执行性检查：前置条件明确，步骤可操作，环境要求清晰
4. 可维护性检查：用例独立性好，步骤清晰，数据可重用

**补充遗漏场景：**

### 数据处理

#### TL-时区处理合同到期提醒时间验证

##### PD-前置条件：用户在不同时区；业务模板设置提醒时间为10:00；

##### 步骤一：在东八区设置业务模板提醒时间

##### 步骤二：切换到其他时区查看提醒时间

##### 步骤三：检查实际提醒发送时间

##### ER-预期结果：1：提醒时间显示正确；2：提醒按服务器时区发送；3：时区转换准确；

#### TL-历史数据兼容性验证

##### PD-前置条件：存在升级前的历史业务模板；

##### 步骤一：查询历史模板的到期提醒配置

##### 步骤二：为历史模板配置到期提醒

##### 步骤三：检查历史模板的提醒功能

##### ER-预期结果：1：历史模板到期提醒配置为空；2：可为历史模板配置提醒；3：配置后提醒功能正常；

### 边界场景

#### TL-消息模板通道控制显示验证

##### PD-前置条件：部分消息模板通道被禁用；

##### 步骤一：禁用短信消息模板通道

##### 步骤二：进入业务模板配置页面

##### 步骤三：查看提醒方式选项

##### ER-预期结果：1：被禁用的通道不显示在选项中；2：可用通道正常显示；3：配置保存正确；

## 六、测试用例评审及补充

**评审结果：**
1. 需求覆盖度：95%
2. 场景完整性：92%
3. 步骤合理性：95%
4. 结果可验证性：95%
5. 数据充分性：90%

**遗漏场景：无重大遗漏，已补充时区处理和历史数据兼容性场景**

## 七、冒烟测试用例提取

### MYTL-业务模板合同到期自动提醒基本配置冒烟验证

#### PD-前置条件：用户已登录；具有业务模板管理权限；

#### 步骤一：进入业务模板编辑页面

#### 步骤二：开启合同到期自动提醒

#### 步骤三：设置基本提醒参数并保存

#### ER-预期结果：1：开关正常开启；2：默认参数正确；3：配置保存成功；

### MYTL-提醒对象和方式配置冒烟验证

#### PD-前置条件：业务模板已开启合同到期提醒；

#### 步骤一：配置提醒对象为发起人、签署人

#### 步骤二：配置提醒方式为站内信、邮箱

#### 步骤三：保存配置

#### ER-预期结果：1：提醒对象配置成功；2：提醒方式配置成功；3：短信默认关闭；

### MYTL-用户信息消息通知配置冒烟验证

#### PD-前置条件：用户已登录；

#### 步骤一：进入用户信息-消息通知设置

#### 步骤二：查看合同到期通知配置

#### 步骤三：修改通知开关状态

#### ER-预期结果：1：存在合同到期通知配置；2：短信默认关闭；3：开关修改成功；

### MYTL-定时任务发送消息冒烟验证

#### PD-前置条件：业务模板配置了提醒时间；存在需要提醒的流程；

#### 步骤一：等待定时任务执行时间

#### 步骤二：检查消息发送情况

#### 步骤三：验证发送内容

#### ER-预期结果：1：定时任务按时执行；2：消息成功发送；3：发送内容正确；

### MYTL-合同到期前按规则提醒冒烟验证

#### PD-前置条件：签署流程已完成；配置了提醒规则；

#### 步骤一：等待第一次提醒时间

#### 步骤二：检查提醒发送情况

#### 步骤三：验证提醒内容

#### ER-预期结果：1：按时发送提醒；2：提醒对象正确；3：提醒内容准确；

### MYTL-流程作废后停止提醒冒烟验证

#### PD-前置条件：签署流程已设置到期提醒；

#### 步骤一：作废签署流程

#### 步骤二：等待原定提醒时间

#### 步骤三：检查是否还有提醒

#### ER-预期结果：1：流程作废成功；2：不再发送提醒；3：系统处理正确；

## 八、线上验证用例提取

### PATL-端到端合同到期提醒完整流程线上验证

#### PD-前置条件：生产环境；真实用户账号；

#### 步骤一：配置业务模板合同到期提醒规则

#### 步骤二：发起签署流程并完成签署

#### 步骤三：等待到期提醒时间并接收通知

#### ER-预期结果：1：模板配置成功；2：流程签署完成；3：按时收到到期提醒；

### PATL-多种通知方式线上验证

#### PD-前置条件：生产环境；配置了多种通知渠道；

#### 步骤一：设置站内信、邮箱通知

#### 步骤二：等待合同到期提醒触发

#### 步骤三：检查各通道通知接收情况

#### ER-预期结果：1：站内信正常接收；2：邮箱通知正常；3：通知内容准确；

### PATL-用户中心消息配置生效线上验证

#### PD-前置条件：生产环境；真实用户账号；

#### 步骤一：在用户中心关闭部分通知方式

#### 步骤二：在业务模板开启所有通知方式

#### 步骤三：等待到期提醒验证实际发送情况

#### ER-预期结果：1：用户中心配置生效；2：关闭的通知方式不发送；3：开启的通知方式正常发送；

### PATL-特殊场景线上验证

#### PD-前置条件：生产环境；存在或签节点和转交场景；

#### 步骤一：创建包含或签节点的流程

#### 步骤二：完成部分签署使另一方无需签署

#### 步骤三：执行流程转交操作

#### 步骤四：等待到期提醒验证通知对象

#### ER-预期结果：1：或签逻辑正确；2：无需签署方不收到通知；3：转交后通知对象正确；

### PATL-大量流程到期提醒性能线上验证

#### PD-前置条件：生产环境；存在大量到期流程；

#### 步骤一：监控定时任务执行时间

#### 步骤二：检查系统资源使用情况

#### 步骤三：验证通知发送效率

#### ER-预期结果：1：定时任务按时执行；2：系统资源使用正常；3：通知发送及时；4：不影响其他业务；

### PATL-消息模板通道控制线上验证

#### PD-前置条件：生产环境；部分消息模板通道被管控；

#### 步骤一：检查被管控通道的显示状态

#### 步骤二：配置可用通道的提醒方式

#### 步骤三：验证实际通知发送情况

#### ER-预期结果：1：被管控通道不显示；2：可用通道配置成功；3：通知按配置正常发送；
