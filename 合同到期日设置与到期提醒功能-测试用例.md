# 合同到期日设置与到期提醒功能-测试用例

## 功能测试

### 业务模板配置

#### TL-业务模板开启合同到期自动提醒默认配置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，找到签署方式-通知设置

##### 步骤二：查看"合同到期自动提醒"配置项状态

##### 步骤三：检查默认配置参数值

##### ER-预期结果：1：合同到期自动提醒开关默认开启；2：前N天开始提醒默认30天；3：每N天默认15天；4：提醒时间默认10:00；5：次数限制默认3次；

#### TL-业务模板提醒规则参数边界值设置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，点击合同到期自动提醒设置

##### 步骤二：分别设置前N天开始提醒为1、999、1000进行保存

##### 步骤三：分别设置每N天为1、999、1000进行保存

##### 步骤四：分别设置次数限制为1、99、100进行保存

##### ER-预期结果：1：前N天开始提醒1-999范围内可正常保存；2：超出范围时显示错误提示；3：每N天1-999范围内可正常保存；4：次数限制1-99范围内可正常保存；5：超出范围的值无法保存并提示错误；

#### TL-业务模板提醒时间格式设置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，点击合同到期自动提醒设置

##### 步骤二：设置提醒时间为0:00、12:30、23:59进行保存

##### 步骤三：尝试设置提醒时间为24:00、25:30等无效时间

##### ER-预期结果：1：有效时间格式0:00-23:59可正常保存；2：无效时间格式无法保存并提示错误；3：时间选择器限制在有效范围内；

#### TL-业务模板提醒对象和方式配置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，找到签署通知策略设置

##### 步骤二：在合同到期通知消息中选择提醒对象：发起人、签署人、抄送人

##### 步骤三：选择提醒方式：站内信、邮箱、短信、第三方通道

##### 步骤四：保存配置

##### ER-预期结果：1：可多选提醒对象；2：提醒方式受消息模板通道控制显示；3：配置保存成功；4：短信默认关闭状态；

### 合同到期时间设置

#### TL-业务模板合同到期日期开关控制验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，找到签署流程配置

##### 步骤二：关闭"合同到期日期与到期提醒"开关

##### 步骤三：开启"合同到期日期与到期提醒"开关，点击设置

##### 步骤四：查看3种配置方式选项

##### ER-预期结果：1：开关关闭时默认无设置；2：开关开启时可点击设置；3：显示发起签署后固定时长、签署完成后固定时长、发起时指定三种方式；

#### TL-发起签署后固定时长配置验证

##### PD-前置条件：用户已登录；业务模板已开启合同到期设置；

##### 步骤一：选择"发起签署后固定时长"配置方式

##### 步骤二：设置时间单位为年、月、天

##### 步骤三：设置具体数值并保存

##### 步骤四：使用该模板发起签署流程

##### ER-预期结果：1：支持年、月、天三种时间单位；2：可设置具体数值；3：配置保存成功；4：发起时自动计算到期日期；

#### TL-签署完成后固定时长配置验证

##### PD-前置条件：用户已登录；业务模板已开启合同到期设置；

##### 步骤一：选择"签署完成后固定时长"配置方式

##### 步骤二：设置时间单位和数值

##### 步骤三：保存配置并使用该模板发起签署

##### 步骤四：完成签署流程，检查到期日期计算

##### ER-预期结果：1：配置保存成功；2：签署完成后自动计算到期日期；3：到期日期基于签署完成时间计算；

#### TL-发起时指定配置验证

##### PD-前置条件：用户已登录；业务模板已开启合同到期设置；

##### 步骤一：选择"发起时指定"配置方式并保存

##### 步骤二：使用该模板进行单份发起（PC）

##### 步骤三：使用该模板进行单份发起（H5）

##### 步骤四：使用该模板进行批量发起

##### ER-预期结果：1：单份发起PC页面显示合同到期日期设置项；2：单份发起H5页面显示合同到期日期设置项；3：批量发起Excel模板增加"合同到期日期"列；4：显示提示文案"系统将在合同到期前{total}天通知流程参与人"；

### 发起页面功能

#### TL-单份发起PC端合同到期日期设置验证

##### PD-前置条件：用户已登录；业务模板配置为发起时指定；

##### 步骤一：进入单份发起页面（PC端）

##### 步骤二：选择配置了合同到期的业务模板

##### 步骤三：设置合同到期日期为未来日期

##### 步骤四：设置合同到期日期为过去日期

##### 步骤五：完成发起流程

##### ER-预期结果：1：页面动态显示合同到期日期设置项；2：未来日期可正常设置；3：过去日期提示错误；4：显示提醒规则提示文案；5：发起成功；

#### TL-单份发起H5端合同到期日期设置验证

##### PD-前置条件：用户已登录；业务模板配置为发起时指定；

##### 步骤一：进入单份发起页面（H5端）

##### 步骤二：选择配置了合同到期的业务模板

##### 步骤三：点击合同到期日期设置

##### 步骤四：使用日期选择器选择日期

##### 步骤五：完成发起流程

##### ER-预期结果：1：H5页面正确显示合同到期日期设置；2：日期选择器功能正常；3：选择的日期正确显示；4：发起流程成功；

#### TL-批量发起合同到期日期设置验证

##### PD-前置条件：用户已登录；业务模板配置为发起时指定；

##### 步骤一：进入批量发起页面

##### 步骤二：选择配置了合同到期的业务模板

##### 步骤三：下载Excel模板

##### 步骤四：填写Excel模板包含合同到期日期列

##### 步骤五：上传Excel文件并发起

##### ER-预期结果：1：Excel模板包含"合同到期日期"列；2：可正确填写日期格式；3：上传解析成功；4：批量发起成功；5：每个流程都有对应的到期日期；

### 通知发送功能

#### TL-合同到期前按规则发送提醒验证

##### PD-前置条件：签署流程已完成；设置了合同到期日期；配置了提醒规则；

##### 步骤一：等待到达提醒时间点

##### 步骤二：检查系统定时任务执行

##### 步骤三：查看各通道通知发送情况

##### 步骤四：验证提醒频率和次数控制

##### ER-预期结果：1：按设定时间发送提醒；2：通知内容正确；3：发送给指定对象；4：按频率和次数限制执行；5：到期后不再提醒；

#### TL-签署完成时已过期次日提醒验证

##### PD-前置条件：签署流程设置了过去的到期日期；

##### 步骤一：完成签署流程

##### 步骤二：等待次日定时任务执行

##### 步骤三：检查通知发送情况

##### ER-预期结果：1：签署完成次日发送提醒通知；2：通知内容包含已过期信息；3：发送给配置的提醒对象；

#### TL-签署完成时未到期但无其他提醒日通知验证

##### PD-前置条件：签署流程未到期；但距离到期日小于提醒开始天数；

##### 步骤一：完成签署流程

##### 步骤二：等待到期日当天

##### 步骤三：检查通知发送情况

##### ER-预期结果：1：在合同到期日发送提醒通知；2：通知内容正确；3：发送给配置的提醒对象；

## 接口测试

### OpenAPI接口

#### TL-一步发起签署接口增加合同到期时间参数验证

##### PD-前置条件：具有API调用权限；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：传入contractExpirationDate参数为未来日期

##### 步骤三：传入contractExpirationDate参数为过去日期

##### 步骤四：不传入contractExpirationDate参数

##### ER-预期结果：1：未来日期参数接受成功；2：过去日期返回错误；3：不传参数时使用模板默认配置；4：返回结果包含设置的到期时间；

#### TL-使用业务模板发起签署接口合同到期时间验证

##### PD-前置条件：具有API调用权限；业务模板已配置；

##### 步骤一：调用/esign-signs/v1/signFlow/createByBizTemplate接口

##### 步骤二：传入有效的contractExpirationDate参数

##### 步骤三：传入无效的contractExpirationDate参数

##### 步骤四：检查与业务模板配置的交互

##### ER-预期结果：1：有效参数创建成功；2：无效参数返回错误提示；3：参数优先级高于模板配置；4：创建的流程包含正确的到期时间；

#### TL-创建签署流程接口合同到期时间验证

##### PD-前置条件：具有API调用权限；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/create接口

##### 步骤二：传入contractExpirationDate参数

##### 步骤三：验证参数格式和有效性

##### 步骤四：检查创建结果

##### ER-预期结果：1：接口接受contractExpirationDate参数；2：参数验证规则正确；3：创建的流程包含到期时间；4：返回结果正确；

#### TL-签署详情接口返回合同到期时间验证

##### PD-前置条件：签署流程已创建；设置了合同到期时间；

##### 步骤一：调用/esign-signs/v1/signFlow/signDetail接口

##### 步骤二：传入有效的流程ID

##### 步骤三：检查返回结果中的contractExpirationDate字段

##### ER-预期结果：1：返回结果包含contractExpirationDate字段；2：字段值与设置的到期时间一致；3：时间格式正确；

#### TL-获取签署流程列表接口合同到期时间查询验证

##### PD-前置条件：存在多个签署流程；部分设置了合同到期时间；

##### 步骤一：调用/esign-signs/v1/signFlow/list接口

##### 步骤二：传入contractExpirationDateStartTime和contractExpirationDateEndTime参数

##### 步骤三：验证时间区间查询功能

##### 步骤四：检查返回结果中的contractExpirationDate字段

##### ER-预期结果：1：支持按到期时间区间查询；2：查询结果准确；3：返回结果包含contractExpirationDate字段；4：分页功能正常；

#### TL-签署回调增加合同到期时间验证

##### PD-前置条件：配置了回调地址；签署流程设置了合同到期时间；

##### 步骤一：触发签署环节开启事件

##### 步骤二：触发签署方签署完成事件

##### 步骤三：触发签署流程完成事件

##### 步骤四：检查回调数据中的contractExpirationDate字段

##### ER-预期结果：1：所有回调事件都包含contractExpirationDate字段；2：字段值正确；3：回调数据格式正确；

### 更新接口

#### TL-更新签署流程接口修改合同到期日期验证

##### PD-前置条件：签署流程已存在；具有API调用权限；

##### 步骤一：调用/esign-docs/v1/signFlow/update接口

##### 步骤二：传入有效的signFlowId和未来的contractExpirationTime

##### 步骤三：传入有效的businessNo和过去的contractExpirationTime

##### 步骤四：传入无效的流程ID

##### ER-预期结果：1：有效ID和未来时间更新成功；2：过去时间提示"合同到期日期应大于当前时间"；3：无效ID提示流程不存在；4：更新后的流程包含新的到期时间；

## 异常测试

### 业务异常

#### TL-或签节点一方已签署另一方无需签署通知处理验证

##### PD-前置条件：设置了或签节点；一方已完成签署；配置了到期提醒；

##### 步骤一：完成或签节点中一方的签署

##### 步骤二：等待到期提醒时间

##### 步骤三：检查通知发送对象

##### ER-预期结果：1：无需签署的签署人不接收通知；2：其他相关人员正常接收通知；3：通知内容正确；

#### TL-转交已签流程发起人通知对象变更验证

##### PD-前置条件：签署流程已完成；发起人已转交；配置了到期提醒；

##### 步骤一：完成流程转交

##### 步骤二：等待到期提醒时间

##### 步骤三：检查通知发送对象

##### ER-预期结果：1：通知发送给转交后的发起人；2：原发起人不再接收通知；3：其他通知对象不受影响；

#### TL-流程全部作废后不再通知验证

##### PD-前置条件：签署流程已设置到期提醒；

##### 步骤一：作废签署流程

##### 步骤二：等待原定的提醒时间

##### 步骤三：检查通知发送情况

##### ER-预期结果：1：作废流程不再发送到期提醒；2：作废流程的合同到期时间为空；3：定时任务不处理作废流程；

### 系统异常

#### TL-消息服务异常时到期提醒处理验证

##### PD-前置条件：签署流程已完成；到达提醒时间；消息服务异常；

##### 步骤一：模拟消息服务不可用

##### 步骤二：定时任务执行到期提醒

##### 步骤三：检查系统处理机制

##### ER-预期结果：1：系统记录发送失败日志；2：具备重试机制；3：不影响其他正常流程的提醒；

#### TL-见证服务异常时合同到期日期同步验证

##### PD-前置条件：发起签署流程；设置了合同到期日期；见证服务异常；

##### 步骤一：发起包含到期日期的签署流程

##### 步骤二：模拟见证服务不可用

##### 步骤三：检查同步处理机制

##### ER-预期结果：1：签署流程正常创建；2：记录同步失败日志；3：具备重试同步机制；4：不影响主流程功能；

## 性能测试

### 并发性能

#### TL-大量流程同时到期提醒性能验证

##### PD-前置条件：存在大量已完成的签署流程；同一天需要发送到期提醒；

##### 步骤一：准备1000个流程在同一天到期

##### 步骤二：定时任务执行到期提醒

##### 步骤三：监控系统性能指标

##### ER-预期结果：1：定时任务在合理时间内完成；2：系统资源使用正常；3：所有提醒正常发送；4：不影响其他业务功能；

#### TL-批量发起包含到期日期性能验证

##### PD-前置条件：准备包含1000条记录的批量发起Excel；每条都设置到期日期；

##### 步骤一：上传批量发起Excel文件

##### 步骤二：执行批量发起操作

##### 步骤三：监控处理时间和系统性能

##### ER-预期结果：1：批量处理在合理时间内完成；2：所有流程都正确设置到期日期；3：系统响应正常；4：内存使用合理；

## 兼容性测试

### 浏览器兼容性

#### TL-不同浏览器合同到期日期设置兼容性验证

##### PD-前置条件：准备Chrome、Firefox、Safari、Edge浏览器；

##### 步骤一：在Chrome浏览器中设置合同到期日期

##### 步骤二：在Firefox浏览器中设置合同到期日期

##### 步骤三：在Safari浏览器中设置合同到期日期

##### 步骤四：在Edge浏览器中设置合同到期日期

##### ER-预期结果：1：所有浏览器都能正常显示日期选择器；2：日期设置功能正常；3：页面布局正确；4：交互体验一致；

### 移动端兼容性

#### TL-移动端H5合同到期日期设置兼容性验证

##### PD-前置条件：准备iOS和Android设备；

##### 步骤一：在iOS设备上访问H5发起页面

##### 步骤二：设置合同到期日期

##### 步骤三：在Android设备上访问H5发起页面

##### 步骤四：设置合同到期日期

##### ER-预期结果：1：iOS设备日期选择器正常；2：Android设备日期选择器正常；3：触摸交互体验良好；4：页面适配正确；

## 安全测试

### 权限控制

#### TL-无权限用户访问合同到期设置验证

##### PD-前置条件：用户无业务模板管理权限；

##### 步骤一：尝试访问业务模板编辑页面

##### 步骤二：尝试修改合同到期提醒配置

##### 步骤三：尝试调用更新接口

##### ER-预期结果：1：无权限用户无法访问配置页面；2：接口调用返回权限错误；3：系统记录访问日志；

#### TL-API接口参数安全验证

##### PD-前置条件：具有API调用权限；

##### 步骤一：传入SQL注入攻击的contractExpirationDate参数

##### 步骤二：传入XSS攻击脚本的参数

##### 步骤三：传入超长字符串参数

##### ER-预期结果：1：系统正确过滤恶意参数；2：返回参数格式错误提示；3：不影响系统安全；4：记录安全日志；

## 六、测试用例评审及补充

**评审结果：**
1. 需求覆盖度：95%
2. 场景完整性：90%
3. 步骤合理性：95%
4. 结果可验证性：95%
5. 数据充分性：90%

**遗漏场景补充：**

### 数据处理

#### TL-时区处理合同到期日期验证

##### PD-前置条件：用户在不同时区；设置了合同到期日期；

##### 步骤一：在东八区设置合同到期日期为2024-12-31

##### 步骤二：切换到其他时区查看到期日期

##### 步骤三：检查提醒发送时间

##### ER-预期结果：1：到期日期显示正确；2：提醒按服务器时区发送；3：时区转换准确；

#### TL-历史数据兼容性验证

##### PD-前置条件：存在升级前的历史签署流程；

##### 步骤一：查询历史流程的到期日期字段

##### 步骤二：为历史流程设置到期日期

##### 步骤三：检查历史流程的提醒功能

##### ER-预期结果：1：历史流程到期日期字段为空；2：可为历史流程设置到期日期；3：设置后提醒功能正常；

### 边界场景

#### TL-合同到期日期与签署截止日期冲突处理验证

##### PD-前置条件：签署流程设置了签署截止日期；

##### 步骤一：设置合同到期日期早于签署截止日期

##### 步骤二：设置合同到期日期晚于签署截止日期

##### 步骤三：检查系统处理逻辑

##### ER-预期结果：1：系统允许设置不同的日期；2：两个日期独立生效；3：提醒逻辑不冲突；

## 七、冒烟测试用例提取

### MYTL-业务模板基本配置冒烟验证

#### PD-前置条件：用户已登录；具有业务模板管理权限；

#### 步骤一：进入业务模板编辑页面

#### 步骤二：开启合同到期自动提醒开关

#### 步骤三：设置基本提醒参数并保存

#### ER-预期结果：1：开关正常开启；2：参数设置成功；3：配置保存成功；

### MYTL-发起时指定合同到期日期冒烟验证

#### PD-前置条件：业务模板配置为发起时指定；用户已登录；

#### 步骤一：进入单份发起页面

#### 步骤二：选择配置了合同到期的业务模板

#### 步骤三：设置合同到期日期为未来日期

#### 步骤四：完成发起流程

#### ER-预期结果：1：页面显示到期日期设置项；2：日期设置成功；3：流程发起成功；4：流程包含到期日期；

### MYTL-合同到期提醒发送冒烟验证

#### PD-前置条件：签署流程已完成；设置了合同到期日期；配置了提醒规则；

#### 步骤一：等待到达提醒时间点

#### 步骤二：检查系统定时任务执行

#### 步骤三：查看通知发送情况

#### ER-预期结果：1：定时任务正常执行；2：通知成功发送；3：通知内容正确；

### MYTL-OpenAPI接口基本功能冒烟验证

#### PD-前置条件：具有API调用权限；接口服务正常；

#### 步骤一：调用创建签署流程接口

#### 步骤二：传入contractExpirationDate参数

#### 步骤三：调用签署详情接口查看结果

#### ER-预期结果：1：接口调用成功；2：参数接受正确；3：详情接口返回到期时间；

### MYTL-更新签署流程接口冒烟验证

#### PD-前置条件：签署流程已存在；具有API调用权限；

#### 步骤一：调用更新签署流程接口

#### 步骤二：传入有效的contractExpirationTime

#### 步骤三：检查更新结果

#### ER-预期结果：1：接口调用成功；2：到期时间更新成功；3：更新后数据正确；

### MYTL-流程作废后停止提醒冒烟验证

#### PD-前置条件：签署流程已设置到期提醒；

#### 步骤一：作废签署流程

#### 步骤二：等待原定的提醒时间

#### 步骤三：检查通知发送情况

#### ER-预期结果：1：流程作废成功；2：不再发送到期提醒；3：作废流程到期时间为空；

## 八、线上验证用例提取

### PATL-端到端合同到期提醒完整流程验证

#### PD-前置条件：生产环境；真实用户账号；

#### 步骤一：配置业务模板合同到期提醒规则

#### 步骤二：发起签署流程并设置到期日期

#### 步骤三：完成签署流程

#### 步骤四：等待到期提醒时间并接收通知

#### ER-预期结果：1：模板配置成功；2：流程发起成功；3：签署完成正常；4：按时收到到期提醒；

### PATL-批量发起合同到期设置线上验证

#### PD-前置条件：生产环境；具有批量发起权限；

#### 步骤一：准备包含到期日期的批量发起Excel

#### 步骤二：上传Excel文件并执行批量发起

#### 步骤三：检查生成的流程到期日期

#### ER-预期结果：1：Excel上传成功；2：批量发起成功；3：所有流程都有正确的到期日期；

### PATL-OpenAPI接口线上功能验证

#### PD-前置条件：生产环境API权限；真实业务数据；

#### 步骤一：调用创建签署流程API设置到期时间

#### 步骤二：调用签署详情API查看到期时间

#### 步骤三：调用更新流程API修改到期时间

#### ER-预期结果：1：API调用成功；2：到期时间设置正确；3：更新功能正常；

### PATL-消息通知渠道线上验证

#### PD-前置条件：生产环境；配置了多种通知渠道；

#### 步骤一：设置站内信、邮箱、短信通知

#### 步骤二：等待合同到期提醒触发

#### 步骤三：检查各通道通知接收情况

#### ER-预期结果：1：站内信正常接收；2：邮箱通知正常；3：短信通知正常；4：通知内容准确；

### PATL-特殊场景线上验证

#### PD-前置条件：生产环境；存在或签节点和转交场景；

#### 步骤一：创建包含或签节点的流程

#### 步骤二：完成部分签署使另一方无需签署

#### 步骤三：执行流程转交操作

#### 步骤四：等待到期提醒验证通知对象

#### ER-预期结果：1：或签逻辑正确；2：无需签署方不收到通知；3：转交后通知对象正确；

### PATL-系统性能线上验证

#### PD-前置条件：生产环境；存在大量到期流程；

#### 步骤一：监控定时任务执行时间

#### 步骤二：检查系统资源使用情况

#### 步骤三：验证通知发送效率

#### ER-预期结果：1：定时任务按时执行；2：系统资源使用正常；3：通知发送及时；4：不影响其他业务；
