# 合同到期日设置与到期提醒完整功能-测试用例

## 功能测试

### 业务模板配置

#### TL-业务模板合同到期自动提醒默认配置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，找到签署方式-通知设置

##### 步骤二：查看"合同到期自动提醒"配置项状态和默认值

##### 步骤三：检查各项默认参数设置

##### ER-预期结果：1：合同到期自动提醒默认开启；2：前N天开始提醒默认30天；3：每N天默认15天；4：提醒时间默认10:00；5：次数限制默认3次；

#### TL-提醒频率参数边界值设置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，点击合同到期自动提醒设置

##### 步骤二：设置前N天开始提醒为1、999、1000进行保存

##### 步骤三：设置每N天为1、999、1000进行保存

##### 步骤四：设置次数限制为1、99、100进行保存

##### ER-预期结果：1：前N天开始提醒1-999范围内可正常保存；2：每N天1-999范围内可正常保存；3：次数限制1-99范围内可正常保存；4：超出范围的值无法保存并提示错误；

#### TL-提醒时间格式设置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，点击合同到期自动提醒设置

##### 步骤二：设置提醒时间为0:00、12:30、23:59进行保存

##### 步骤三：尝试设置提醒时间为24:00、25:30等无效时间

##### ER-预期结果：1：有效时间格式0:00-23:59可正常保存；2：无效时间格式无法保存并提示错误；3：时间选择器限制在有效范围内；

#### TL-签署通知策略合同到期通知消息配置验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，找到签署通知策略设置

##### 步骤二：在合同到期通知消息中选择提醒对象：发起人、签署人、抄送人

##### 步骤三：选择提醒方式：站内信、邮箱、短信、第三方通道

##### 步骤四：保存配置

##### ER-预期结果：1：可多选提醒对象；2：提醒方式受消息模板通道控制显示；3：配置保存成功；4：短信默认关闭状态；

### 合同到期时间设置

#### TL-业务模板合同到期日期开关控制验证

##### PD-前置条件：用户已登录；具有业务模板管理权限；业务模板已创建；

##### 步骤一：进入业务模板编辑页面，找到签署流程配置

##### 步骤二：关闭"合同到期日期与到期提醒"开关

##### 步骤三：开启"合同到期日期与到期提醒"开关，点击设置

##### 步骤四：查看3种配置方式选项

##### ER-预期结果：1：开关关闭时默认无设置；2：开关开启时可点击设置；3：显示发起签署后固定时长、签署完成后固定时长、发起时指定三种方式；

#### TL-发起签署后固定时长配置验证

##### PD-前置条件：用户已登录；业务模板已开启合同到期设置；

##### 步骤一：选择"发起签署后固定时长"配置方式

##### 步骤二：设置时间单位为年、月、天

##### 步骤三：设置具体数值并保存

##### 步骤四：使用该模板发起签署流程

##### ER-预期结果：1：支持年、月、天三种时间单位；2：可设置具体数值；3：配置保存成功；4：发起时自动计算到期日期；

#### TL-签署完成后固定时长配置验证

##### PD-前置条件：用户已登录；业务模板已开启合同到期设置；

##### 步骤一：选择"签署完成后固定时长"配置方式

##### 步骤二：设置时间单位和数值

##### 步骤三：保存配置并使用该模板发起签署

##### 步骤四：完成签署流程，检查到期日期计算

##### ER-预期结果：1：配置保存成功；2：签署完成后自动计算到期日期；3：到期日期基于签署完成时间计算；

#### TL-发起时指定配置验证

##### PD-前置条件：用户已登录；业务模板已开启合同到期设置；

##### 步骤一：选择"发起时指定"配置方式并保存

##### 步骤二：使用该模板进行单份发起（PC）

##### 步骤三：使用该模板进行单份发起（H5）

##### 步骤四：使用该模板进行批量发起

##### ER-预期结果：1：单份发起PC页面显示合同到期日期设置项；2：单份发起H5页面显示合同到期日期设置项；3：批量发起Excel模板增加"合同到期日期"列；4：显示提示文案"系统将在合同到期前{total}天通知流程参与人"；

#### TL-提示文案动态显示验证

##### PD-前置条件：业务模板配置为发起时指定；设置了提醒规则；

##### 步骤一：进入发起页面选择该业务模板

##### 步骤二：查看提示文案显示

##### 步骤三：修改业务模板提醒规则中的"前N天开始提醒"参数

##### 步骤四：重新进入发起页面查看提示文案

##### ER-预期结果：1：提示文案正确显示；2：{total}天数值读取业务模板设置；3：修改模板后提示文案同步更新；

### 发起页面功能

#### TL-单份发起PC端合同到期日期设置验证

##### PD-前置条件：用户已登录；业务模板配置为发起时指定；

##### 步骤一：进入单份发起页面（PC端）

##### 步骤二：选择配置了合同到期的业务模板

##### 步骤三：设置合同到期日期为未来日期

##### 步骤四：设置合同到期日期为过去日期

##### 步骤五：完成发起流程

##### ER-预期结果：1：页面动态显示合同到期日期设置项；2：未来日期可正常设置；3：过去日期提示错误；4：显示提醒规则提示文案；5：发起成功；

#### TL-单份发起H5端合同到期日期设置验证

##### PD-前置条件：用户已登录；业务模板配置为发起时指定；

##### 步骤一：进入单份发起页面（H5端）

##### 步骤二：选择配置了合同到期的业务模板

##### 步骤三：点击合同到期日期设置

##### 步骤四：使用日期选择器选择日期

##### 步骤五：完成发起流程

##### ER-预期结果：1：H5页面正确显示合同到期日期设置；2：日期选择器功能正常；3：选择的日期正确显示；4：发起流程成功；

#### TL-批量发起合同到期日期设置验证

##### PD-前置条件：用户已登录；业务模板配置为发起时指定；

##### 步骤一：进入批量发起页面

##### 步骤二：选择配置了合同到期的业务模板

##### 步骤三：下载Excel模板

##### 步骤四：填写Excel模板包含合同到期日期列

##### 步骤五：上传Excel文件并发起

##### ER-预期结果：1：Excel模板包含"合同到期日期"列；2：可正确填写日期格式；3：上传解析成功；4：批量发起成功；5：每个流程都有对应的到期日期；

### OpenAPI接口

#### TL-一步发起签署接口增加合同到期时间参数验证

##### PD-前置条件：具有API调用权限；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/createAndStart接口

##### 步骤二：业务模板配置为"发起时指定"，传入contractExpirationDate参数

##### 步骤三：业务模板配置为"发起签署后固定时长"，传入contractExpirationDate参数

##### 步骤四：业务模板配置为"无需设置"，传入contractExpirationDate参数

##### ER-预期结果：1：发起时指定模式参数生效；2：固定时长模式传值不生效，按模板规则生成；3：无需设置模式传值不生效；4：时间格式"yyyy-MM-dd hh:mm:ss"验证正确；

#### TL-使用业务模板发起签署接口合同到期时间验证

##### PD-前置条件：具有API调用权限；业务模板已配置；

##### 步骤一：调用/esign-signs/v1/signFlow/createByBizTemplate接口

##### 步骤二：传入有效的contractExpirationDate参数

##### 步骤三：传入无效格式的contractExpirationDate参数

##### 步骤四：检查与业务模板配置的交互

##### ER-预期结果：1：有效参数创建成功；2：无效参数返回格式错误；3：参数与模板规则交互正确；4：创建的流程包含正确的到期时间；

#### TL-创建签署流程接口合同到期时间验证

##### PD-前置条件：具有API调用权限；接口服务正常；

##### 步骤一：调用/esign-signs/v1/signFlow/create接口

##### 步骤二：传入contractExpirationDate参数

##### 步骤三：验证参数格式和有效性

##### 步骤四：检查创建结果

##### ER-预期结果：1：接口接受contractExpirationDate参数；2：参数验证规则正确；3：创建的流程包含到期时间；4：返回结果正确；

#### TL-签署详情接口返回合同到期时间验证

##### PD-前置条件：签署流程已创建；设置了合同到期时间；

##### 步骤一：调用/esign-signs/v1/signFlow/signDetail接口

##### 步骤二：传入有效的流程ID

##### 步骤三：检查返回结果中的contractExpirationDate字段

##### ER-预期结果：1：返回结果包含contractExpirationDate字段；2：字段值与设置的到期时间一致；3：时间格式正确；

#### TL-获取签署流程列表接口合同到期时间查询验证

##### PD-前置条件：存在多个签署流程；部分设置了合同到期时间；

##### 步骤一：调用/esign-signs/v1/signFlow/list接口

##### 步骤二：传入contractExpirationDateStartTime和contractExpirationDateEndTime参数

##### 步骤三：验证时间区间查询功能

##### 步骤四：检查返回结果中的contractExpirationDate字段

##### ER-预期结果：1：支持按到期时间区间查询；2：查询结果准确；3：返回结果包含contractExpirationDate字段；4：分页功能正常；

#### TL-更新签署流程接口修改合同到期日期验证

##### PD-前置条件：签署流程已存在；具有API调用权限；

##### 步骤一：调用/esign-docs/v1/signFlow/update接口

##### 步骤二：传入有效的signFlowId和未来的contractExpirationTime

##### 步骤三：传入有效的businessNo和过去的contractExpirationTime

##### 步骤四：传入无效的流程ID

##### ER-预期结果：1：有效ID和未来时间更新成功；2：过去时间提示"合同到期日期应大于当前时间"；3：无效ID提示流程不存在；4：更新后的流程包含新的到期时间；

#### TL-更新签署流程接口状态限制验证

##### PD-前置条件：存在不同状态的签署流程；

##### 步骤一：对"填写中"状态流程调用更新接口

##### 步骤二：对"填写完成"状态流程调用更新接口

##### 步骤三：对"签署中"状态流程调用更新接口

##### 步骤四：对"已完成"状态流程调用更新接口

##### ER-预期结果：1：填写中、填写完成、审批中、签署中状态可更新；2：已完成状态不可更新；3：返回相应的状态提示；

### 页面显示功能

#### TL-签署页面合同到期时间展示验证

##### PD-前置条件：签署流程已完成；设置了合同到期时间；

##### 步骤一：进入签署页面（PC端）

##### 步骤二：进入签署页面（H5端）

##### 步骤三：检查合同到期时间显示

##### ER-预期结果：1：PC端签署页面正确展示合同到期时间；2：H5端签署页面正确展示合同到期时间；3：时间格式显示正确；

#### TL-签署预览页面合同到期时间展示验证

##### PD-前置条件：签署流程已完成；设置了合同到期时间；

##### 步骤一：进入签署预览页面（PC端）

##### 步骤二：进入签署预览页面（H5端）

##### 步骤三：检查合同到期时间显示

##### ER-预期结果：1：PC端预览页面正确展示合同到期时间；2：H5端预览页面正确展示合同到期时间；3：时间格式显示正确；

#### TL-电子签署列表签署详情页面展示验证

##### PD-前置条件：签署流程已完成；设置了合同到期时间；

##### 步骤一：进入电子签署列表页面（PC端）

##### 步骤二：点击进入签署详情页面

##### 步骤三：进入电子签署列表页面（H5端）

##### 步骤四：点击进入签署详情页面

##### ER-预期结果：1：PC端详情页面正确展示合同到期时间；2：H5端详情页面正确展示合同到期时间；3：时间格式显示正确；

#### TL-已签文件列表合同到期时间查询验证

##### PD-前置条件：存在多个已签文件；部分设置了合同到期时间；

##### 步骤一：进入已签文件列表页面

##### 步骤二：使用合同到期时间进行查询

##### 步骤三：设置时间范围进行筛选

##### 步骤四：检查查询结果

##### ER-预期结果：1：支持按合同到期时间查询；2：时间范围筛选功能正常；3：查询结果准确；4：列表页增加合同到期时间字段；

#### TL-已签文件列表导出功能验证

##### PD-前置条件：已签文件列表包含设置了到期时间的文件；

##### 步骤一：进入已签文件列表页面

##### 步骤二：选择需要导出的文件

##### 步骤三：执行导出操作

##### 步骤四：检查导出文件内容

##### ER-预期结果：1：导出功能正常；2：导出文件增加合同到期时间字段；3：到期时间数据正确；4：文件格式正确；

#### TL-已签文件详情页面展示验证

##### PD-前置条件：已签文件设置了合同到期时间；

##### 步骤一：进入已签文件列表页面

##### 步骤二：点击进入文件详情页面

##### 步骤三：检查合同到期时间显示

##### ER-预期结果：1：详情页面正确展示合同到期时间；2：时间格式显示正确；3：页面布局合理；

## 异常测试

### 特殊场景处理

#### TL-或签节点一方已签署另一方无需签署通知处理验证

##### PD-前置条件：设置了或签节点；一方已完成签署；配置了到期提醒；

##### 步骤一：完成或签节点中一方的签署

##### 步骤二：等待到期提醒时间

##### 步骤三：检查通知发送对象

##### ER-预期结果：1：无需签署的签署人不接收通知；2：其他相关人员正常接收通知；3：通知内容正确；

#### TL-转交已签流程发起人通知对象变更验证

##### PD-前置条件：签署流程已完成；发起人已转交；配置了到期提醒；

##### 步骤一：完成流程转交

##### 步骤二：等待到期提醒时间

##### 步骤三：检查通知发送对象

##### ER-预期结果：1：通知发送给转交后的发起人；2：原发起人不再接收通知；3：其他通知对象不受影响；

#### TL-流程全部作废后不再通知验证

##### PD-前置条件：签署流程已设置到期提醒；

##### 步骤一：作废签署流程

##### 步骤二：等待原定的提醒时间

##### 步骤三：检查通知发送情况

##### ER-预期结果：1：作废流程不再发送到期提醒；2：定时任务不处理作废流程；3：系统日志记录正确；

### 系统异常

#### TL-接口参数格式异常处理验证

##### PD-前置条件：具有API调用权限；

##### 步骤一：传入错误格式的contractExpirationDate参数

##### 步骤二：传入空值的contractExpirationDate参数

##### 步骤三：传入超长字符串的contractExpirationDate参数

##### ER-预期结果：1：错误格式返回格式错误提示；2：空值按业务规则处理；3：超长字符串返回参数错误；4：系统不崩溃；

#### TL-页面显示异常处理验证

##### PD-前置条件：存在合同到期时间为空的流程；

##### 步骤一：进入签署页面查看到期时间显示

##### 步骤二：进入列表页面查看到期时间字段

##### 步骤三：执行导出操作

##### ER-预期结果：1：到期时间为空时显示为空或默认提示；2：列表页面正常显示；3：导出文件包含空值处理；4：页面不报错；

## 性能测试

### 并发性能

#### TL-大量流程同时到期提醒性能验证

##### PD-前置条件：存在大量已完成的签署流程；同一天需要发送到期提醒；

##### 步骤一：准备1000个流程在同一天到期

##### 步骤二：定时任务执行到期提醒

##### 步骤三：监控系统性能指标

##### ER-预期结果：1：定时任务在合理时间内完成；2：系统资源使用正常；3：所有提醒正常发送；4：不影响其他业务功能；

#### TL-批量发起包含到期日期性能验证

##### PD-前置条件：准备包含1000条记录的批量发起Excel；每条都设置到期日期；

##### 步骤一：上传批量发起Excel文件

##### 步骤二：执行批量发起操作

##### 步骤三：监控处理时间和系统性能

##### ER-预期结果：1：批量处理在合理时间内完成；2：所有流程都正确设置到期日期；3：系统响应正常；4：内存使用合理；

#### TL-已签文件列表大数据量查询性能验证

##### PD-前置条件：已签文件列表包含大量数据；部分设置了到期时间；

##### 步骤一：在大数据量情况下按到期时间查询

##### 步骤二：执行导出操作

##### 步骤三：监控查询和导出性能

##### ER-预期结果：1：查询响应时间合理；2：导出功能正常；3：系统资源使用正常；4：用户体验良好；

## 兼容性测试

### 浏览器兼容性

#### TL-不同浏览器合同到期日期设置兼容性验证

##### PD-前置条件：准备Chrome、Firefox、Safari、Edge浏览器；

##### 步骤一：在Chrome浏览器中设置合同到期日期

##### 步骤二：在Firefox浏览器中设置合同到期日期

##### 步骤三：在Safari浏览器中设置合同到期日期

##### 步骤四：在Edge浏览器中设置合同到期日期

##### ER-预期结果：1：所有浏览器都能正常显示日期选择器；2：日期设置功能正常；3：页面布局正确；4：交互体验一致；

### 移动端兼容性

#### TL-移动端H5合同到期日期设置兼容性验证

##### PD-前置条件：准备iOS和Android设备；

##### 步骤一：在iOS设备上访问H5发起页面

##### 步骤二：设置合同到期日期

##### 步骤三：在Android设备上访问H5发起页面

##### 步骤四：设置合同到期日期

##### ER-预期结果：1：iOS设备日期选择器正常；2：Android设备日期选择器正常；3：触摸交互体验良好；4：页面适配正确；

#### TL-移动端页面显示兼容性验证

##### PD-前置条件：准备iOS和Android设备；存在设置了到期时间的流程；

##### 步骤一：在iOS设备上查看签署页面到期时间显示

##### 步骤二：在Android设备上查看签署页面到期时间显示

##### 步骤三：检查列表页面到期时间字段显示

##### ER-预期结果：1：iOS设备显示正常；2：Android设备显示正常；3：时间格式正确；4：页面布局合理；

## 安全测试

### 权限控制

#### TL-无权限用户访问业务模板配置验证

##### PD-前置条件：用户无业务模板管理权限；

##### 步骤一：尝试访问业务模板编辑页面

##### 步骤二：尝试修改合同到期提醒配置

##### 步骤三：检查系统权限控制

##### ER-预期结果：1：无权限用户无法访问配置页面；2：系统返回权限错误提示；3：系统记录访问日志；

#### TL-API接口权限控制验证

##### PD-前置条件：用户无API调用权限；

##### 步骤一：尝试调用更新签署流程接口

##### 步骤二：尝试调用创建签署流程接口

##### 步骤三：检查权限控制效果

##### ER-预期结果：1：无权限用户无法调用接口；2：返回权限错误提示；3：系统记录权限违规日志；

### 数据安全

#### TL-合同到期时间数据传输安全验证

##### PD-前置条件：配置了HTTPS环境；

##### 步骤一：通过API接口传输合同到期时间数据

##### 步骤二：检查数据传输过程

##### 步骤三：验证数据完整性

##### ER-预期结果：1：数据传输使用HTTPS加密；2：数据完整性得到保障；3：敏感信息不泄露；

## 五、测试用例优化

**优化维度检查：**
1. 完整性检查：覆盖了业务模板配置、合同到期时间设置、OpenAPI接口、页面显示等所有功能点
2. 准确性检查：步骤描述清晰，期望结果明确，测试数据准确
3. 可执行性检查：前置条件明确，步骤可操作，环境要求清晰
4. 可维护性检查：用例独立性好，步骤清晰，数据可重用

**补充遗漏场景：**

### 数据处理

#### TL-时区处理合同到期日期验证

##### PD-前置条件：用户在不同时区；设置了合同到期日期；

##### 步骤一：在东八区设置合同到期日期为2024-12-31

##### 步骤二：切换到其他时区查看到期日期

##### 步骤三：检查提醒发送时间

##### ER-预期结果：1：到期日期显示正确；2：提醒按服务器时区发送；3：时区转换准确；

#### TL-历史数据兼容性验证

##### PD-前置条件：存在升级前的历史签署流程；

##### 步骤一：查询历史流程的到期日期字段

##### 步骤二：为历史流程设置到期日期

##### 步骤三：检查历史流程的显示功能

##### ER-预期结果：1：历史流程到期日期字段为空；2：可为历史流程设置到期日期；3：设置后显示功能正常；

## 六、测试用例评审及补充

**评审结果：**
1. 需求覆盖度：95%
2. 场景完整性：92%
3. 步骤合理性：95%
4. 结果可验证性：95%
5. 数据充分性：90%

**遗漏场景：无重大遗漏，已补充时区处理和历史数据兼容性场景**

## 七、冒烟测试用例提取

### MYTL-业务模板合同到期配置冒烟验证

#### PD-前置条件：用户已登录；具有业务模板管理权限；

#### 步骤一：进入业务模板编辑页面

#### 步骤二：开启合同到期日期与到期提醒开关

#### 步骤三：配置基本提醒参数并保存

#### ER-预期结果：1：开关正常开启；2：默认参数正确；3：配置保存成功；

### MYTL-发起时指定合同到期日期冒烟验证

#### PD-前置条件：业务模板配置为发起时指定；用户已登录；

#### 步骤一：进入单份发起页面

#### 步骤二：选择配置了合同到期的业务模板

#### 步骤三：设置合同到期日期为未来日期

#### 步骤四：完成发起流程

#### ER-预期结果：1：页面显示到期日期设置项；2：日期设置成功；3：流程发起成功；4：流程包含到期日期；

### MYTL-OpenAPI接口基本功能冒烟验证

#### PD-前置条件：具有API调用权限；接口服务正常；

#### 步骤一：调用创建签署流程接口

#### 步骤二：传入contractExpirationDate参数

#### 步骤三：调用签署详情接口查看结果

#### ER-预期结果：1：接口调用成功；2：参数接受正确；3：详情接口返回到期时间；

### MYTL-页面显示功能冒烟验证

#### PD-前置条件：签署流程已完成；设置了合同到期时间；

#### 步骤一：进入签署页面查看到期时间显示

#### 步骤二：进入已签文件列表查看到期时间字段

#### 步骤三：执行按到期时间查询

#### ER-预期结果：1：签署页面正确显示到期时间；2：列表页面显示到期时间字段；3：查询功能正常；

### MYTL-更新签署流程接口冒烟验证

#### PD-前置条件：签署流程已存在；具有API调用权限；

#### 步骤一：调用更新签署流程接口

#### 步骤二：传入有效的contractExpirationTime

#### 步骤三：检查更新结果

#### ER-预期结果：1：接口调用成功；2：到期时间更新成功；3：更新后数据正确；

### MYTL-合同到期提醒发送冒烟验证

#### PD-前置条件：签署流程已完成；配置了提醒规则；

#### 步骤一：等待到期提醒时间

#### 步骤二：检查提醒发送情况

#### 步骤三：验证提醒内容

#### ER-预期结果：1：按时发送提醒；2：提醒对象正确；3：提醒内容准确；

## 八、线上验证用例提取

### PATL-端到端合同到期完整流程线上验证

#### PD-前置条件：生产环境；真实用户账号；

#### 步骤一：配置业务模板合同到期规则

#### 步骤二：发起签署流程并设置到期日期

#### 步骤三：完成签署流程

#### 步骤四：等待到期提醒时间并接收通知

#### ER-预期结果：1：模板配置成功；2：流程发起成功；3：签署完成正常；4：按时收到到期提醒；

### PATL-批量发起合同到期设置线上验证

#### PD-前置条件：生产环境；具有批量发起权限；

#### 步骤一：准备包含到期日期的批量发起Excel

#### 步骤二：上传Excel文件并执行批量发起

#### 步骤三：检查生成的流程到期日期

#### ER-预期结果：1：Excel上传成功；2：批量发起成功；3：所有流程都有正确的到期日期；

### PATL-OpenAPI接口线上功能验证

#### PD-前置条件：生产环境API权限；真实业务数据；

#### 步骤一：调用创建签署流程API设置到期时间

#### 步骤二：调用签署详情API查看到期时间

#### 步骤三：调用更新流程API修改到期时间

#### ER-预期结果：1：API调用成功；2：到期时间设置正确；3：更新功能正常；

### PATL-页面显示功能线上验证

#### PD-前置条件：生产环境；存在设置了到期时间的流程；

#### 步骤一：在PC端查看签署页面到期时间显示

#### 步骤二：在H5端查看签署页面到期时间显示

#### 步骤三：在已签文件列表查看到期时间字段

#### 步骤四：执行按到期时间查询和导出

#### ER-预期结果：1：PC端显示正常；2：H5端显示正常；3：列表字段显示正确；4：查询导出功能正常；

### PATL-特殊场景线上验证

#### PD-前置条件：生产环境；存在或签节点和转交场景；

#### 步骤一：创建包含或签节点的流程

#### 步骤二：完成部分签署使另一方无需签署

#### 步骤三：执行流程转交操作

#### 步骤四：等待到期提醒验证通知对象

#### ER-预期结果：1：或签逻辑正确；2：无需签署方不收到通知；3：转交后通知对象正确；

### PATL-系统性能线上验证

#### PD-前置条件：生产环境；存在大量到期流程；

#### 步骤一：监控定时任务执行时间

#### 步骤二：检查系统资源使用情况

#### 步骤三：验证页面查询和导出性能

#### ER-预期结果：1：定时任务按时执行；2：系统资源使用正常；3：页面响应及时；4：不影响其他业务；
