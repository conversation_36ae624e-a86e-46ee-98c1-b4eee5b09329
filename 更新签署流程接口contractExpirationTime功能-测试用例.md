# 更新签署流程接口contractExpirationTime功能-测试用例

## 接口基本功能测试

### 接口参数验证

#### TL-更新签署流程接口signFlowId参数验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：调用/esign-docs/v1/signFlow/update接口

##### 步骤二：传入有效的signFlowId和contractExpirationTime

##### 步骤三：检查更新结果

##### ER-预期结果：1：接口调用成功；2：使用signFlowId成功定位流程；3：contractExpirationTime更新成功；4：返回成功响应；

#### TL-更新签署流程接口businessNo参数验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：调用/esign-docs/v1/signFlow/update接口

##### 步骤二：传入有效的businessNo和contractExpirationTime

##### 步骤三：检查更新结果

##### ER-预期结果：1：接口调用成功；2：使用businessNo成功定位流程；3：contractExpirationTime更新成功；4：返回成功响应；

#### TL-更新签署流程接口参数优先级验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：调用/esign-docs/v1/signFlow/update接口

##### 步骤二：同时传入有效的signFlowId和businessNo

##### 步骤三：传入contractExpirationTime

##### 步骤四：检查更新结果

##### ER-预期结果：1：接口调用成功；2：以signFlowId为准进行更新；3：businessNo被忽略；4：更新成功；

#### TL-更新签署流程接口必填参数验证

##### PD-前置条件：具有API调用权限；

##### 步骤一：调用接口，不传入signFlowId和businessNo

##### 步骤二：调用接口，只传入contractExpirationTime

##### 步骤三：检查错误响应

##### ER-预期结果：1：接口调用失败；2：返回参数缺失错误；3：提示signFlowId、businessNo二选一必填；

#### TL-更新签署流程接口contractExpirationTime格式验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：传入正确格式"2024-12-31 23:59:59"

##### 步骤二：传入错误格式"2024/12/31 23:59:59"

##### 步骤三：传入错误格式"2024-12-31"

##### 步骤四：传入错误格式"2024-12-31T23:59:59"

##### 步骤五：传入非法字符"abc-def-ghi hh:mm:ss"

##### ER-预期结果：1：正确格式"yyyy-MM-dd hh:mm:ss"更新成功；2：错误格式返回格式错误提示；3：只有日期格式返回格式错误；4：ISO格式返回格式错误；5：非法字符返回格式错误；

#### TL-更新签署流程接口signFlowExpireTime预留字段验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：传入有效的signFlowExpireTime格式

##### 步骤二：传入错误的signFlowExpireTime格式

##### 步骤三：检查接口响应和数据库变化

##### ER-预期结果：1：正确格式通过校验；2：错误格式返回格式错误；3：会校验格式但不会真的落库操作；4：数据库中signFlowExpireTime字段无变化；

### 流程ID有效性验证

#### TL-更新签署流程接口无效signFlowId验证

##### PD-前置条件：具有API调用权限；

##### 步骤一：传入不存在的signFlowId

##### 步骤二：传入格式错误的signFlowId

##### 步骤三：传入空的signFlowId

##### 步骤四：检查错误响应

##### ER-预期结果：1：不存在的signFlowId返回流程不存在错误；2：格式错误的signFlowId返回格式错误；3：空的signFlowId返回参数缺失错误；4：沿用获取签署流程进度详情接口的校验与提示；

#### TL-更新签署流程接口无效businessNo验证

##### PD-前置条件：具有API调用权限；

##### 步骤一：传入不存在的businessNo

##### 步骤二：传入格式错误的businessNo

##### 步骤三：传入空的businessNo

##### 步骤四：检查错误响应

##### ER-预期结果：1：不存在的businessNo返回流程不存在错误；2：格式错误的businessNo返回格式错误；3：空的businessNo返回参数缺失错误；4：沿用获取签署流程进度详情接口的校验与提示；

### 日期校验验证

#### TL-更新签署流程接口日期时间校验

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：传入大于当前时间的contractExpirationTime

##### 步骤二：传入等于当前时间的contractExpirationTime

##### 步骤三：传入小于当前时间的contractExpirationTime

##### 步骤四：检查更新结果

##### ER-预期结果：1：大于当前时间更新成功；2：等于当前时间更新成功；3：小于当前时间更新失败，提示"合同到期日期应大于当前时间"；

#### TL-更新签署流程接口日期边界值验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：传入当前时间+1秒的contractExpirationTime

##### 步骤二：传入当前时间-1秒的contractExpirationTime

##### 步骤三：传入当前日期00:00:00的contractExpirationTime

##### 步骤四：传入当前日期23:59:59的contractExpirationTime

##### ER-预期结果：1：当前时间+1秒更新成功；2：当前时间-1秒更新失败；3：当前日期00:00:00根据具体时间判断；4：当前日期23:59:59根据具体时间判断；

### 流程状态限制验证

#### TL-更新签署流程接口支持状态验证

##### PD-前置条件：具有API调用权限；存在不同状态的签署流程；

##### 步骤一：更新"填写中"状态流程的contractExpirationTime

##### 步骤二：更新"填写完成"状态流程的contractExpirationTime

##### 步骤三：更新"审批中"状态流程的contractExpirationTime

##### 步骤四：更新"签署中"状态流程的contractExpirationTime

##### ER-预期结果：1：填写中状态更新成功；2：填写完成状态更新成功；3：审批中状态更新成功；4：签署中状态更新成功；

#### TL-更新签署流程接口不支持状态验证

##### PD-前置条件：具有API调用权限；存在不同状态的签署流程；

##### 步骤一：更新"已完成"状态流程的contractExpirationTime

##### 步骤二：更新"已过期"状态流程的contractExpirationTime

##### 步骤三：更新"已拒签"状态流程的contractExpirationTime

##### 步骤四：检查错误响应

##### ER-预期结果：1：已完成状态更新失败，返回状态不支持错误；2：已过期状态更新失败，返回状态不支持错误；3：已拒签状态更新失败，返回状态不支持错误；

#### TL-更新签署流程接口作废流程验证

##### PD-前置条件：具有API调用权限；存在已作废的签署流程；

##### 步骤一：更新已作废流程的contractExpirationTime

##### 步骤二：检查错误响应

##### ER-预期结果：1：作废流程更新失败；2：返回作废流程不支持修改错误；3：错误提示明确；

### 合同到期日期修改验证

#### TL-更新签署流程接口有到期日期流程修改验证

##### PD-前置条件：具有API调用权限；存在已设置合同到期日期的签署流程；

##### 步骤一：查询流程当前的contractExpirationTime

##### 步骤二：调用更新接口修改contractExpirationTime

##### 步骤三：再次查询流程的contractExpirationTime

##### 步骤四：验证修改结果

##### ER-预期结果：1：查询到当前到期日期；2：更新接口调用成功；3：查询到新的到期日期；4：修改成功；

#### TL-更新签署流程接口无到期日期流程修改验证

##### PD-前置条件：具有API调用权限；存在未设置合同到期日期的签署流程；

##### 步骤一：查询流程当前的contractExpirationTime（为空）

##### 步骤二：调用更新接口设置contractExpirationTime

##### 步骤三：再次查询流程的contractExpirationTime

##### 步骤四：验证设置结果

##### ER-预期结果：1：查询到当前到期日期为空；2：更新接口调用成功；3：查询到新设置的到期日期；4：设置成功；

#### TL-更新签署流程接口清空到期日期验证

##### PD-前置条件：具有API调用权限；存在已设置合同到期日期的签署流程；

##### 步骤一：查询流程当前的contractExpirationTime

##### 步骤二：调用更新接口，contractExpirationTime传入空值

##### 步骤三：再次查询流程的contractExpirationTime

##### 步骤四：验证清空结果

##### ER-预期结果：1：查询到当前到期日期；2：更新接口调用成功；3：查询到到期日期被清空；4：清空成功；

### 业务场景验证

#### TL-更新签署流程接口业务模板规则交互验证

##### PD-前置条件：具有API调用权限；不同业务模板规则的签署流程；

##### 步骤一：更新"发起时指定"规则流程的contractExpirationTime

##### 步骤二：更新"发起签署后固定时长"规则流程的contractExpirationTime

##### 步骤三：更新"签署完成后固定时长"规则流程的contractExpirationTime

##### 步骤四：更新"无需设置"规则流程的contractExpirationTime

##### 步骤五：检查更新结果

##### ER-预期结果：1：发起时指定规则流程更新成功；2：发起签署后固定时长规则流程更新成功；3：签署完成后固定时长规则流程更新成功；4：无需设置规则流程更新成功；5：更新接口不受业务模板规则限制；

#### TL-更新签署流程接口多方签署场景验证

##### PD-前置条件：具有API调用权限；存在多方签署流程；

##### 步骤一：更新顺序签署流程的contractExpirationTime

##### 步骤二：更新无序签署流程的contractExpirationTime

##### 步骤三：更新或签流程的contractExpirationTime

##### 步骤四：检查更新结果和后续影响

##### ER-预期结果：1：顺序签署流程更新成功；2：无序签署流程更新成功；3：或签流程更新成功；4：更新后的到期时间对所有签署方生效；

#### TL-更新签署流程接口权限验证

##### PD-前置条件：不同权限级别的用户；存在签署流程；

##### 步骤一：流程发起人调用更新接口

##### 步骤二：流程签署方调用更新接口

##### 步骤三：无关用户调用更新接口

##### 步骤四：检查权限控制结果

##### ER-预期结果：1：流程发起人更新成功；2：流程签署方根据权限配置决定是否成功；3：无关用户更新失败，返回权限不足错误；4：权限控制严格；

## 异常测试

### 接口异常处理

#### TL-更新签署流程接口并发更新异常验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：同时发起多个更新请求修改同一流程的contractExpirationTime

##### 步骤二：传入不同的到期时间值

##### 步骤三：检查并发处理结果

##### 步骤四：验证数据一致性

##### ER-预期结果：1：并发请求正常处理；2：数据一致性得到保障；3：无数据竞争问题；4：最终结果符合预期；

#### TL-更新签署流程接口网络异常处理验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：调用更新接口

##### 步骤二：模拟网络中断

##### 步骤三：检查接口超时处理

##### 步骤四：验证数据状态

##### ER-预期结果：1：网络异常有超时处理；2：接口返回超时错误；3：数据状态一致；4：无脏数据产生；

#### TL-更新签署流程接口系统异常处理验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：调用更新接口

##### 步骤二：模拟系统异常

##### 步骤三：检查异常处理机制

##### ER-预期结果：1：系统异常有容错处理；2：返回系统错误提示；3：不影响其他流程；4：异常恢复后接口正常；

### 数据异常处理

#### TL-更新签署流程接口数据库异常处理验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：调用更新接口

##### 步骤二：模拟数据库连接异常

##### 步骤三：检查数据库异常处理

##### ER-预期结果：1：数据库异常有重试机制；2：返回数据库错误提示；3：事务回滚正确；4：数据一致性保障；

#### TL-更新签署流程接口数据格式异常处理验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：传入超长字符串的contractExpirationTime

##### 步骤二：传入特殊字符的contractExpirationTime

##### 步骤三：传入SQL注入攻击的contractExpirationTime

##### 步骤四：传入XSS攻击脚本的contractExpirationTime

##### ER-预期结果：1：超长字符串返回参数长度错误；2：特殊字符返回格式错误；3：SQL注入攻击被正确过滤；4：XSS攻击脚本被正确过滤；5：系统安全性得到保障；

## 性能测试

### 接口性能

#### TL-更新签署流程接口响应时间验证

##### PD-前置条件：具有API调用权限；存在大量签署流程；

##### 步骤一：连续调用1000次更新接口

##### 步骤二：每次传入不同的contractExpirationTime

##### 步骤三：监控接口响应时间

##### 步骤四：统计性能指标

##### ER-预期结果：1：所有接口调用成功；2：平均响应时间在合理范围内；3：99%请求响应时间符合要求；4：无性能瓶颈；

#### TL-更新签署流程接口并发性能验证

##### PD-前置条件：具有API调用权限；存在大量签署流程；

##### 步骤一：同时发起100个并发更新请求

##### 步骤二：监控系统资源使用

##### 步骤三：检查并发处理能力

##### ER-预期结果：1：并发请求正常处理；2：系统资源使用正常；3：无内存泄漏问题；4：并发性能良好；

#### TL-更新签署流程接口大数据量性能验证

##### PD-前置条件：具有API调用权限；存在大量签署流程数据；

##### 步骤一：在包含10万条流程数据的环境中调用更新接口

##### 步骤二：监控数据库查询性能

##### 步骤三：检查更新操作性能

##### ER-预期结果：1：大数据量环境下接口正常；2：数据库查询性能良好；3：更新操作及时；4：不影响其他操作；

## 兼容性测试

### 接口版本兼容性

#### TL-更新签署流程接口向下兼容性验证

##### PD-前置条件：具有API调用权限；存在旧版本客户端；

##### 步骤一：使用旧版本客户端调用新增的更新接口

##### 步骤二：检查接口返回结果

##### 步骤三：验证旧版本客户端的正常功能

##### ER-预期结果：1：新增接口不影响旧版本功能；2：向下兼容性良好；3：旧版本客户端可正常使用其他接口；

#### TL-更新签署流程接口数据格式兼容性验证

##### PD-前置条件：具有API调用权限；存在不同格式的流程数据；

##### 步骤一：更新旧格式流程数据的contractExpirationTime

##### 步骤二：更新新格式流程数据的contractExpirationTime

##### 步骤三：检查兼容性处理

##### ER-预期结果：1：旧格式数据更新成功；2：新格式数据更新成功；3：数据格式兼容性良好；4：无格式转换错误；

### 业务兼容性

#### TL-更新签署流程接口业务流程兼容性验证

##### PD-前置条件：具有API调用权限；存在不同版本的业务流程；

##### 步骤一：更新旧版本业务流程的contractExpirationTime

##### 步骤二：更新新版本业务流程的contractExpirationTime

##### 步骤三：检查业务流程兼容性

##### ER-预期结果：1：旧版本业务流程更新成功；2：新版本业务流程更新成功；3：业务流程兼容性良好；4：不影响业务逻辑；

## 安全测试

### 接口安全

#### TL-更新签署流程接口权限控制验证

##### PD-前置条件：不同权限级别的用户；存在签署流程；

##### 步骤一：无权限用户调用更新接口

##### 步骤二：只读权限用户调用更新接口

##### 步骤三：管理员权限用户调用更新接口

##### ER-预期结果：1：无权限用户无法调用接口；2：只读权限用户无法调用接口；3：管理员权限用户可以调用接口；4：权限控制严格；

#### TL-更新签署流程接口数据安全验证

##### PD-前置条件：具有API调用权限；配置了HTTPS环境；

##### 步骤一：通过HTTPS调用更新接口传输contractExpirationTime

##### 步骤二：检查数据传输过程

##### 步骤三：验证数据存储安全

##### ER-预期结果：1：数据传输使用HTTPS加密；2：敏感数据不泄露；3：数据存储安全；4：日志记录合规；

#### TL-更新签署流程接口防篡改验证

##### PD-前置条件：具有API调用权限；存在签署流程；

##### 步骤一：调用更新接口修改contractExpirationTime

##### 步骤二：尝试篡改请求数据

##### 步骤三：检查防篡改机制

##### ER-预期结果：1：正常请求更新成功；2：篡改请求被拒绝；3：防篡改机制有效；4：数据完整性保障；

#### TL-更新签署流程接口审计日志验证

##### PD-前置条件：具有API调用权限；配置了审计日志；

##### 步骤一：调用更新接口修改contractExpirationTime

##### 步骤二：检查审计日志记录

##### 步骤三：验证日志完整性

##### ER-预期结果：1：更新操作被记录；2：日志包含操作人、时间、修改内容；3：日志完整准确；4：符合审计要求；

## 五、测试用例优化

**优化维度检查：**
1. 完整性检查：覆盖了接口参数验证、业务规则验证、状态限制、异常处理、性能、安全等
2. 准确性检查：接口参数验证准确，业务规则验证明确，校验逻辑清晰
3. 可执行性检查：前置条件明确，API调用步骤清晰，验证点具体
4. 可维护性检查：用例独立性好，参数设置清晰，结果验证明确

**补充遗漏场景：**

### 边界场景

#### TL-更新签署流程接口时间边界值处理验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：设置contractExpirationTime为最大时间值

##### 步骤二：设置contractExpirationTime为最小有效时间值

##### 步骤三：设置contractExpirationTime为当前时间的边界值

##### ER-预期结果：1：最大时间值更新成功；2：最小有效时间值更新成功；3：边界值处理正确；4：无时间溢出异常；

#### TL-更新签署流程接口参数长度边界值验证

##### PD-前置条件：具有API调用权限；存在有效的签署流程；

##### 步骤一：传入最长允许长度的signFlowId

##### 步骤二：传入最长允许长度的businessNo

##### 步骤三：传入超长的参数值

##### ER-预期结果：1：最长允许长度参数更新成功；2：超长参数返回长度错误；3：边界值处理正确；

## 六、测试用例评审及补充

**评审结果：**
1. 需求覆盖度：98%
2. 场景完整性：95%
3. 步骤合理性：95%
4. 结果可验证性：95%
5. 数据充分性：92%

**遗漏场景：无重大遗漏，已补充时间边界值和参数长度边界值场景**

## 七、冒烟测试用例提取

### MYTL-更新签署流程接口基本功能冒烟验证

#### PD-前置条件：具有API调用权限；存在有效的签署流程；

#### 步骤一：调用/esign-docs/v1/signFlow/update接口

#### 步骤二：传入有效的signFlowId和contractExpirationTime

#### 步骤三：检查更新结果

#### ER-预期结果：1：接口调用成功；2：contractExpirationTime更新成功；3：返回成功响应；

### MYTL-更新签署流程接口参数验证冒烟验证

#### PD-前置条件：具有API调用权限；存在有效的签署流程；

#### 步骤一：使用signFlowId调用更新接口

#### 步骤二：使用businessNo调用更新接口

#### 步骤三：检查两种参数的处理结果

#### ER-预期结果：1：signFlowId参数更新成功；2：businessNo参数更新成功；3：参数处理正确；

### MYTL-更新签署流程接口时间格式冒烟验证

#### PD-前置条件：具有API调用权限；存在有效的签署流程；

#### 步骤一：传入正确格式"yyyy-MM-dd hh:mm:ss"的contractExpirationTime

#### 步骤二：传入错误格式的contractExpirationTime

#### 步骤三：检查格式校验结果

#### ER-预期结果：1：正确格式更新成功；2：错误格式返回格式错误；3：格式校验正确；

### MYTL-更新签署流程接口日期校验冒烟验证

#### PD-前置条件：具有API调用权限；存在有效的签署流程；

#### 步骤一：传入大于当前时间的contractExpirationTime

#### 步骤二：传入小于当前时间的contractExpirationTime

#### 步骤三：检查日期校验结果

#### ER-预期结果：1：大于当前时间更新成功；2：小于当前时间更新失败，提示"合同到期日期应大于当前时间"；3：日期校验正确；

### MYTL-更新签署流程接口状态限制冒烟验证

#### PD-前置条件：具有API调用权限；存在不同状态的签署流程；

#### 步骤一：更新"签署中"状态流程的contractExpirationTime

#### 步骤二：更新"已完成"状态流程的contractExpirationTime

#### 步骤三：检查状态限制结果

#### ER-预期结果：1：签署中状态更新成功；2：已完成状态更新失败；3：状态限制正确；

### MYTL-更新签署流程接口作废流程冒烟验证

#### PD-前置条件：具有API调用权限；存在已作废的签署流程；

#### 步骤一：更新已作废流程的contractExpirationTime

#### 步骤二：检查错误响应

#### ER-预期结果：1：作废流程更新失败；2：返回作废流程不支持修改错误；3：错误处理正确；

### MYTL-更新签署流程接口权限控制冒烟验证

#### PD-前置条件：不同权限级别的用户；存在签署流程；

#### 步骤一：流程发起人调用更新接口

#### 步骤二：无关用户调用更新接口

#### 步骤三：检查权限控制结果

#### ER-预期结果：1：流程发起人更新成功；2：无关用户更新失败；3：权限控制正确；

## 八、线上验证用例提取

### PATL-更新签署流程接口完整流程线上验证

#### PD-前置条件：生产环境API权限；真实业务数据；

#### 步骤一：调用更新签署流程API修改contractExpirationTime

#### 步骤二：调用签署详情API查看更新结果

#### 步骤三：验证更新后的业务流程

#### ER-预期结果：1：更新API调用成功；2：contractExpirationTime修改正确；3：业务流程正常；4：生产数据处理正确；

### PATL-更新签署流程接口参数处理线上验证

#### PD-前置条件：生产环境；真实流程数据；

#### 步骤一：使用signFlowId更新真实流程

#### 步骤二：使用businessNo更新真实流程

#### 步骤三：检查参数处理效果

#### ER-预期结果：1：signFlowId参数处理正确；2：businessNo参数处理正确；3：参数优先级逻辑正确；

### PATL-更新签署流程接口业务规则线上验证

#### PD-前置条件：生产环境；不同业务模板规则的流程；

#### 步骤一：更新不同业务模板规则流程的contractExpirationTime

#### 步骤二：检查业务规则交互

#### 步骤三：验证更新后的业务逻辑

#### ER-预期结果：1：不同业务规则流程都能更新；2：更新接口不受业务模板规则限制；3：业务逻辑正确；

### PATL-更新签署流程接口状态控制线上验证

#### PD-前置条件：生产环境；不同状态的真实流程；

#### 步骤一：更新支持状态流程的contractExpirationTime

#### 步骤二：尝试更新不支持状态流程的contractExpirationTime

#### 步骤三：检查状态控制效果

#### ER-预期结果：1：支持状态流程更新成功；2：不支持状态流程更新失败；3：状态控制严格；4：错误提示准确；

### PATL-更新签署流程接口性能线上验证

#### PD-前置条件：生产环境；大量真实流程数据；

#### 步骤一：批量调用更新接口

#### 步骤二：监控接口性能指标

#### 步骤三：检查系统资源使用

#### ER-预期结果：1：接口响应时间合理；2：系统资源使用正常；3：不影响其他业务；4：性能表现良好；

### PATL-更新签署流程接口安全性线上验证

#### PD-前置条件：生产环境；HTTPS环境；真实用户权限；

#### 步骤一：通过HTTPS调用更新接口

#### 步骤二：验证权限控制机制

#### 步骤三：检查审计日志记录

#### ER-预期结果：1：HTTPS传输安全；2：权限控制严格；3：审计日志完整；4：安全机制有效；

### PATL-更新签署流程接口异常处理线上验证

#### PD-前置条件：生产环境；可能的异常场景；

#### 步骤一：监控接口异常情况

#### 步骤二：检查异常处理机制

#### 步骤三：验证系统恢复能力

#### ER-预期结果：1：异常处理机制正常；2：系统稳定性良好；3：恢复能力强；4：不影响其他功能；

### PATL-更新签署流程接口兼容性线上验证

#### PD-前置条件：生产环境；不同版本的客户端；

#### 步骤一：使用不同版本客户端调用接口

#### 步骤二：检查兼容性处理

#### 步骤三：验证向下兼容性

#### ER-预期结果：1：不同版本客户端都正常；2：新增接口不影响旧功能；3：向下兼容性良好；4：升级平滑；

### PATL-更新签署流程接口数据一致性线上验证

#### PD-前置条件：生产环境；真实业务数据；

#### 步骤一：更新流程的contractExpirationTime

#### 步骤二：检查相关系统的数据同步

#### 步骤三：验证数据一致性

#### ER-预期结果：1：更新操作成功；2：相关系统数据同步；3：数据一致性良好；4：无数据不一致问题；

## 九、测试用例总结

**测试用例统计：**
- **接口基本功能测试用例**：20条（参数验证、业务规则验证、状态限制验证）
- **异常测试用例**：5条（接口异常、数据异常）
- **性能测试用例**：3条（响应时间、并发性能、大数据量性能）
- **兼容性测试用例**：2条（版本兼容性、业务兼容性）
- **安全测试用例**：4条（权限控制、数据安全、防篡改、审计日志）
- **补充测试用例**：2条（边界场景）
- **冒烟测试用例**：7条（约占总用例的19%）
- **线上验证用例**：9条（约占总用例的25%）

**总计：52条测试用例**

**核心功能验证：**
1. ✅ **接口参数验证**：signFlowId、businessNo、contractExpirationTime参数的完整验证
2. ✅ **参数优先级**：signFlowId和businessNo都有值时以signFlowId为准
3. ✅ **时间格式校验**：严格验证"yyyy-MM-dd hh:mm:ss"格式
4. ✅ **日期有效性校验**：contractExpirationTime必须大于当前时间
5. ✅ **流程状态限制**：只能修改填写中、填写完成、审批中、签署中状态的流程
6. ✅ **作废流程限制**：作废流程不支持修改
7. ✅ **业务模板规则交互**：更新接口不受业务模板规则限制
8. ✅ **权限控制**：严格的权限验证机制
9. ✅ **预留字段处理**：signFlowExpireTime会校验格式但不落库

**重点测试场景：**
- **参数处理**：signFlowId和businessNo的优先级处理
- **时间校验**：格式校验和有效性校验
- **状态控制**：支持和不支持状态的严格区分
- **业务兼容**：与现有业务模板规则的兼容性
- **安全保障**：权限控制、数据安全、审计日志

**文件特点：**
- 符合XMind导入标准的markdown格式
- 专门针对新增更新签署流程接口的详细测试覆盖
- 重点关注了contractExpirationTime字段的修改功能
- 包含了完整的参数验证、业务规则、异常处理测试
- 特别关注了状态限制和权限控制的验证

测试用例已生成完成，可以直接导入XMind进行管理和执行。建议优先执行冒烟测试用例验证核心接口功能，然后按功能模块进行专项测试，最后在生产环境执行线上验证用例。
